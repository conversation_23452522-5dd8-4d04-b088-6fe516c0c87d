import '../providers/app_state.dart';
import '../services/plant_data_service.dart';
import 'world_plant_encyclopedia.dart';

/// Enhanced Plant Dataset using World-Class Encyclopedia and Daily Updates
class PlantDataset {
  static final PlantDataService _plantDataService = PlantDataService();

  /// Get all plants from the world-class encyclopedia and daily additions
  static List<Plant> getAllPlants() {
    // Get plants from both encyclopedia and daily service
    final encyclopediaPlants = WorldPlantEncyclopedia.getAllPlants();
    final dailyPlants = _plantDataService.getAllPlants();

    // Combine and deduplicate
    final allPlants = <String, Plant>{};

    // Add encyclopedia plants
    for (final plant in encyclopediaPlants) {
      allPlants[plant.id] = plant;
    }

    // Add daily plants (will override if same ID)
    for (final plant in dailyPlants) {
      allPlants[plant.id] = plant;
    }

    return allPlants.values.toList();
  }

  /// Get all plant categories from the world encyclopedia
  static List<String> getAllCategories() {
    return WorldPlantEncyclopedia.getAllCategories();
  }

  /// Get all healing benefits from the world encyclopedia
  static List<String> getAllBenefits() {
    return WorldPlantEncyclopedia.getAllBenefits();
  }

  /// Get plants by category
  static List<Plant> getPlantsByCategory(String category) {
    if (category == 'All') return getAllPlants();
    return getAllPlants().where((plant) => plant.category == category).toList();
  }

  /// Get plants by benefit
  static List<Plant> getPlantsByBenefit(String benefit) {
    if (benefit == 'All') return getAllPlants();
    return getAllPlants().where((plant) => plant.benefits.contains(benefit)).toList();
  }

  /// Search plants with enhanced text search
  static List<Plant> searchPlants(String query) {
    return advancedSearch(query: query);
  }

  /// Get plant by ID
  static Plant? getPlantById(String id) {
    try {
      return getAllPlants().firstWhere((plant) => plant.id == id);
    } catch (e) {
      return null;
    }
  }

  /// Get featured plants (highest rated)
  static List<Plant> getFeaturedPlants() {
    final plants = getAllPlants();
    plants.sort((a, b) => b.rating.compareTo(a.rating));
    return plants.take(6).toList();
  }

  /// Get popular plants (most reviewed)
  static List<Plant> getPopularPlants() {
    final plants = getAllPlants();
    plants.sort((a, b) => b.reviewCount.compareTo(a.reviewCount));
    return plants.take(8).toList();
  }

  /// Get plants by origin/region
  static List<Plant> getPlantsByOrigin(String origin) {
    if (origin == 'All Regions') return getAllPlants();
    return getAllPlants().where((plant) =>
        plant.origin.toLowerCase().contains(origin.toLowerCase())
    ).toList();
  }

  /// Get plants by traditional medicine system
  static List<Plant> getPlantsByMedicineSystem(String system) {
    if (system == 'All Systems') return getAllPlants();

    final systemKeywords = {
      'Ayurveda': ['ayur', 'india', 'ayurvedic'],
      'Traditional Chinese Medicine': ['tcm', 'china', 'chinese'],
      'European Herbalism': ['eur', 'europe', 'european'],
      'Native American': ['nam', 'north america', 'native'],
      'Amazonian Shamanism': ['amz', 'amazon', 'peru', 'brazil'],
      'African Traditional': ['afr', 'africa', 'african'],
      'Aboriginal Medicine': ['aus', 'australia', 'aboriginal'],
    };

    final keywords = systemKeywords[system] ?? [];
    return getAllPlants().where((plant) =>
        keywords.any((keyword) =>
            plant.id.toLowerCase().contains(keyword) ||
            plant.origin.toLowerCase().contains(keyword) ||
            plant.traditionalUse.toLowerCase().contains(keyword)
        )
    ).toList();
  }



  /// Advanced search with multiple filters
  static List<Plant> advancedSearch({
    String? query,
    String? category,
    String? benefit,
    String? origin,
    String? medicineSystem,
    double? minRating,
    bool? isEndangered,
    String? activeCompound,
    String? family,
    String? partUsed,
  }) {
    List<Plant> results = getAllPlants();

    // Text search
    if (query != null && query.isNotEmpty) {
      final lowercaseQuery = query.toLowerCase();
      results = results.where((plant) =>
          plant.name.toLowerCase().contains(lowercaseQuery) ||
          plant.scientificName.toLowerCase().contains(lowercaseQuery) ||
          plant.description.toLowerCase().contains(lowercaseQuery) ||
          plant.commonNames.any((name) => name.toLowerCase().contains(lowercaseQuery)) ||
          plant.benefits.any((benefit) => benefit.toLowerCase().contains(lowercaseQuery)) ||
          plant.uses.any((use) => use.toLowerCase().contains(lowercaseQuery)) ||
          plant.traditionalUse.toLowerCase().contains(lowercaseQuery)
      ).toList();
    }

    // Category filter
    if (category != null && category != 'All') {
      results = results.where((plant) => plant.category == category).toList();
    }

    // Benefit filter
    if (benefit != null && benefit != 'All') {
      results = results.where((plant) => plant.benefits.contains(benefit)).toList();
    }

    // Origin filter
    if (origin != null && origin != 'All Regions') {
      results = results.where((plant) =>
          plant.origin.toLowerCase().contains(origin.toLowerCase())).toList();
    }

    // Medicine system filter (based on origin and traditional use)
    if (medicineSystem != null && medicineSystem != 'All Systems') {
      results = results.where((plant) {
        final traditionalUse = plant.traditionalUse.toLowerCase();
        switch (medicineSystem) {
          case 'Ayurveda':
            return traditionalUse.contains('ayurveda') ||
                   plant.origin.toLowerCase().contains('india');
          case 'Traditional Chinese Medicine':
            return traditionalUse.contains('chinese') ||
                   plant.origin.toLowerCase().contains('china');
          case 'European Herbalism':
            return plant.origin.toLowerCase().contains('europe');
          case 'Native American':
            return traditionalUse.contains('native american') ||
                   traditionalUse.contains('first nations');
          case 'Amazonian Shamanism':
            return plant.origin.toLowerCase().contains('amazon');
          case 'African Traditional':
            return plant.origin.toLowerCase().contains('africa');
          case 'Aboriginal Medicine':
            return plant.origin.toLowerCase().contains('australia');
          case 'Persian Medicine':
            return traditionalUse.contains('persian') ||
                   plant.origin.toLowerCase().contains('iran');
          case 'Arctic Traditional Medicine':
            return plant.origin.toLowerCase().contains('arctic') ||
                   traditionalUse.contains('inuit');
          default:
            return true;
        }
      }).toList();
    }

    // Rating filter
    if (minRating != null) {
      results = results.where((plant) => plant.rating >= minRating).toList();
    }

    // Endangered status filter
    if (isEndangered != null) {
      results = results.where((plant) =>
          plant.isEndangered == isEndangered).toList();
    }

    // Active compound filter
    if (activeCompound != null && activeCompound.isNotEmpty) {
      results = results.where((plant) =>
          plant.activeCompounds.any((compound) =>
              compound.toLowerCase().contains(activeCompound.toLowerCase()))).toList();
    }

    // Plant family filter
    if (family != null && family.isNotEmpty) {
      results = results.where((plant) =>
          plant.family.toLowerCase().contains(family.toLowerCase())).toList();
    }

    // Part used filter
    if (partUsed != null && partUsed.isNotEmpty) {
      results = results.where((plant) =>
          plant.partUsed.toLowerCase().contains(partUsed.toLowerCase())).toList();
    }

    return results;
  }

  /// Get plants by multiple benefits
  static List<Plant> getPlantsByMultipleBenefits(List<String> benefits) {
    if (benefits.isEmpty) return getAllPlants();

    return getAllPlants().where((plant) {
      return benefits.every((benefit) => plant.benefits.contains(benefit));
    }).toList();
  }

  /// Get endangered plants
  static List<Plant> getEndangeredPlants() {
    return getAllPlants().where((plant) => plant.isEndangered == true).toList();
  }

  /// Get plants by active compound
  static List<Plant> getPlantsByActiveCompound(String compound) {
    if (compound.isEmpty) return getAllPlants();

    return getAllPlants().where((plant) =>
        plant.activeCompounds.any((c) =>
            c.toLowerCase().contains(compound.toLowerCase()))).toList();
  }

  /// Get plants by plant family
  static List<Plant> getPlantsByFamily(String family) {
    if (family.isEmpty) return getAllPlants();

    return getAllPlants().where((plant) =>
        plant.family.toLowerCase().contains(family.toLowerCase())).toList();
  }

  /// Get plants by preparation method
  static List<Plant> getPlantsByPreparation(String method) {
    if (method.isEmpty) return getAllPlants();

    return getAllPlants().where((plant) =>
        plant.preparationMethods.any((m) =>
            m.toLowerCase().contains(method.toLowerCase()))).toList();
  }

  /// Get plants by energetics (Traditional medicine classification)
  static List<Plant> getPlantsByEnergetics(String energetic) {
    if (energetic.isEmpty) return getAllPlants();

    return getAllPlants().where((plant) =>
        plant.energetics.toLowerCase().contains(energetic.toLowerCase())).toList();
  }

  /// Get plants by taste profile
  static List<Plant> getPlantsByTaste(String taste) {
    if (taste.isEmpty) return getAllPlants();

    return getAllPlants().where((plant) =>
        plant.taste.toLowerCase().contains(taste.toLowerCase())).toList();
  }

  /// Get plants suitable for specific conditions
  static List<Plant> getPlantsForCondition(String condition) {
    final lowercaseCondition = condition.toLowerCase();

    return getAllPlants().where((plant) =>
        plant.uses.any((use) => use.toLowerCase().contains(lowercaseCondition)) ||
        plant.benefits.any((benefit) => benefit.toLowerCase().contains(lowercaseCondition)) ||
        plant.traditionalUse.toLowerCase().contains(lowercaseCondition)).toList();
  }

  /// Get plants with specific contraindications
  static List<Plant> getPlantsWithContraindications(String contraindication) {
    if (contraindication.isEmpty) return [];

    return getAllPlants().where((plant) =>
        plant.contraindications.any((c) =>
            c.toLowerCase().contains(contraindication.toLowerCase()))).toList();
  }

  /// Get plants by conservation status
  static List<Plant> getPlantsByConservationStatus(String status) {
    if (status.isEmpty) return getAllPlants();

    return getAllPlants().where((plant) =>
        plant.conservationStatus.toLowerCase().contains(status.toLowerCase())).toList();
  }

  /// Get recently added plants (based on ID patterns)
  static List<Plant> getRecentlyAddedPlants() {
    // Get plants from newer sections (higher ID numbers or specific prefixes)
    final recentPrefixes = ['sam_', 'me_', 'scan_', 'car_', 'ind_', 'sea_', 'arc_'];

    return getAllPlants().where((plant) =>
        recentPrefixes.any((prefix) => plant.id.startsWith(prefix))).toList();
  }

  /// Get random plants for discovery
  static List<Plant> getRandomPlants({int count = 5}) {
    final plants = getAllPlants();
    plants.shuffle();
    return plants.take(count).toList();
  }

  /// Get plants by rating range
  static List<Plant> getPlantsByRatingRange(double minRating, double maxRating) {
    return getAllPlants().where((plant) =>
        plant.rating >= minRating && plant.rating <= maxRating).toList();
  }

  /// Get plants with most reviews (popularity)
  static List<Plant> getMostReviewedPlants({int count = 10}) {
    final plants = getAllPlants();
    plants.sort((a, b) => b.reviewCount.compareTo(a.reviewCount));
    return plants.take(count).toList();
  }

  /// Get plants by harvest season
  static List<Plant> getPlantsByHarvestSeason(String season) {
    if (season.isEmpty) return getAllPlants();

    return getAllPlants().where((plant) =>
        plant.harvestingSeason.toLowerCase().contains(season.toLowerCase())).toList();
  }
}
