import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';

/// Enhanced network image handler with comprehensive error handling
/// Specifically designed to handle Pixabay, Unsplash, and other external image service errors
class NetworkImageHandler {
  
  /// Build a robust network image with fallback handling
  static Widget buildRobustNetworkImage({
    required String imageUrl,
    double? width,
    double? height,
    BoxFit fit = BoxFit.cover,
    double borderRadius = 8.0,
    Widget? placeholder,
    Widget? errorWidget,
  }) {
    // Validate URL first
    if (imageUrl.isEmpty || !_isValidUrl(imageUrl)) {
      return _buildFallbackWidget(width, height, borderRadius);
    }

    return ClipRRect(
      borderRadius: BorderRadius.circular(borderRadius),
      child: CachedNetworkImage(
        imageUrl: imageUrl,
        width: width,
        height: height,
        fit: fit,
        placeholder: (context, url) => placeholder ?? _buildLoadingWidget(width, height),
        errorWidget: (context, url, error) {
          debugPrint('🖼️ Network image error for: $url');
          debugPrint('🔥 Error details: $error');
          
          // Try fallback URL for known problematic domains
          final fallbackUrl = _getFallbackUrl(url);
          if (fallbackUrl != null && fallbackUrl != url) {
            debugPrint('🔄 Attempting fallback URL: $fallbackUrl');
            return _buildFallbackImage(fallbackUrl, width, height, fit, borderRadius);
          }
          
          return errorWidget ?? _buildErrorWidget(width, height, borderRadius);
        },
        // Enhanced HTTP headers for better compatibility
        httpHeaders: const {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
          'Accept': 'image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8',
          'Accept-Language': 'en-US,en;q=0.9',
          'Accept-Encoding': 'gzip, deflate, br',
          'Cache-Control': 'max-age=31536000',
          'Sec-Fetch-Dest': 'image',
          'Sec-Fetch-Mode': 'no-cors',
          'Sec-Fetch-Site': 'cross-site',
        },
        // Cache configuration
        memCacheWidth: width?.toInt(),
        memCacheHeight: height?.toInt(),
        maxWidthDiskCache: 1200,
        maxHeightDiskCache: 1200,
        // Animation configuration
        fadeInDuration: const Duration(milliseconds: 300),
        fadeOutDuration: const Duration(milliseconds: 100),
        // Error listener for additional logging
        errorListener: (error) {
          debugPrint('🔥 CachedNetworkImage error listener: $error');
        },
      ),
    );
  }

  /// Build fallback image when primary fails
  static Widget _buildFallbackImage(String fallbackUrl, double? width, double? height, BoxFit fit, double borderRadius) {
    return ClipRRect(
      borderRadius: BorderRadius.circular(borderRadius),
      child: CachedNetworkImage(
        imageUrl: fallbackUrl,
        width: width,
        height: height,
        fit: fit,
        placeholder: (context, url) => _buildLoadingWidget(width, height),
        errorWidget: (context, url, error) {
          debugPrint('🔥 Fallback image also failed: $fallbackUrl');
          return _buildErrorWidget(width, height, borderRadius);
        },
        httpHeaders: const {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
          'Accept': 'image/*,*/*;q=0.8',
        },
      ),
    );
  }

  /// Build loading placeholder widget
  static Widget _buildLoadingWidget(double? width, double? height) {
    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(8),
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            SizedBox(
              width: (width != null && width < 100) ? width * 0.3 : 24,
              height: (height != null && height < 100) ? height * 0.3 : 24,
              child: const CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF22c55e)),
              ),
            ),
            if (width != null && width > 80) ...[
              const SizedBox(height: 8),
              Text(
                'Loading...',
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 12,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// Build error widget when all attempts fail
  static Widget _buildErrorWidget(double? width, double? height, double borderRadius) {
    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(borderRadius),
        border: Border.all(color: Colors.grey[300]!, width: 1),
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.nature,
              size: (width != null && width < 100) ? width * 0.4 : 32,
              color: const Color(0xFF22c55e),
            ),
            if (width != null && width > 80) ...[
              const SizedBox(height: 8),
              Text(
                'Nature\'s Place',
                style: TextStyle(
                  color: const Color(0xFF22c55e),
                  fontSize: (width < 150) ? 10 : 12,
                  fontWeight: FontWeight.w600,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 2),
              Text(
                'Image unavailable',
                style: TextStyle(
                  color: Colors.grey[500],
                  fontSize: (width < 150) ? 8 : 10,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// Build fallback widget for invalid URLs
  static Widget _buildFallbackWidget(double? width, double? height, double borderRadius) {
    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            const Color(0xFF22c55e).withValues(alpha: 0.1),
            const Color(0xFF16a34a).withValues(alpha: 0.1),
          ],
        ),
        borderRadius: BorderRadius.circular(borderRadius),
        border: Border.all(color: const Color(0xFF22c55e).withValues(alpha: 0.3), width: 1),
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.eco,
              size: (width != null && width < 100) ? width * 0.4 : 32,
              color: const Color(0xFF22c55e),
            ),
            if (width != null && width > 80) ...[
              const SizedBox(height: 8),
              Text(
                'Nature\'s Place',
                style: TextStyle(
                  color: const Color(0xFF22c55e),
                  fontSize: (width < 150) ? 10 : 12,
                  fontWeight: FontWeight.w600,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// Get fallback URL for problematic domains
  static String? _getFallbackUrl(String originalUrl) {
    // Pixabay fallbacks - use reliable Unsplash alternatives
    if (originalUrl.contains('pixabay.com')) {
      if (originalUrl.contains('ashwagandha') || originalUrl.contains('herb')) {
        return 'https://images.unsplash.com/photo-1544787219-7f47ccb76574?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=80';
      }
      if (originalUrl.contains('turmeric') || originalUrl.contains('spice')) {
        return 'https://images.unsplash.com/photo-1597362925123-77861d3fbac7?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=80';
      }
      if (originalUrl.contains('lavender') || originalUrl.contains('flower')) {
        return 'https://images.unsplash.com/photo-1611909023032-2d6b3134ecba?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=80';
      }
      if (originalUrl.contains('ginseng') || originalUrl.contains('root')) {
        return 'https://images.unsplash.com/photo-1544787219-7f47ccb76574?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=80';
      }
      // Generic nature fallback for other Pixabay images
      return 'https://images.unsplash.com/photo-1441974231531-c6227db76b6e?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=80';
    }
    
    // Unsplash fallbacks - use different Unsplash images
    if (originalUrl.contains('unsplash.com')) {
      return 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=80';
    }
    
    return null;
  }

  /// Validate URL format
  static bool _isValidUrl(String url) {
    try {
      final uri = Uri.parse(url);
      return uri.hasScheme && (uri.scheme == 'http' || uri.scheme == 'https');
    } catch (e) {
      return false;
    }
  }

  /// Clear image cache
  static Future<void> clearCache() async {
    try {
      await CachedNetworkImage.evictFromCache('');
      debugPrint('🧹 Network image cache cleared');
    } catch (e) {
      debugPrint('🔥 Failed to clear image cache: $e');
    }
  }

  /// Preload critical images
  static Future<void> preloadImage(String imageUrl, BuildContext context) async {
    if (!_isValidUrl(imageUrl)) return;

    // Check if context is still mounted before using it
    if (!context.mounted) return;

    try {
      await precacheImage(
        CachedNetworkImageProvider(imageUrl),
        context,
      );
      debugPrint('🖼️ Preloaded image: $imageUrl');
    } catch (e) {
      debugPrint('🔥 Failed to preload image: $imageUrl - $e');
      // Try fallback URL
      final fallbackUrl = _getFallbackUrl(imageUrl);
      if (fallbackUrl != null && context.mounted) {
        try {
          await precacheImage(
            CachedNetworkImageProvider(fallbackUrl),
            context,
          );
          debugPrint('🔄 Preloaded fallback image: $fallbackUrl');
        } catch (fallbackError) {
          debugPrint('🔥 Failed to preload fallback image: $fallbackError');
        }
      }
    }
  }
}
