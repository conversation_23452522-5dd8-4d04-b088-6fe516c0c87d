import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import '../models/vendor_models.dart';
import '../services/vendor_management_service.dart';

/// Screen for vendor onboarding and application
class VendorOnboardingScreen extends StatefulWidget {
  const VendorOnboardingScreen({super.key});

  @override
  State<VendorOnboardingScreen> createState() => _VendorOnboardingScreenState();
}

class _VendorOnboardingScreenState extends State<VendorOnboardingScreen> {
  final _formKey = GlobalKey<FormState>();
  final VendorManagementService _vendorService = VendorManagementService();
  
  // Form controllers
  final _nameController = TextEditingController();
  final _emailController = TextEditingController();
  final _phoneController = TextEditingController();
  final _businessNameController = TextEditingController();
  final _businessAddressController = TextEditingController();
  final _taxIdController = TextEditingController();
  final _descriptionController = TextEditingController();
  
  VendorType _selectedVendorType = VendorType.regular;
  final Set<ProductCategory> _selectedCategories = {};
  bool _isSubmitting = false;

  @override
  void dispose() {
    _nameController.dispose();
    _emailController.dispose();
    _phoneController.dispose();
    _businessNameController.dispose();
    _businessAddressController.dispose();
    _taxIdController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Become a Partner'),
        backgroundColor: const Color(0xFF22c55e),
        foregroundColor: Colors.white,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(20),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          const FaIcon(
                            FontAwesomeIcons.handshake,
                            color: Color(0xFF22c55e),
                            size: 24,
                          ),
                          const SizedBox(width: 12),
                          Text(
                            'Partner Application',
                            style: Theme.of(context).textTheme.titleLarge?.copyWith(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 12),
                      Text(
                        'Join our marketplace and start earning commissions by selling natural health products.',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              
              const SizedBox(height: 24),
              
              // Personal Information
              _buildSectionHeader('Personal Information'),
              const SizedBox(height: 16),
              
              _buildTextField(
                controller: _nameController,
                label: 'Full Name',
                icon: Icons.person,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter your full name';
                  }
                  return null;
                },
              ),
              
              const SizedBox(height: 16),
              
              _buildTextField(
                controller: _emailController,
                label: 'Email Address',
                icon: Icons.email,
                keyboardType: TextInputType.emailAddress,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter your email';
                  }
                  if (!value.contains('@')) {
                    return 'Please enter a valid email';
                  }
                  return null;
                },
              ),
              
              const SizedBox(height: 16),
              
              _buildTextField(
                controller: _phoneController,
                label: 'Phone Number',
                icon: Icons.phone,
                keyboardType: TextInputType.phone,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter your phone number';
                  }
                  return null;
                },
              ),
              
              const SizedBox(height: 24),
              
              // Business Information
              _buildSectionHeader('Business Information'),
              const SizedBox(height: 16),
              
              _buildTextField(
                controller: _businessNameController,
                label: 'Business Name',
                icon: Icons.business,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter your business name';
                  }
                  return null;
                },
              ),
              
              const SizedBox(height: 16),
              
              _buildTextField(
                controller: _businessAddressController,
                label: 'Business Address',
                icon: Icons.location_on,
                maxLines: 3,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter your business address';
                  }
                  return null;
                },
              ),
              
              const SizedBox(height: 16),
              
              _buildTextField(
                controller: _taxIdController,
                label: 'Tax ID / EIN',
                icon: Icons.receipt,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter your tax ID';
                  }
                  return null;
                },
              ),
              
              const SizedBox(height: 16),
              
              _buildTextField(
                controller: _descriptionController,
                label: 'Business Description',
                icon: Icons.description,
                maxLines: 4,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please describe your business';
                  }
                  return null;
                },
              ),
              
              const SizedBox(height: 24),
              
              // Partnership Type
              _buildSectionHeader('Partnership Type'),
              const SizedBox(height: 16),
              
              _buildVendorTypeSelector(),
              
              const SizedBox(height: 24),
              
              // Product Categories
              _buildSectionHeader('Product Categories'),
              const SizedBox(height: 16),
              
              _buildCategorySelector(),
              
              const SizedBox(height: 32),
              
              // Submit Button
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: _isSubmitting ? null : _submitApplication,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFF22c55e),
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  child: _isSubmitting
                      ? const Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            SizedBox(
                              width: 20,
                              height: 20,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                              ),
                            ),
                            SizedBox(width: 12),
                            Text('Submitting Application...'),
                          ],
                        )
                      : const Text(
                          'Submit Application',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                ),
              ),
              
              const SizedBox(height: 16),
              
              // Terms and conditions
              Text(
                'By submitting this application, you agree to our Terms of Service and Privacy Policy. Your application will be reviewed within 3-5 business days.',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Colors.grey[600],
                ),
                textAlign: TextAlign.center,
              ),
              
              const SizedBox(height: 32),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Text(
      title,
      style: Theme.of(context).textTheme.titleMedium?.copyWith(
        fontWeight: FontWeight.bold,
        color: const Color(0xFF22c55e),
      ),
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required IconData icon,
    TextInputType? keyboardType,
    int maxLines = 1,
    String? Function(String?)? validator,
  }) {
    return TextFormField(
      controller: controller,
      keyboardType: keyboardType,
      maxLines: maxLines,
      validator: validator,
      decoration: InputDecoration(
        labelText: label,
        prefixIcon: Icon(icon),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: Color(0xFF22c55e), width: 2),
        ),
      ),
    );
  }

  Widget _buildVendorTypeSelector() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Select Partnership Type',
              style: Theme.of(context).textTheme.titleSmall?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            ...VendorType.values.map((type) {
              return RadioListTile<VendorType>(
                title: Text(_getVendorTypeName(type)),
                subtitle: Text(_getVendorTypeDescription(type)),
                value: type,
                groupValue: _selectedVendorType,
                onChanged: (value) {
                  setState(() {
                    _selectedVendorType = value!;
                  });
                },
                activeColor: const Color(0xFF22c55e),
              );
            }),
          ],
        ),
      ),
    );
  }

  Widget _buildCategorySelector() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Select Product Categories',
              style: Theme.of(context).textTheme.titleSmall?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Choose the categories of products you plan to sell',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 12),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: ProductCategory.values.map((category) {
                final isSelected = _selectedCategories.contains(category);
                return FilterChip(
                  label: Text(_getCategoryName(category)),
                  selected: isSelected,
                  onSelected: (selected) {
                    setState(() {
                      if (selected) {
                        _selectedCategories.add(category);
                      } else {
                        _selectedCategories.remove(category);
                      }
                    });
                  },
                  selectedColor: const Color(0xFF22c55e).withValues(alpha: 0.2),
                  checkmarkColor: const Color(0xFF22c55e),
                );
              }).toList(),
            ),
          ],
        ),
      ),
    );
  }

  String _getVendorTypeName(VendorType type) {
    switch (type) {
      case VendorType.regular:
        return 'Regular Vendor';
      case VendorType.affiliate:
        return 'Affiliate Partner';
      case VendorType.premium:
        return 'Premium Vendor';
      case VendorType.exclusive:
        return 'Exclusive Partner';
    }
  }

  String _getVendorTypeDescription(VendorType type) {
    switch (type) {
      case VendorType.regular:
        return 'Standard commission rates, basic features';
      case VendorType.affiliate:
        return 'Performance-based commissions, marketing focus';
      case VendorType.premium:
        return 'Higher commission rates, premium features';
      case VendorType.exclusive:
        return 'Exclusive products, highest commission rates';
    }
  }

  String _getCategoryName(ProductCategory category) {
    switch (category) {
      case ProductCategory.supplements:
        return 'Supplements & Vitamins';
      case ProductCategory.essentialOils:
        return 'Essential Oils';
      case ProductCategory.herbalTeas:
        return 'Herbal Teas';
      case ProductCategory.skincare:
        return 'Natural Skincare';
      case ProductCategory.books:
        return 'Books & Guides';
      case ProductCategory.equipment:
        return 'Equipment & Tools';
      case ProductCategory.consultations:
        return 'Consultations';
    }
  }

  Future<void> _submitApplication() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    if (_selectedCategories.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please select at least one product category'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    setState(() {
      _isSubmitting = true;
    });

    try {
      // Create default commission rates for selected categories
      final commissionRates = <ProductCategory, double>{};
      for (final category in _selectedCategories) {
        commissionRates[category] = _getDefaultCommissionRate(category);
      }

      // Create vendor application
      final vendor = Vendor(
        id: 'vendor_${DateTime.now().millisecondsSinceEpoch}',
        name: _nameController.text.trim(),
        email: _emailController.text.trim(),
        phone: _phoneController.text.trim(),
        businessName: _businessNameController.text.trim(),
        businessAddress: _businessAddressController.text.trim(),
        taxId: _taxIdController.text.trim(),
        type: _selectedVendorType,
        status: VerificationStatus.pending,
        tier: CommissionTier.tier1,
        joinDate: DateTime.now(),
        categories: _selectedCategories.toList(),
        commissionRates: commissionRates,
        monthlyListingFee: _getMonthlyListingFee(_selectedVendorType),
        description: _descriptionController.text.trim(),
      );

      // Submit application
      await _vendorService.addVendor(vendor);

      // Show success message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Application submitted successfully! We\'ll review it within 3-5 business days.'),
            backgroundColor: Colors.green,
            duration: Duration(seconds: 5),
          ),
        );

        // Navigate back
        Navigator.of(context).pop();
      }

    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error submitting application: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSubmitting = false;
        });
      }
    }
  }

  double _getDefaultCommissionRate(ProductCategory category) {
    switch (category) {
      case ProductCategory.supplements:
        return 25.0;
      case ProductCategory.essentialOils:
        return 30.0;
      case ProductCategory.herbalTeas:
        return 20.0;
      case ProductCategory.skincare:
        return 28.0;
      case ProductCategory.books:
        return 15.0;
      case ProductCategory.equipment:
        return 18.0;
      case ProductCategory.consultations:
        return 35.0;
    }
  }

  double _getMonthlyListingFee(VendorType type) {
    switch (type) {
      case VendorType.regular:
        return 99.0;
      case VendorType.affiliate:
        return 0.0; // Affiliates don't pay listing fees
      case VendorType.premium:
        return 199.0;
      case VendorType.exclusive:
        return 299.0;
    }
  }
}
