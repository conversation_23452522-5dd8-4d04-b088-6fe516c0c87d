@echo off
echo 🔧 Nature's Place App Troubleshooting 🔧
echo.

echo Running comprehensive diagnostics...
echo.

echo ========================================
echo 1. Flutter Doctor
echo ========================================
flutter doctor -v
echo.

echo ========================================
echo 2. Available Devices
echo ========================================
flutter devices
echo.

echo ========================================
echo 3. Available Emulators
echo ========================================
flutter emulators
echo.

echo ========================================
echo 4. ADB Devices
echo ========================================
adb devices
echo.

echo ========================================
echo 5. Project Dependencies
echo ========================================
flutter pub deps
echo.

echo ========================================
echo 6. Android SDK Info
echo ========================================
echo Android SDK Path: %ANDROID_HOME%
echo Android SDK Tools: %ANDROID_HOME%\tools
echo Android Platform Tools: %ANDROID_HOME%\platform-tools
echo.

echo ========================================
echo 7. Java Version
echo ========================================
java -version
echo.

echo ========================================
echo 8. Gradle Version
echo ========================================
cd android
call gradlew --version
cd ..
echo.

echo ========================================
echo 9. Common Solutions
echo ========================================
echo If you're experiencing issues:
echo.
echo A. MainActivity not found:
echo    - Run: clean_build.bat
echo    - Check MainActivity.kt exists in correct package
echo    - Verify AndroidManifest.xml points to correct activity
echo.
echo B. Build failures:
echo    - Run: flutter clean
echo    - Delete build folders manually
echo    - Check Android SDK is properly installed
echo.
echo C. Device not detected:
echo    - Enable USB debugging on device
echo    - Install device drivers
echo    - Try different USB cable/port
echo.
echo D. Gradle issues:
echo    - Update Android Studio
echo    - Clear Gradle cache
echo    - Check internet connection for downloads
echo.

pause
