@echo off
echo 🌐 Starting Nature's Place in Chrome...
echo.

echo 📱 Checking Flutter installation...
flutter --version
if %errorlevel% neq 0 (
    echo ❌ Flutter not found! Please install Flutter first.
    pause
    exit /b 1
)

echo.
echo 🧹 Cleaning previous builds...
flutter clean

echo.
echo 📦 Getting dependencies...
flutter pub get

echo.
echo 🌐 Running in Chrome browser...
echo.
echo 🚀 Your Nature's Place app will open in Chrome!
echo.
echo ⏹️  Press Ctrl+C to stop the app
echo.

flutter run -d chrome

pause
