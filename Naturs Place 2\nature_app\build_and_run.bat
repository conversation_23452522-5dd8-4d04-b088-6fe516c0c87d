@echo off
echo 🌿 Nature's Place - Complete Build and Run Script 🌿
echo.

echo Step 1: Checking Flutter installation...
flutter --version
if %errorlevel% neq 0 (
    echo ❌ Flutter not found! Please install Flutter first.
    pause
    exit /b 1
)
echo ✅ Flutter installation verified
echo.

echo Step 2: Checking available devices...
flutter devices
echo.

echo Step 3: Cleaning build cache...
flutter clean
echo ✅ Build cache cleaned
echo.

echo Step 4: Getting dependencies...
flutter pub get
if %errorlevel% neq 0 (
    echo ❌ Failed to get dependencies!
    pause
    exit /b 1
)
echo ✅ Dependencies updated
echo.

echo Step 5: Building APK (debug)...
flutter build apk --debug
if %errorlevel% neq 0 (
    echo ❌ Build failed! Check the error messages above.
    echo.
    echo Trying alternative build approach...
    echo.
    echo Cleaning Android Gradle cache...
    cd android
    call gradlew clean
    cd ..
    echo.
    echo Retrying build...
    flutter build apk --debug
    if %errorlevel% neq 0 (
        echo ❌ Build still failed! Please check the error messages.
        pause
        exit /b 1
    )
)
echo ✅ APK built successfully
echo.

echo Step 6: Running the app...
echo Make sure you have an emulator running or device connected
echo.
flutter run --debug
echo.

echo 🎉 Script completed!
pause
