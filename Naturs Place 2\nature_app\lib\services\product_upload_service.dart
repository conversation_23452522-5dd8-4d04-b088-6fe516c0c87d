import 'package:flutter/foundation.dart';
import '../models/user_models.dart';
import 'role_based_access_service.dart';
import 'postgres_service.dart';

/// Service for managing vendor product uploads with tier-based limits
class ProductUploadService {
  static final ProductUploadService _instance = ProductUploadService._internal();
  factory ProductUploadService() => _instance;
  ProductUploadService._internal();

  final RoleBasedAccessService _accessService = RoleBasedAccessService();
  final PostgreSQLService _postgresService = PostgreSQLService();

  /// Initialize product upload service
  Future<void> initialize() async {
    await _postgresService.initialize();
    debugPrint('✅ Product Upload Service initialized');
  }

  /// Check if vendor can upload a new product
  Future<UploadValidationResult> validateProductUpload(String vendorId, {
    int imageCount = 1,
    bool hasVideo = false,
    bool isBulkUpload = false,
  }) async {
    try {
      final vendor = await _getVendorById(vendorId);
      if (vendor == null) {
        return UploadValidationResult(
          canUpload: false,
          error: 'Vendor not found',
        );
      }

      final benefits = _accessService.getVendorTierBenefits(vendor.tier);
      final uploadRestrictions = benefits['upload_restrictions'] as Map<String, dynamic>;
      
      // Check total product limit
      final maxProducts = benefits['max_products'] as int;
      if (vendor.productCount >= maxProducts) {
        return UploadValidationResult(
          canUpload: false,
          error: 'Product limit reached (${vendor.productCount}/$maxProducts). Upgrade tier to add more products.',
          currentCount: vendor.productCount,
          maxAllowed: maxProducts,
        );
      }

      // Check daily upload limit
      final maxDailyUploads = benefits['max_daily_uploads'] as int;
      final todayUploads = await _getTodayUploadCount(vendorId);
      if (todayUploads >= maxDailyUploads) {
        return UploadValidationResult(
          canUpload: false,
          error: 'Daily upload limit reached ($todayUploads/$maxDailyUploads). Try again tomorrow.',
          currentCount: todayUploads,
          maxAllowed: maxDailyUploads,
        );
      }

      // Check weekly upload limit
      final maxWeeklyUploads = benefits['max_weekly_uploads'] as int;
      final weekUploads = await _getWeekUploadCount(vendorId);
      if (weekUploads >= maxWeeklyUploads) {
        return UploadValidationResult(
          canUpload: false,
          error: 'Weekly upload limit reached ($weekUploads/$maxWeeklyUploads). Limit resets weekly.',
          currentCount: weekUploads,
          maxAllowed: maxWeeklyUploads,
        );
      }

      // Check image limit per product
      final maxImages = uploadRestrictions['image_limit_per_product'] as int;
      if (imageCount > maxImages) {
        return UploadValidationResult(
          canUpload: false,
          error: 'Too many images. Your tier allows $maxImages images per product.',
          currentCount: imageCount,
          maxAllowed: maxImages,
        );
      }

      // Check video upload permission
      final videoAllowed = uploadRestrictions['video_uploads'] as bool;
      if (hasVideo && !videoAllowed) {
        return UploadValidationResult(
          canUpload: false,
          error: 'Video uploads not available in your tier. Upgrade to Standard or higher.',
        );
      }

      // Check bulk upload permission
      final bulkAllowed = uploadRestrictions['bulk_upload'] as bool;
      if (isBulkUpload && !bulkAllowed) {
        return UploadValidationResult(
          canUpload: false,
          error: 'Bulk uploads not available in your tier. Upgrade to Standard or higher.',
        );
      }

      // All checks passed
      return UploadValidationResult(
        canUpload: true,
        remainingProducts: maxProducts - vendor.productCount,
        remainingDailyUploads: maxDailyUploads - todayUploads,
        remainingWeeklyUploads: maxWeeklyUploads - weekUploads,
      );

    } catch (e) {
      debugPrint('❌ Upload validation failed: $e');
      return UploadValidationResult(
        canUpload: false,
        error: 'Upload validation error',
      );
    }
  }

  /// Get vendor's upload statistics
  Future<UploadStatistics> getVendorUploadStats(String vendorId) async {
    try {
      final vendor = await _getVendorById(vendorId);
      if (vendor == null) {
        return UploadStatistics.empty(vendorId);
      }

      final benefits = _accessService.getVendorTierBenefits(vendor.tier);
      
      return UploadStatistics(
        vendorId: vendorId,
        currentTier: vendor.tier,
        totalProducts: vendor.productCount,
        maxProducts: benefits['max_products'] as int,
        todayUploads: await _getTodayUploadCount(vendorId),
        maxDailyUploads: benefits['max_daily_uploads'] as int,
        weekUploads: await _getWeekUploadCount(vendorId),
        maxWeeklyUploads: benefits['max_weekly_uploads'] as int,
        monthUploads: await _getMonthUploadCount(vendorId),
        uploadRestrictions: benefits['upload_restrictions'] as Map<String, dynamic>,
        marketingOptions: benefits['marketing_options'] as Map<String, dynamic>,
      );
    } catch (e) {
      debugPrint('❌ Failed to get upload stats: $e');
      return UploadStatistics.empty(vendorId);
    }
  }

  /// Record a successful product upload
  Future<void> recordProductUpload(String vendorId, {
    int imageCount = 1,
    bool hasVideo = false,
    bool isBulkUpload = false,
  }) async {
    try {
      await _postgresService.recordProductUpload(
        vendorId,
        imageCount: imageCount,
        hasVideo: hasVideo,
        isBulkUpload: isBulkUpload,
      );
      
      // Update vendor product count
      await _postgresService.incrementVendorProductCount(vendorId);
      
      debugPrint('✅ Product upload recorded for vendor: $vendorId');
    } catch (e) {
      debugPrint('❌ Failed to record upload: $e');
    }
  }

  /// Get marketing options available to vendor
  Future<MarketingOptions> getVendorMarketingOptions(String vendorId) async {
    try {
      final vendor = await _getVendorById(vendorId);
      if (vendor == null) {
        return MarketingOptions.empty();
      }

      final benefits = _accessService.getVendorTierBenefits(vendor.tier);
      final marketingData = benefits['marketing_options'] as Map<String, dynamic>;
      
      return MarketingOptions(
        tier: vendor.tier,
        featuredProductSlots: marketingData['featured_products'] as int,
        usedFeaturedSlots: await _getUsedFeaturedSlots(vendorId),
        homepageBannerAccess: marketingData['homepage_banner'] as bool,
        categorySpotlightAccess: marketingData['category_spotlight'] as bool,
        emailCampaignAccess: marketingData['email_campaigns'] as bool,
        socialMediaPromotionAccess: marketingData['social_media_promotion'] as bool,
        seoOptimizationAccess: marketingData['seo_optimization'] as bool,
        availableBadges: List<String>.from(marketingData['product_badges']),
        activeCampaigns: await _getActiveCampaigns(vendorId),
      );
    } catch (e) {
      debugPrint('❌ Failed to get marketing options: $e');
      return MarketingOptions.empty();
    }
  }

  /// Check if vendor can feature a product
  Future<bool> canFeatureProduct(String vendorId) async {
    try {
      final marketingOptions = await getVendorMarketingOptions(vendorId);
      return marketingOptions.usedFeaturedSlots < marketingOptions.featuredProductSlots;
    } catch (e) {
      debugPrint('❌ Failed to check feature product eligibility: $e');
      return false;
    }
  }

  /// Feature a product (if slots available)
  Future<FeatureProductResult> featureProduct(String vendorId, String productId) async {
    try {
      final canFeature = await canFeatureProduct(vendorId);
      if (!canFeature) {
        final marketingOptions = await getVendorMarketingOptions(vendorId);
        return FeatureProductResult(
          success: false,
          error: 'No featured product slots available (${marketingOptions.usedFeaturedSlots}/${marketingOptions.featuredProductSlots})',
        );
      }

      await _postgresService.featureProduct(vendorId, productId);
      
      return FeatureProductResult(
        success: true,
        message: 'Product featured successfully',
      );
    } catch (e) {
      debugPrint('❌ Failed to feature product: $e');
      return FeatureProductResult(
        success: false,
        error: 'Failed to feature product',
      );
    }
  }

  // Private helper methods

  Future<VendorUser?> _getVendorById(String vendorId) async {
    // Mock implementation - replace with actual database query
    if (vendorId == 'vendor_001') {
      return VendorUser(
        id: vendorId,
        email: '<EMAIL>',
        firstName: 'Jane',
        lastName: 'Vendor',
        status: UserStatus.active,
        createdAt: DateTime.now().subtract(const Duration(days: 60)),
        businessName: 'Healing Herbs Co.',
        businessAddress: '123 Herb Street',
        taxId: 'TAX123456',
        tier: VendorTier.standard,
        monthlyFee: VendorTier.standard.monthlyFee,
        paymentCurrent: true,
        totalSales: 15000.0,
        totalCommissionOwed: 1800.0,
        productCount: 45, // Current product count
      );
    }
    return null;
  }

  Future<int> _getTodayUploadCount(String vendorId) async {
    // Mock implementation - replace with actual database query
    return 3; // 3 uploads today
  }

  Future<int> _getWeekUploadCount(String vendorId) async {
    // Mock implementation - replace with actual database query
    return 12; // 12 uploads this week
  }

  Future<int> _getMonthUploadCount(String vendorId) async {
    // Mock implementation - replace with actual database query
    return 35; // 35 uploads this month
  }

  Future<int> _getUsedFeaturedSlots(String vendorId) async {
    // Mock implementation - replace with actual database query
    return 1; // 1 featured product currently
  }

  Future<List<String>> _getActiveCampaigns(String vendorId) async {
    // Mock implementation - replace with actual database query
    return ['Spring Sale', 'New Product Launch'];
  }
}

/// Result of upload validation
class UploadValidationResult {
  final bool canUpload;
  final String? error;
  final int? currentCount;
  final int? maxAllowed;
  final int? remainingProducts;
  final int? remainingDailyUploads;
  final int? remainingWeeklyUploads;

  UploadValidationResult({
    required this.canUpload,
    this.error,
    this.currentCount,
    this.maxAllowed,
    this.remainingProducts,
    this.remainingDailyUploads,
    this.remainingWeeklyUploads,
  });
}

/// Vendor upload statistics
class UploadStatistics {
  final String vendorId;
  final VendorTier currentTier;
  final int totalProducts;
  final int maxProducts;
  final int todayUploads;
  final int maxDailyUploads;
  final int weekUploads;
  final int maxWeeklyUploads;
  final int monthUploads;
  final Map<String, dynamic> uploadRestrictions;
  final Map<String, dynamic> marketingOptions;

  UploadStatistics({
    required this.vendorId,
    required this.currentTier,
    required this.totalProducts,
    required this.maxProducts,
    required this.todayUploads,
    required this.maxDailyUploads,
    required this.weekUploads,
    required this.maxWeeklyUploads,
    required this.monthUploads,
    required this.uploadRestrictions,
    required this.marketingOptions,
  });

  factory UploadStatistics.empty(String vendorId) {
    return UploadStatistics(
      vendorId: vendorId,
      currentTier: VendorTier.basic,
      totalProducts: 0,
      maxProducts: 0,
      todayUploads: 0,
      maxDailyUploads: 0,
      weekUploads: 0,
      maxWeeklyUploads: 0,
      monthUploads: 0,
      uploadRestrictions: {},
      marketingOptions: {},
    );
  }
}

/// Marketing options available to vendor
class MarketingOptions {
  final VendorTier tier;
  final int featuredProductSlots;
  final int usedFeaturedSlots;
  final bool homepageBannerAccess;
  final bool categorySpotlightAccess;
  final bool emailCampaignAccess;
  final bool socialMediaPromotionAccess;
  final bool seoOptimizationAccess;
  final List<String> availableBadges;
  final List<String> activeCampaigns;

  MarketingOptions({
    required this.tier,
    required this.featuredProductSlots,
    required this.usedFeaturedSlots,
    required this.homepageBannerAccess,
    required this.categorySpotlightAccess,
    required this.emailCampaignAccess,
    required this.socialMediaPromotionAccess,
    required this.seoOptimizationAccess,
    required this.availableBadges,
    required this.activeCampaigns,
  });

  factory MarketingOptions.empty() {
    return MarketingOptions(
      tier: VendorTier.basic,
      featuredProductSlots: 0,
      usedFeaturedSlots: 0,
      homepageBannerAccess: false,
      categorySpotlightAccess: false,
      emailCampaignAccess: false,
      socialMediaPromotionAccess: false,
      seoOptimizationAccess: false,
      availableBadges: [],
      activeCampaigns: [],
    );
  }
}

/// Result of featuring a product
class FeatureProductResult {
  final bool success;
  final String? message;
  final String? error;

  FeatureProductResult({
    required this.success,
    this.message,
    this.error,
  });
}
