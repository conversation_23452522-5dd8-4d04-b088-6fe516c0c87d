<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AndroidLayouts">
    <shared>
      <config />
    </shared>
  </component>
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="NONE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="e036299c-37a7-429d-a303-4de6049518ca" name="Changes" comment="">
      <change beforePath="$PROJECT_DIR$/../../.gitignore" beforeDir="false" afterPath="$PROJECT_DIR$/../../.gitignore" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../.idea/kotlinc.xml" beforeDir="false" afterPath="$PROJECT_DIR$/../../.idea/kotlinc.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../.idea/misc.xml" beforeDir="false" afterPath="$PROJECT_DIR$/../../.idea/misc.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../.idea/vcs.xml" beforeDir="false" afterPath="$PROJECT_DIR$/../../.idea/vcs.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../MobileApp" beforeDir="false" afterPath="$PROJECT_DIR$/../../MobileApp" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../README.md" beforeDir="false" afterPath="$PROJECT_DIR$/../../README.md" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../app/build.gradle.kts" beforeDir="false" afterPath="$PROJECT_DIR$/../../app/build.gradle.kts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../app/src/main/AndroidManifest.xml" beforeDir="false" afterPath="$PROJECT_DIR$/../../app/src/main/AndroidManifest.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../app/src/main/java/com/example/healing_app/HealingApp.kt" beforeDir="false" afterPath="$PROJECT_DIR$/../../app/src/main/java/com/example/healing_app/HealingApp.kt" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../app/src/main/java/com/example/healing_app/MainActivity.kt" beforeDir="false" afterPath="$PROJECT_DIR$/../../app/src/main/java/com/example/healing_app/MainActivity.kt" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../app/src/main/java/com/example/healing_app/ui/theme/Color.kt" beforeDir="false" afterPath="$PROJECT_DIR$/../../app/src/main/java/com/example/healing_app/ui/theme/Color.kt" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../app/src/main/java/com/example/healing_app/ui/theme/Theme.kt" beforeDir="false" afterPath="$PROJECT_DIR$/../../app/src/main/java/com/example/healing_app/ui/theme/Theme.kt" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../app/src/main/java/com/example/healing_app/ui/theme/Type.kt" beforeDir="false" afterPath="$PROJECT_DIR$/../../app/src/main/java/com/example/healing_app/ui/theme/Type.kt" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../app/src/main/java/com/example/healingapp/MainActivity.kt" beforeDir="false" afterPath="$PROJECT_DIR$/../../app/src/main/java/com/example/healingapp/MainActivity.kt" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../app/src/main/res/values/colors.xml" beforeDir="false" afterPath="$PROJECT_DIR$/../../app/src/main/res/values/colors.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../app/src/main/res/values/strings.xml" beforeDir="false" afterPath="$PROJECT_DIR$/../../app/src/main/res/values/strings.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../app/src/main/res/values/themes.xml" beforeDir="false" afterPath="$PROJECT_DIR$/../../app/src/main/res/values/themes.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../backend/src/config/db.ts" beforeDir="false" afterPath="$PROJECT_DIR$/../../backend/src/config/db.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../backend/src/data/mockData.ts" beforeDir="false" afterPath="$PROJECT_DIR$/../../backend/src/data/mockData.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../backend/src/data/seeder.ts" beforeDir="false" afterPath="$PROJECT_DIR$/../../backend/src/data/seeder.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../backend/src/server.ts" beforeDir="false" afterPath="$PROJECT_DIR$/../../backend/src/server.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../build.gradle.kts" beforeDir="false" afterPath="$PROJECT_DIR$/../../build.gradle.kts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/index.html" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/index.html" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/.package-lock.json" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/.package-lock.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/.vite/deps_temp_385b9825/axios.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/.vite/deps_temp_385b9825/axios.js.map" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/.vite/deps_temp_385b9825/chunk-DC5AMYBS.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/.vite/deps_temp_385b9825/chunk-DC5AMYBS.js.map" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/.vite/deps_temp_385b9825/chunk-NUMECXU6.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/.vite/deps_temp_385b9825/chunk-NUMECXU6.js.map" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/.vite/deps_temp_385b9825/chunk-RLJ2RCJQ.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/.vite/deps_temp_385b9825/chunk-RLJ2RCJQ.js.map" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/.vite/deps_temp_385b9825/package.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/.vite/deps_temp_385b9825/react-dom.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/.vite/deps_temp_385b9825/react-dom.js.map" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/.vite/deps_temp_385b9825/react-dom_client.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/.vite/deps_temp_385b9825/react-dom_client.js.map" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/.vite/deps_temp_385b9825/react-router-dom.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/.vite/deps_temp_385b9825/react-router-dom.js.map" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/.vite/deps_temp_385b9825/react.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/.vite/deps_temp_385b9825/react.js.map" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/.vite/deps_temp_385b9825/react_jsx-dev-runtime.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/.vite/deps_temp_385b9825/react_jsx-dev-runtime.js.map" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/.vite/deps_temp_385b9825/react_jsx-runtime.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/.vite/deps_temp_385b9825/react_jsx-runtime.js.map" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/@esbuild/win32-x64/README.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/@esbuild/win32-x64/esbuild.exe" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/@esbuild/win32-x64/package.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/@rollup/rollup-win32-x64-msvc/README.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/@rollup/rollup-win32-x64-msvc/package.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/@rollup/rollup-win32-x64-msvc/rollup.win32-x64-msvc.node" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/@types/babel__generator/README.md" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/@types/babel__generator/README.md" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/@types/babel__generator/index.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/@types/babel__generator/index.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/@types/babel__generator/package.json" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/@types/babel__generator/package.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/@types/react-dom/README.md" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/@types/react-dom/README.md" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/@types/react-dom/package.json" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/@types/react-dom/package.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/@types/react-dom/server.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/@types/react-dom/server.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/agents.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/agents.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/browserVersions.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/browserVersions.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/aac.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/aac.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/abortcontroller.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/abortcontroller.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/ac3-ec3.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/ac3-ec3.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/accelerometer.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/accelerometer.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/addeventlistener.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/addeventlistener.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/alternate-stylesheet.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/alternate-stylesheet.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/ambient-light.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/ambient-light.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/apng.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/apng.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/array-find-index.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/array-find-index.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/array-find.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/array-find.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/array-flat.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/array-flat.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/array-includes.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/array-includes.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/arrow-functions.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/arrow-functions.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/asmjs.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/asmjs.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/async-clipboard.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/async-clipboard.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/async-functions.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/async-functions.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/atob-btoa.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/atob-btoa.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/audio-api.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/audio-api.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/audio.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/audio.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/audiotracks.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/audiotracks.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/autofocus.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/autofocus.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/auxclick.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/auxclick.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/av1.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/av1.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/avif.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/avif.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/background-attachment.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/background-attachment.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/background-clip-text.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/background-clip-text.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/background-img-opts.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/background-img-opts.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/background-position-x-y.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/background-position-x-y.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/background-repeat-round-space.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/background-repeat-round-space.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/background-sync.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/background-sync.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/battery-status.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/battery-status.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/beacon.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/beacon.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/beforeafterprint.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/beforeafterprint.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/bigint.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/bigint.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/blobbuilder.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/blobbuilder.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/bloburls.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/bloburls.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/border-image.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/border-image.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/border-radius.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/border-radius.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/broadcastchannel.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/broadcastchannel.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/brotli.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/brotli.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/calc.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/calc.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/canvas-blending.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/canvas-blending.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/canvas-text.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/canvas-text.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/canvas.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/canvas.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/ch-unit.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/ch-unit.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/chacha20-poly1305.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/chacha20-poly1305.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/channel-messaging.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/channel-messaging.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/childnode-remove.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/childnode-remove.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/classlist.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/classlist.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/client-hints-dpr-width-viewport.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/client-hints-dpr-width-viewport.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/clipboard.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/clipboard.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/colr-v1.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/colr-v1.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/colr.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/colr.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/comparedocumentposition.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/comparedocumentposition.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/console-basic.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/console-basic.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/console-time.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/console-time.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/const.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/const.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/constraint-validation.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/constraint-validation.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/contenteditable.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/contenteditable.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/contentsecuritypolicy.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/contentsecuritypolicy.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/contentsecuritypolicy2.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/contentsecuritypolicy2.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/cookie-store-api.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/cookie-store-api.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/cors.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/cors.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/createimagebitmap.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/createimagebitmap.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/credential-management.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/credential-management.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/cross-document-view-transitions.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/cross-document-view-transitions.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/cryptography.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/cryptography.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-all.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-all.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-anchor-positioning.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-anchor-positioning.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-animation.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-animation.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-any-link.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-any-link.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-appearance.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-appearance.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-at-counter-style.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-at-counter-style.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-autofill.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-autofill.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-backdrop-filter.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-backdrop-filter.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-background-offsets.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-background-offsets.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-backgroundblendmode.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-backgroundblendmode.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-boxdecorationbreak.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-boxdecorationbreak.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-boxshadow.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-boxshadow.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-canvas.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-canvas.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-caret-color.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-caret-color.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-cascade-layers.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-cascade-layers.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-cascade-scope.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-cascade-scope.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-case-insensitive.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-case-insensitive.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-clip-path.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-clip-path.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-color-adjust.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-color-adjust.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-color-function.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-color-function.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-conic-gradients.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-conic-gradients.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-container-queries-style.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-container-queries-style.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-container-queries.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-container-queries.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-container-query-units.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-container-query-units.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-containment.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-containment.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-content-visibility.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-content-visibility.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-counters.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-counters.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-crisp-edges.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-crisp-edges.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-cross-fade.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-cross-fade.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-default-pseudo.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-default-pseudo.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-descendant-gtgt.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-descendant-gtgt.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-deviceadaptation.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-deviceadaptation.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-dir-pseudo.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-dir-pseudo.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-display-contents.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-display-contents.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-element-function.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-element-function.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-env-function.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-env-function.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-exclusions.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-exclusions.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-featurequeries.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-featurequeries.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-file-selector-button.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-file-selector-button.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-filter-function.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-filter-function.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-filters.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-filters.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-first-letter.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-first-letter.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-first-line.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-first-line.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-fixed.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-fixed.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-focus-visible.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-focus-visible.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-focus-within.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-focus-within.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-font-palette.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-font-palette.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-font-rendering-controls.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-font-rendering-controls.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-font-stretch.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-font-stretch.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-gencontent.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-gencontent.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-gradients.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-gradients.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-grid-animation.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-grid-animation.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-grid.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-grid.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-hanging-punctuation.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-hanging-punctuation.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-has.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-has.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-hyphens.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-hyphens.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-image-orientation.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-image-orientation.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-image-set.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-image-set.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-in-out-of-range.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-in-out-of-range.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-indeterminate-pseudo.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-indeterminate-pseudo.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-initial-letter.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-initial-letter.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-initial-value.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-initial-value.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-lch-lab.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-lch-lab.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-letter-spacing.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-letter-spacing.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-line-clamp.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-line-clamp.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-logical-props.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-logical-props.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-marker-pseudo.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-marker-pseudo.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-masks.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-masks.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-matches-pseudo.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-matches-pseudo.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-math-functions.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-math-functions.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-media-interaction.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-media-interaction.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-media-range-syntax.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-media-range-syntax.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-media-resolution.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-media-resolution.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-media-scripting.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-media-scripting.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-mediaqueries.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-mediaqueries.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-mixblendmode.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-mixblendmode.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-module-scripts.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-module-scripts.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-motion-paths.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-motion-paths.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-namespaces.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-namespaces.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-nesting.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-nesting.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-not-sel-list.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-not-sel-list.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-nth-child-of.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-nth-child-of.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-opacity.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-opacity.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-optional-pseudo.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-optional-pseudo.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-overflow-anchor.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-overflow-anchor.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-overflow-overlay.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-overflow-overlay.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-overflow.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-overflow.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-overscroll-behavior.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-overscroll-behavior.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-page-break.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-page-break.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-paged-media.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-paged-media.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-paint-api.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-paint-api.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-placeholder-shown.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-placeholder-shown.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-placeholder.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-placeholder.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-print-color-adjust.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-print-color-adjust.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-read-only-write.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-read-only-write.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-rebeccapurple.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-rebeccapurple.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-reflections.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-reflections.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-regions.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-regions.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-relative-colors.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-relative-colors.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-repeating-gradients.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-repeating-gradients.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-resize.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-resize.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-revert-value.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-revert-value.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-rrggbbaa.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-rrggbbaa.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-scroll-behavior.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-scroll-behavior.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-scrollbar.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-scrollbar.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-sel2.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-sel2.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-sel3.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-sel3.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-selection.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-selection.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-shapes.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-shapes.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-snappoints.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-snappoints.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-sticky.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-sticky.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-subgrid.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-subgrid.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-supports-api.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-supports-api.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-table.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-table.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-text-align-last.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-text-align-last.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-text-box-trim.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-text-box-trim.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-text-indent.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-text-indent.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-text-justify.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-text-justify.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-text-orientation.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-text-orientation.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-text-spacing.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-text-spacing.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-text-wrap-balance.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-text-wrap-balance.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-textshadow.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-textshadow.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-touch-action.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-touch-action.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-transitions.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-transitions.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-unicode-bidi.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-unicode-bidi.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-unset-value.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-unset-value.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-variables.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-variables.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-when-else.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-when-else.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-widows-orphans.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-widows-orphans.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-width-stretch.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-width-stretch.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-writing-mode.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-writing-mode.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-zoom.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css-zoom.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css3-attr.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css3-attr.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css3-boxsizing.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css3-boxsizing.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css3-colors.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css3-colors.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css3-cursors-grab.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css3-cursors-grab.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css3-cursors-newer.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css3-cursors-newer.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css3-cursors.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css3-cursors.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css3-tabsize.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/css3-tabsize.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/currentcolor.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/currentcolor.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/custom-elements.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/custom-elements.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/custom-elementsv1.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/custom-elementsv1.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/customevent.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/customevent.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/datalist.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/datalist.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/dataset.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/dataset.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/datauri.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/datauri.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/date-tolocaledatestring.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/date-tolocaledatestring.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/declarative-shadow-dom.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/declarative-shadow-dom.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/decorators.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/decorators.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/details.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/details.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/deviceorientation.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/deviceorientation.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/devicepixelratio.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/devicepixelratio.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/dialog.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/dialog.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/dispatchevent.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/dispatchevent.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/dnssec.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/dnssec.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/do-not-track.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/do-not-track.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/document-currentscript.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/document-currentscript.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/document-evaluate-xpath.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/document-evaluate-xpath.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/document-execcommand.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/document-execcommand.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/document-policy.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/document-policy.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/document-scrollingelement.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/document-scrollingelement.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/documenthead.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/documenthead.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/dom-manip-convenience.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/dom-manip-convenience.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/dom-range.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/dom-range.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/domcontentloaded.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/domcontentloaded.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/dommatrix.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/dommatrix.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/download.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/download.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/dragndrop.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/dragndrop.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/element-closest.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/element-closest.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/element-from-point.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/element-from-point.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/element-scroll-methods.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/element-scroll-methods.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/eme.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/eme.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/eot.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/eot.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/es5.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/es5.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/es6-class.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/es6-class.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/es6-generators.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/es6-generators.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/es6-module-dynamic-import.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/es6-module-dynamic-import.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/es6-module.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/es6-module.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/es6-number.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/es6-number.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/es6-string-includes.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/es6-string-includes.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/es6.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/es6.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/eventsource.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/eventsource.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/extended-system-fonts.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/extended-system-fonts.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/feature-policy.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/feature-policy.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/fetch.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/fetch.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/fieldset-disabled.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/fieldset-disabled.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/fileapi.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/fileapi.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/filereader.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/filereader.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/filereadersync.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/filereadersync.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/filesystem.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/filesystem.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/flac.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/flac.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/flexbox-gap.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/flexbox-gap.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/flexbox.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/flexbox.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/flow-root.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/flow-root.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/focusin-focusout-events.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/focusin-focusout-events.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/font-family-system-ui.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/font-family-system-ui.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/font-feature.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/font-feature.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/font-kerning.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/font-kerning.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/font-loading.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/font-loading.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/font-size-adjust.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/font-size-adjust.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/font-smooth.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/font-smooth.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/font-unicode-range.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/font-unicode-range.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/font-variant-alternates.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/font-variant-alternates.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/font-variant-numeric.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/font-variant-numeric.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/fontface.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/fontface.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/form-attribute.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/form-attribute.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/form-submit-attributes.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/form-submit-attributes.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/form-validation.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/form-validation.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/forms.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/forms.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/fullscreen.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/fullscreen.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/gamepad.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/gamepad.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/geolocation.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/geolocation.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/getboundingclientrect.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/getboundingclientrect.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/getcomputedstyle.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/getcomputedstyle.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/getelementsbyclassname.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/getelementsbyclassname.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/getrandomvalues.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/getrandomvalues.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/gyroscope.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/gyroscope.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/hardwareconcurrency.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/hardwareconcurrency.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/hashchange.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/hashchange.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/heif.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/heif.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/hevc.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/hevc.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/hidden.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/hidden.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/high-resolution-time.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/high-resolution-time.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/history.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/history.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/html-media-capture.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/html-media-capture.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/html5semantic.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/html5semantic.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/http-live-streaming.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/http-live-streaming.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/http2.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/http2.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/http3.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/http3.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/iframe-sandbox.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/iframe-sandbox.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/iframe-seamless.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/iframe-seamless.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/iframe-srcdoc.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/iframe-srcdoc.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/imagecapture.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/imagecapture.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/ime.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/ime.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/img-naturalwidth-naturalheight.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/img-naturalwidth-naturalheight.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/import-maps.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/import-maps.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/imports.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/imports.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/indeterminate-checkbox.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/indeterminate-checkbox.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/indexeddb.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/indexeddb.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/indexeddb2.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/indexeddb2.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/inline-block.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/inline-block.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/innertext.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/innertext.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/input-autocomplete-onoff.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/input-autocomplete-onoff.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/input-color.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/input-color.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/input-datetime.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/input-datetime.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/input-email-tel-url.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/input-email-tel-url.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/input-event.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/input-event.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/input-file-accept.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/input-file-accept.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/input-file-directory.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/input-file-directory.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/input-file-multiple.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/input-file-multiple.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/input-inputmode.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/input-inputmode.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/input-minlength.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/input-minlength.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/input-number.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/input-number.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/input-pattern.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/input-pattern.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/input-placeholder.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/input-placeholder.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/input-range.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/input-range.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/input-search.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/input-search.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/input-selection.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/input-selection.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/insert-adjacent.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/insert-adjacent.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/insertadjacenthtml.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/insertadjacenthtml.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/internationalization.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/internationalization.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/intersectionobserver-v2.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/intersectionobserver-v2.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/intersectionobserver.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/intersectionobserver.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/intl-pluralrules.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/intl-pluralrules.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/intrinsic-width.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/intrinsic-width.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/jpeg2000.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/jpeg2000.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/jpegxl.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/jpegxl.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/jpegxr.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/jpegxr.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/js-regexp-lookbehind.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/js-regexp-lookbehind.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/json.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/json.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/justify-content-space-evenly.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/justify-content-space-evenly.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/kerning-pairs-ligatures.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/kerning-pairs-ligatures.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/keyboardevent-charcode.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/keyboardevent-charcode.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/keyboardevent-code.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/keyboardevent-code.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/keyboardevent-getmodifierstate.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/keyboardevent-getmodifierstate.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/keyboardevent-key.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/keyboardevent-key.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/keyboardevent-location.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/keyboardevent-location.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/keyboardevent-which.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/keyboardevent-which.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/lazyload.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/lazyload.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/let.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/let.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/link-icon-png.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/link-icon-png.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/link-icon-svg.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/link-icon-svg.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/link-rel-dns-prefetch.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/link-rel-dns-prefetch.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/link-rel-modulepreload.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/link-rel-modulepreload.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/link-rel-preconnect.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/link-rel-preconnect.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/link-rel-prefetch.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/link-rel-prefetch.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/link-rel-preload.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/link-rel-preload.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/link-rel-prerender.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/link-rel-prerender.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/loading-lazy-attr.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/loading-lazy-attr.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/localecompare.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/localecompare.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/magnetometer.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/magnetometer.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/matchesselector.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/matchesselector.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/matchmedia.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/matchmedia.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/mathml.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/mathml.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/maxlength.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/maxlength.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/mdn-css-backdrop-pseudo-element.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/mdn-css-backdrop-pseudo-element.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/mdn-css-unicode-bidi-isolate-override.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/mdn-css-unicode-bidi-isolate-override.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/mdn-css-unicode-bidi-isolate.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/mdn-css-unicode-bidi-isolate.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/mdn-css-unicode-bidi-plaintext.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/mdn-css-unicode-bidi-plaintext.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/mdn-text-decoration-color.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/mdn-text-decoration-color.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/mdn-text-decoration-line.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/mdn-text-decoration-line.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/mdn-text-decoration-shorthand.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/mdn-text-decoration-shorthand.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/mdn-text-decoration-style.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/mdn-text-decoration-style.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/media-fragments.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/media-fragments.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/mediacapture-fromelement.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/mediacapture-fromelement.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/mediarecorder.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/mediarecorder.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/mediasource.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/mediasource.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/menu.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/menu.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/meta-theme-color.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/meta-theme-color.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/meter.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/meter.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/midi.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/midi.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/minmaxwh.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/minmaxwh.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/mp3.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/mp3.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/mpeg-dash.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/mpeg-dash.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/mpeg4.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/mpeg4.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/multibackgrounds.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/multibackgrounds.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/multicolumn.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/multicolumn.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/mutation-events.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/mutation-events.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/mutationobserver.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/mutationobserver.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/namevalue-storage.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/namevalue-storage.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/native-filesystem-api.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/native-filesystem-api.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/nav-timing.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/nav-timing.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/netinfo.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/netinfo.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/notifications.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/notifications.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/object-entries.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/object-entries.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/object-fit.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/object-fit.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/object-observe.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/object-observe.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/object-values.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/object-values.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/objectrtc.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/objectrtc.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/offline-apps.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/offline-apps.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/offscreencanvas.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/offscreencanvas.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/ogg-vorbis.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/ogg-vorbis.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/ogv.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/ogv.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/ol-reversed.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/ol-reversed.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/once-event-listener.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/once-event-listener.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/online-status.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/online-status.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/opus.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/opus.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/orientation-sensor.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/orientation-sensor.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/outline.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/outline.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/pad-start-end.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/pad-start-end.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/page-transition-events.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/page-transition-events.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/pagevisibility.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/pagevisibility.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/passive-event-listener.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/passive-event-listener.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/passkeys.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/passkeys.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/passwordrules.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/passwordrules.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/path2d.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/path2d.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/payment-request.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/payment-request.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/pdf-viewer.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/pdf-viewer.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/permissions-api.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/permissions-api.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/permissions-policy.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/permissions-policy.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/picture-in-picture.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/picture-in-picture.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/picture.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/picture.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/ping.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/ping.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/png-alpha.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/png-alpha.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/pointer-events.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/pointer-events.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/pointer.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/pointer.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/pointerlock.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/pointerlock.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/portals.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/portals.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/prefers-color-scheme.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/prefers-color-scheme.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/prefers-reduced-motion.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/prefers-reduced-motion.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/progress.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/progress.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/promise-finally.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/promise-finally.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/promises.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/promises.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/proximity.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/proximity.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/proxy.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/proxy.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/publickeypinning.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/publickeypinning.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/push-api.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/push-api.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/queryselector.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/queryselector.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/readonly-attr.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/readonly-attr.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/referrer-policy.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/referrer-policy.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/registerprotocolhandler.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/registerprotocolhandler.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/rel-noopener.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/rel-noopener.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/rel-noreferrer.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/rel-noreferrer.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/rellist.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/rellist.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/rem.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/rem.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/requestanimationframe.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/requestanimationframe.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/requestidlecallback.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/requestidlecallback.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/resizeobserver.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/resizeobserver.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/resource-timing.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/resource-timing.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/rest-parameters.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/rest-parameters.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/rtcpeerconnection.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/rtcpeerconnection.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/ruby.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/ruby.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/run-in.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/run-in.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/same-site-cookie-attribute.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/same-site-cookie-attribute.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/screen-orientation.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/screen-orientation.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/script-async.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/script-async.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/script-defer.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/script-defer.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/scrollintoview.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/scrollintoview.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/scrollintoviewifneeded.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/scrollintoviewifneeded.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/sdch.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/sdch.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/selection-api.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/selection-api.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/selectlist.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/selectlist.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/server-timing.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/server-timing.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/serviceworkers.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/serviceworkers.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/setimmediate.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/setimmediate.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/shadowdom.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/shadowdom.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/shadowdomv1.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/shadowdomv1.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/sharedarraybuffer.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/sharedarraybuffer.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/sharedworkers.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/sharedworkers.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/sni.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/sni.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/spdy.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/spdy.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/speech-recognition.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/speech-recognition.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/speech-synthesis.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/speech-synthesis.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/spellcheck-attribute.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/spellcheck-attribute.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/sql-storage.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/sql-storage.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/srcset.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/srcset.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/stream.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/stream.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/streams.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/streams.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/stricttransportsecurity.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/stricttransportsecurity.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/style-scoped.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/style-scoped.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/subresource-bundling.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/subresource-bundling.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/subresource-integrity.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/subresource-integrity.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/svg-css.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/svg-css.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/svg-filters.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/svg-filters.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/svg-fonts.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/svg-fonts.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/svg-fragment.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/svg-fragment.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/svg-html.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/svg-html.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/svg-html5.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/svg-html5.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/svg-img.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/svg-img.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/svg-smil.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/svg-smil.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/svg.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/svg.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/sxg.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/sxg.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/tabindex-attr.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/tabindex-attr.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/template-literals.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/template-literals.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/template.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/template.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/temporal.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/temporal.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/testfeat.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/testfeat.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/text-decoration.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/text-decoration.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/text-emphasis.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/text-emphasis.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/text-overflow.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/text-overflow.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/text-size-adjust.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/text-size-adjust.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/text-stroke.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/text-stroke.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/textcontent.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/textcontent.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/textencoder.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/textencoder.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/tls1-1.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/tls1-1.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/tls1-2.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/tls1-2.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/tls1-3.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/tls1-3.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/touch.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/touch.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/transforms2d.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/transforms2d.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/transforms3d.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/transforms3d.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/trusted-types.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/trusted-types.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/ttf.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/ttf.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/typedarrays.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/typedarrays.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/u2f.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/u2f.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/unhandledrejection.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/unhandledrejection.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/upgradeinsecurerequests.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/upgradeinsecurerequests.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/url-scroll-to-text-fragment.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/url-scroll-to-text-fragment.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/url.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/url.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/urlsearchparams.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/urlsearchparams.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/use-strict.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/use-strict.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/user-select-none.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/user-select-none.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/user-timing.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/user-timing.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/variable-fonts.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/variable-fonts.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/vector-effect.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/vector-effect.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/vibration.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/vibration.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/video.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/video.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/videotracks.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/videotracks.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/view-transitions.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/view-transitions.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/viewport-unit-variants.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/viewport-unit-variants.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/viewport-units.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/viewport-units.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/wai-aria.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/wai-aria.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/wake-lock.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/wake-lock.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/wasm-bigint.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/wasm-bigint.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/wasm-bulk-memory.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/wasm-bulk-memory.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/wasm-extended-const.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/wasm-extended-const.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/wasm-gc.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/wasm-gc.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/wasm-multi-memory.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/wasm-multi-memory.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/wasm-multi-value.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/wasm-multi-value.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/wasm-mutable-globals.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/wasm-mutable-globals.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/wasm-nontrapping-fptoint.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/wasm-nontrapping-fptoint.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/wasm-reference-types.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/wasm-reference-types.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/wasm-relaxed-simd.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/wasm-relaxed-simd.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/wasm-signext.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/wasm-signext.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/wasm-simd.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/wasm-simd.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/wasm-tail-calls.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/wasm-tail-calls.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/wasm-threads.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/wasm-threads.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/wasm.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/wasm.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/wav.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/wav.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/wbr-element.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/wbr-element.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/web-animation.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/web-animation.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/web-app-manifest.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/web-app-manifest.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/web-bluetooth.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/web-bluetooth.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/web-serial.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/web-serial.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/web-share.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/web-share.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/webauthn.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/webauthn.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/webcodecs.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/webcodecs.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/webgl.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/webgl.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/webgl2.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/webgl2.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/webgpu.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/webgpu.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/webhid.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/webhid.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/webkit-user-drag.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/webkit-user-drag.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/webm.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/webm.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/webnfc.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/webnfc.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/webp.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/webp.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/websockets.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/websockets.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/webtransport.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/webtransport.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/webusb.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/webusb.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/webvr.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/webvr.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/webvtt.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/webvtt.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/webworkers.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/webworkers.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/webxr.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/webxr.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/will-change.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/will-change.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/woff.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/woff.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/woff2.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/woff2.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/word-break.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/word-break.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/wordwrap.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/wordwrap.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/x-doc-messaging.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/x-doc-messaging.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/x-frame-options.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/x-frame-options.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/xhr2.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/xhr2.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/xhtml.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/xhtml.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/xhtmlsmil.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/xhtmlsmil.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/xml-serializer.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/xml-serializer.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/zstd.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/features/zstd.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/AD.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/AD.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/AE.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/AE.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/AF.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/AF.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/AG.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/AG.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/AI.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/AI.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/AL.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/AL.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/AM.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/AM.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/AO.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/AO.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/AR.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/AR.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/AS.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/AS.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/AT.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/AT.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/AU.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/AU.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/AW.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/AW.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/AX.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/AX.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/AZ.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/AZ.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/BA.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/BA.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/BB.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/BB.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/BD.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/BD.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/BE.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/BE.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/BF.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/BF.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/BG.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/BG.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/BH.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/BH.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/BI.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/BI.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/BJ.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/BJ.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/BM.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/BM.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/BN.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/BN.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/BO.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/BO.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/BR.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/BR.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/BS.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/BS.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/BT.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/BT.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/BW.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/BW.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/BY.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/BY.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/BZ.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/BZ.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/CA.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/CA.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/CD.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/CD.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/CF.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/CF.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/CG.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/CG.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/CH.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/CH.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/CI.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/CI.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/CK.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/CK.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/CL.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/CL.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/CM.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/CM.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/CN.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/CN.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/CO.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/CO.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/CR.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/CR.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/CU.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/CU.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/CV.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/CV.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/CX.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/CX.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/CY.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/CY.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/CZ.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/CZ.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/DE.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/DE.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/DJ.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/DJ.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/DK.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/DK.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/DM.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/DM.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/DO.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/DO.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/DZ.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/DZ.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/EC.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/EC.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/EE.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/EE.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/EG.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/EG.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/ER.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/ER.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/ES.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/ES.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/ET.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/ET.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/FI.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/FI.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/FJ.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/FJ.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/FK.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/FK.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/FM.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/FM.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/FO.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/FO.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/FR.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/FR.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/GA.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/GA.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/GB.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/GB.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/GD.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/GD.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/GE.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/GE.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/GF.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/GF.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/GG.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/GG.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/GH.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/GH.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/GI.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/GI.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/GL.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/GL.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/GM.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/GM.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/GN.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/GN.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/GP.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/GP.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/GQ.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/GQ.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/GR.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/GR.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/GT.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/GT.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/GU.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/GU.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/GW.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/GW.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/GY.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/GY.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/HK.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/HK.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/HN.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/HN.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/HR.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/HR.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/HT.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/HT.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/HU.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/HU.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/ID.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/ID.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/IE.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/IE.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/IL.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/IL.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/IM.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/IM.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/IN.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/IN.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/IQ.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/IQ.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/IR.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/IR.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/IS.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/IS.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/IT.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/IT.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/JE.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/JE.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/JM.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/JM.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/JO.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/JO.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/JP.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/JP.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/KE.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/KE.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/KG.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/KG.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/KH.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/KH.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/KI.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/KI.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/KM.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/KM.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/KN.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/KN.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/KP.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/KP.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/KR.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/KR.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/KW.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/KW.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/KY.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/KY.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/KZ.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/KZ.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/LA.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/LA.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/LB.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/LB.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/LC.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/LC.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/LI.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/LI.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/LK.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/LK.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/LR.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/LR.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/LS.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/LS.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/LT.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/LT.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/LU.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/LU.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/LV.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/LV.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/LY.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/LY.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/MA.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/MA.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/MC.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/MC.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/MD.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/MD.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/ME.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/ME.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/MG.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/MG.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/MH.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/MH.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/MK.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/MK.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/ML.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/ML.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/MM.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/MM.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/MN.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/MN.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/MO.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/MO.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/MP.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/MP.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/MQ.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/MQ.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/MR.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/MR.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/MS.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/MS.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/MT.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/MT.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/MU.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/MU.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/MV.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/MV.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/MW.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/MW.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/MX.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/MX.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/MY.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/MY.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/MZ.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/MZ.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/NA.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/NA.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/NC.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/NC.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/NE.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/NE.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/NF.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/NF.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/NG.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/NG.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/NI.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/NI.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/NL.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/NL.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/NO.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/NO.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/NP.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/NP.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/NR.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/NR.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/NU.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/NU.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/NZ.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/NZ.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/OM.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/OM.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/PA.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/PA.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/PE.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/PE.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/PF.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/PF.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/PG.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/PG.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/PH.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/PH.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/PK.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/PK.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/PL.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/PL.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/PM.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/PM.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/PN.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/PN.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/PR.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/PR.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/PS.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/PS.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/PT.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/PT.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/PW.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/PW.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/PY.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/PY.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/QA.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/QA.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/RE.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/RE.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/RO.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/RO.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/RS.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/RS.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/RU.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/RU.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/RW.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/RW.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/SA.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/SA.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/SB.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/SB.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/SC.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/SC.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/SD.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/SD.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/SE.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/SE.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/SG.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/SG.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/SH.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/SH.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/SI.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/SI.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/SK.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/SK.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/SL.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/SL.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/SM.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/SM.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/SN.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/SN.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/SO.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/SO.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/SR.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/SR.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/ST.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/ST.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/SV.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/SV.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/SY.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/SY.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/SZ.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/SZ.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/TC.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/TC.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/TD.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/TD.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/TG.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/TG.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/TH.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/TH.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/TJ.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/TJ.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/TL.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/TL.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/TM.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/TM.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/TN.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/TN.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/TO.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/TO.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/TR.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/TR.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/TT.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/TT.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/TV.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/TV.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/TW.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/TW.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/TZ.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/TZ.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/UA.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/UA.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/UG.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/UG.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/US.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/US.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/UY.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/UY.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/UZ.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/UZ.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/VA.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/VA.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/VC.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/VC.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/VE.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/VE.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/VG.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/VG.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/VI.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/VI.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/VN.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/VN.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/VU.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/VU.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/WF.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/WF.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/WS.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/WS.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/YE.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/YE.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/YT.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/YT.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/ZA.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/ZA.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/ZM.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/ZM.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/ZW.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/ZW.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/alt-af.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/alt-af.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/alt-an.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/alt-an.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/alt-as.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/alt-as.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/alt-eu.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/alt-eu.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/alt-na.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/alt-na.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/alt-oc.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/alt-oc.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/alt-sa.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/alt-sa.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/alt-ww.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/data/regions/alt-ww.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/package.json" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/caniuse-lite/package.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/electron-to-chromium/full-chromium-versions.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/electron-to-chromium/full-chromium-versions.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/electron-to-chromium/full-chromium-versions.json" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/electron-to-chromium/full-chromium-versions.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/electron-to-chromium/full-versions.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/electron-to-chromium/full-versions.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/electron-to-chromium/full-versions.json" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/electron-to-chromium/full-versions.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/electron-to-chromium/package.json" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/electron-to-chromium/package.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/electron-to-chromium/versions.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/electron-to-chromium/versions.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/electron-to-chromium/versions.json" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/electron-to-chromium/versions.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/emoji-regex/README.md" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/emoji-regex/README.md" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/emoji-regex/RGI_Emoji.d.ts" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/emoji-regex/RGI_Emoji.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/emoji-regex/es2015/RGI_Emoji.d.ts" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/emoji-regex/es2015/RGI_Emoji.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/emoji-regex/es2015/index.d.ts" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/emoji-regex/es2015/index.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/emoji-regex/es2015/index.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/emoji-regex/es2015/text.d.ts" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/emoji-regex/es2015/text.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/emoji-regex/es2015/text.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/emoji-regex/index.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/emoji-regex/index.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/emoji-regex/index.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/emoji-regex/index.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/emoji-regex/package.json" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/emoji-regex/package.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/emoji-regex/text.d.ts" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/emoji-regex/text.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/emoji-regex/text.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/rollup/dist/bin/rollup" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/rollup/dist/bin/rollup" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/rollup/dist/es/getLogFilter.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/rollup/dist/es/getLogFilter.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/rollup/dist/es/parseAst.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/rollup/dist/es/parseAst.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/rollup/dist/es/rollup.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/rollup/dist/es/rollup.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/rollup/dist/es/shared/node-entry.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/rollup/dist/es/shared/node-entry.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/rollup/dist/es/shared/parseAst.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/rollup/dist/es/shared/parseAst.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/rollup/dist/es/shared/watch.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/rollup/dist/es/shared/watch.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/rollup/dist/getLogFilter.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/rollup/dist/getLogFilter.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/rollup/dist/loadConfigFile.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/rollup/dist/loadConfigFile.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/rollup/dist/parseAst.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/rollup/dist/parseAst.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/rollup/dist/rollup.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/rollup/dist/rollup.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/rollup/dist/rollup.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/rollup/dist/rollup.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/rollup/dist/shared/fsevents-importer.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/rollup/dist/shared/fsevents-importer.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/rollup/dist/shared/index.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/rollup/dist/shared/index.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/rollup/dist/shared/loadConfigFile.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/rollup/dist/shared/loadConfigFile.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/rollup/dist/shared/parseAst.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/rollup/dist/shared/parseAst.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/rollup/dist/shared/rollup.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/rollup/dist/shared/rollup.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/rollup/dist/shared/watch-cli.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/rollup/dist/shared/watch-cli.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/rollup/dist/shared/watch.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/rollup/dist/shared/watch.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/rollup/package.json" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/rollup/package.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/string-width-cjs/node_modules/emoji-regex/LICENSE-MIT.txt" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/string-width-cjs/node_modules/emoji-regex/README.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/string-width-cjs/node_modules/emoji-regex/es2015/index.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/string-width-cjs/node_modules/emoji-regex/es2015/text.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/string-width-cjs/node_modules/emoji-regex/index.d.ts" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/string-width-cjs/node_modules/emoji-regex/index.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/string-width-cjs/node_modules/emoji-regex/package.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/string-width-cjs/node_modules/emoji-regex/text.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/string-width/index.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/string-width/index.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/string-width/index.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/string-width/index.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/string-width/license" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/string-width/license" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/string-width/node_modules/ansi-regex/index.d.ts" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/string-width/node_modules/ansi-regex/index.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/string-width/node_modules/ansi-regex/license" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/string-width/node_modules/ansi-regex/package.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/string-width/node_modules/ansi-regex/readme.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/string-width/node_modules/strip-ansi/index.d.ts" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/string-width/node_modules/strip-ansi/index.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/string-width/node_modules/strip-ansi/license" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/string-width/node_modules/strip-ansi/package.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/string-width/node_modules/strip-ansi/readme.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/string-width/package.json" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/string-width/package.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/string-width/readme.md" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/string-width/readme.md" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/type-fest/base.d.ts" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/type-fest/index.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/type-fest/index.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/type-fest/license" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/type-fest/package.json" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/type-fest/package.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/type-fest/readme.md" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/type-fest/readme.md" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/type-fest/source/async-return-type.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/type-fest/source/async-return-type.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/type-fest/source/asyncify.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/type-fest/source/asyncify.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/type-fest/source/basic.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/type-fest/source/basic.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/type-fest/source/conditional-except.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/type-fest/source/conditional-except.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/type-fest/source/conditional-keys.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/type-fest/source/conditional-keys.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/type-fest/source/conditional-pick.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/type-fest/source/conditional-pick.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/type-fest/source/entries.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/type-fest/source/entries.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/type-fest/source/entry.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/type-fest/source/entry.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/type-fest/source/except.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/type-fest/source/except.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/type-fest/source/fixed-length-array.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/type-fest/source/fixed-length-array.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/type-fest/source/iterable-element.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/type-fest/source/iterable-element.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/type-fest/source/literal-union.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/type-fest/source/literal-union.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/type-fest/source/merge-exclusive.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/type-fest/source/merge-exclusive.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/type-fest/source/merge.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/type-fest/source/merge.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/type-fest/source/mutable.d.ts" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/type-fest/source/opaque.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/type-fest/source/opaque.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/type-fest/source/package-json.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/type-fest/source/package-json.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/type-fest/source/partial-deep.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/type-fest/source/partial-deep.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/type-fest/source/promisable.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/type-fest/source/promisable.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/type-fest/source/promise-value.d.ts" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/type-fest/source/readonly-deep.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/type-fest/source/readonly-deep.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/type-fest/source/require-at-least-one.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/type-fest/source/require-at-least-one.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/type-fest/source/require-exactly-one.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/type-fest/source/require-exactly-one.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/type-fest/source/set-optional.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/type-fest/source/set-optional.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/type-fest/source/set-required.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/type-fest/source/set-required.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/type-fest/source/set-return-type.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/type-fest/source/set-return-type.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/type-fest/source/stringified.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/type-fest/source/stringified.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/type-fest/source/tsconfig-json.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/type-fest/source/tsconfig-json.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/type-fest/source/union-to-intersection.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/type-fest/source/union-to-intersection.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/type-fest/source/utilities.d.ts" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/type-fest/source/value-of.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/type-fest/source/value-of.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/type-fest/ts41/camel-case.d.ts" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/type-fest/ts41/delimiter-case.d.ts" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/type-fest/ts41/index.d.ts" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/type-fest/ts41/kebab-case.d.ts" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/type-fest/ts41/pascal-case.d.ts" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/type-fest/ts41/snake-case.d.ts" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/typescript/lib/_tsc.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/typescript/lib/_tsc.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/typescript/lib/typescript.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/typescript/lib/typescript.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/typescript/package.json" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/typescript/package.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/vite/dist/node/chunks/dep-C3azpbs2.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/vite/dist/node/chunks/dep-DN5F0dfg.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/vite/dist/node/chunks/dep-U2-FgckH.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/vite/dist/node/cli.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/vite/dist/node/cli.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/vite/dist/node/index.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/vite/dist/node/index.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/vite/package.json" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/vite/package.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/wrap-ansi-cjs/node_modules/emoji-regex/LICENSE-MIT.txt" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/wrap-ansi-cjs/node_modules/emoji-regex/README.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/wrap-ansi-cjs/node_modules/emoji-regex/es2015/index.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/wrap-ansi-cjs/node_modules/emoji-regex/es2015/text.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/wrap-ansi-cjs/node_modules/emoji-regex/index.d.ts" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/wrap-ansi-cjs/node_modules/emoji-regex/index.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/wrap-ansi-cjs/node_modules/emoji-regex/package.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/wrap-ansi-cjs/node_modules/emoji-regex/text.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/wrap-ansi-cjs/node_modules/string-width/index.d.ts" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/wrap-ansi-cjs/node_modules/string-width/index.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/wrap-ansi-cjs/node_modules/string-width/license" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/wrap-ansi-cjs/node_modules/string-width/package.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/wrap-ansi-cjs/node_modules/string-width/readme.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/wrap-ansi/index.d.ts" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/wrap-ansi/index.js" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/wrap-ansi/index.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/wrap-ansi/node_modules/ansi-regex/index.d.ts" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/wrap-ansi/node_modules/ansi-regex/index.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/wrap-ansi/node_modules/ansi-regex/license" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/wrap-ansi/node_modules/ansi-regex/package.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/wrap-ansi/node_modules/ansi-regex/readme.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/wrap-ansi/node_modules/ansi-styles/index.d.ts" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/wrap-ansi/node_modules/ansi-styles/index.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/wrap-ansi/node_modules/ansi-styles/license" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/wrap-ansi/node_modules/ansi-styles/package.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/wrap-ansi/node_modules/ansi-styles/readme.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/wrap-ansi/node_modules/strip-ansi/index.d.ts" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/wrap-ansi/node_modules/strip-ansi/index.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/wrap-ansi/node_modules/strip-ansi/license" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/wrap-ansi/node_modules/strip-ansi/package.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/wrap-ansi/node_modules/strip-ansi/readme.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/wrap-ansi/package.json" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/wrap-ansi/package.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/node_modules/wrap-ansi/readme.md" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/node_modules/wrap-ansi/readme.md" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/package-lock.json" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/package-lock.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/package.json" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/package.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/postcss.config.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/src/App.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/src/App.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/src/components/Footer.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/src/components/Footer.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/src/components/Header.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/src/components/Header.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/src/index.css" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/src/index.css" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/src/screens/HomeScreen.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/src/screens/HomeScreen.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/src/screens/LoginScreen.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/src/screens/LoginScreen.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/src/screens/ProductDetailScreen.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/src/screens/ProductDetailScreen.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/src/screens/ProductListScreen.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/src/screens/ProductListScreen.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/src/screens/RegisterScreen.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/src/screens/RegisterScreen.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/tailwind.config.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/tsconfig.json" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/tsconfig.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../frontend/vite.config.ts" beforeDir="false" afterPath="$PROJECT_DIR$/../../frontend/vite.config.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../gradle.properties" beforeDir="false" afterPath="$PROJECT_DIR$/../../gradle.properties" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../gradle/libs.versions.toml" beforeDir="false" afterPath="$PROJECT_DIR$/../../gradle/libs.versions.toml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../node_modules/.package-lock.json" beforeDir="false" afterPath="$PROJECT_DIR$/../../node_modules/.package-lock.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../package-lock.json" beforeDir="false" afterPath="$PROJECT_DIR$/../../package-lock.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../package.json" beforeDir="false" afterPath="$PROJECT_DIR$/../../package.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../settings.gradle.kts" beforeDir="false" afterPath="$PROJECT_DIR$/../../settings.gradle.kts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../src/context/SubscriptionContext.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/../../src/context/SubscriptionContext.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../src/data/subscriptionData.ts" beforeDir="false" afterPath="$PROJECT_DIR$/../../src/data/subscriptionData.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../src/types/subscription.ts" beforeDir="false" afterPath="$PROJECT_DIR$/../../src/types/subscription.ts" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ClangdSettings">
    <option name="formatViaClangd" value="false" />
  </component>
  <component name="FileEditorManager">
    <leaf>
      <file leaf-file-name="main.dart" pinned="false" current-in-tab="true">
        <entry file="file://$PROJECT_DIR$/lib/main.dart">
          <provider selected="true" editor-type-id="text-editor">
            <state relative-caret-position="0">
              <caret line="0" column="0" lean-forward="false" selection-start-line="0" selection-start-column="0" selection-end-line="0" selection-end-column="0" />
            </state>
          </provider>
        </entry>
      </file>
    </leaf>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$/../.." />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 6
}</component>
  <component name="ProjectId" id="2xyogWrBf9UsGjghdxws9szgoFn" />
  <component name="ProjectView">
    <navigator currentView="ProjectPane" proportions="" version="1" />
    <panes>
      <pane id="ProjectPane">
        <option name="show-excluded-files" value="false" />
      </pane>
    </panes>
  </component>
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "Flutter.main.dart.executor": "Debug",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.cidr.known.project.marker": "true",
    "RunOnceActivity.git.unshallow": "true",
    "RunOnceActivity.readMode.enableVisualFormatting": "true",
    "ScreenshotViewer.SavePath": "D:/",
    "cf.first.check.clang-format": "false",
    "cidr.known.project.marker": "true",
    "git-widget-placeholder": "master",
    "ignore.virus.scanning.warn.message": "true",
    "io.flutter.reload.alreadyRun": "true",
    "kotlin-language-version-configured": "true",
    "last_opened_file_path": "D:/",
    "project.structure.last.edited": "Problems",
    "project.structure.proportion": "0.0",
    "project.structure.side.proportion": "0.2",
    "settings.editor.selected.configurable": "com.google.gct.login2.settings.GoogleLoginApplicationSettingsConfigurableProvider",
    "show.migrate.to.gradle.popup": "false"
  }
}]]></component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="e036299c-37a7-429d-a303-4de6049518ca" name="Changes" comment="" />
      <created>1748919786194</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1748919786194</updated>
    </task>
    <servers />
  </component>
  <component name="ToolWindowManager">
    <editor active="true" />
    <layout>
      <window_info id="Project" active="false" anchor="left" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="true" show_stripe_button="true" weight="0.25" sideWeight="0.5" order="0" side_tool="false" content_ui="combo" />
    </layout>
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" type="Dart">
          <url>file://$PROJECT_DIR$/../../../flutter/packages/flutter/lib/src/widgets/framework.dart</url>
          <line>5309</line>
          <option name="timeStamp" value="1" />
        </line-breakpoint>
      </breakpoints>
    </breakpoint-manager>
  </component>
</project>