import 'package:flutter/foundation.dart';
import '../data/plant_dataset.dart';
import '../providers/app_state.dart';
import 'ai_learning_engine.dart';

/// Advanced holistic and naturopathic AI assistant service
class HolisticAIService {
  static final HolisticAIService _instance = HolisticAIService._internal();
  factory HolisticAIService() => _instance;
  HolisticAIService._internal();



  /// Generate comprehensive holistic response to medical questions with learning
  Future<String> generateHolisticResponse(String userMessage) async {
    try {
      // Use enhanced learning engine for analysis if available
      try {
        final learningEngine = AILearningEngine();
        final enhancedAnalysis = await learningEngine.analyzeAndLearn(userMessage, null);

        // Generate response using enhanced analysis
        final response = await _generateEnhancedResponse(enhancedAnalysis);

        // Add safety disclaimer
        final safeResponse = _addEnhancedSafetyDisclaimer(response, enhancedAnalysis);

        return safeResponse;
      } catch (learningError) {
        debugPrint('Learning engine unavailable, using basic analysis: $learningError');

        // Fallback to basic analysis
        final analysis = await _analyzeUserQuery(userMessage);
        final response = await _generateResponse(analysis);
        final safeResponse = _addSafetyDisclaimer(response, analysis);

        return safeResponse;
      }
    } catch (e) {
      debugPrint('Error generating holistic response: $e');
      return _getDefaultResponse();
    }
  }

  /// Generate response using enhanced learning analysis
  Future<String> _generateEnhancedResponse(dynamic enhancedAnalysis) async {
    final responseBuilder = StringBuffer();

    // Add personalized greeting based on enhanced analysis
    responseBuilder.writeln(_getEnhancedPersonalizedGreeting(enhancedAnalysis));
    responseBuilder.writeln();

    // Add main holistic recommendations
    final recommendations = await _getEnhancedHolisticRecommendations(enhancedAnalysis);
    responseBuilder.writeln(recommendations);
    responseBuilder.writeln();

    // Add specific plant recommendations
    if (enhancedAnalysis.identifiedPlants.isNotEmpty) {
      final plantRecommendations = await _getEnhancedPlantRecommendations(enhancedAnalysis);
      responseBuilder.writeln(plantRecommendations);
      responseBuilder.writeln();
    }

    // Add lifestyle recommendations
    final lifestyleAdvice = _getEnhancedLifestyleRecommendations(enhancedAnalysis);
    responseBuilder.writeln(lifestyleAdvice);
    responseBuilder.writeln();

    // Add nutritional guidance
    final nutritionAdvice = _getEnhancedNutritionalGuidance(enhancedAnalysis);
    responseBuilder.writeln(nutritionAdvice);
    responseBuilder.writeln();

    // Add mind-body practices
    final mindBodyPractices = _getEnhancedMindBodyPractices(enhancedAnalysis);
    responseBuilder.writeln(mindBodyPractices);

    // Add follow-up suggestions if available
    if (enhancedAnalysis.suggestedFollowUps.isNotEmpty) {
      responseBuilder.writeln();
      responseBuilder.writeln('💭 **Follow-up Questions:**');
      for (final suggestion in enhancedAnalysis.suggestedFollowUps.take(2)) {
        responseBuilder.writeln('• $suggestion');
      }
    }

    return responseBuilder.toString().trim();
  }

  /// Get enhanced personalized greeting
  String _getEnhancedPersonalizedGreeting(dynamic analysis) {
    if (analysis.urgencyLevel.toString().contains('emergency')) {
      return '🚨 **For emergency situations, please contact emergency services immediately (911) or visit your nearest emergency room.**';
    } else if (analysis.detectedConditions.isNotEmpty) {
      final conditions = analysis.detectedConditions.map((c) => c.name).join(" and ");
      return 'I understand you\'re seeking natural support for $conditions. Here\'s a comprehensive holistic approach based on my enhanced analysis:';
    } else if (analysis.detectedSymptoms.isNotEmpty) {
      final symptoms = analysis.detectedSymptoms.map((s) => s.name).join(" and ");
      return 'I can help you explore natural approaches for $symptoms. Here\'s what I recommend based on my analysis:';
    } else {
      return 'I\'m here to provide enhanced holistic and naturopathic guidance. Here\'s a comprehensive approach:';
    }
  }

  /// Get enhanced holistic recommendations
  Future<String> _getEnhancedHolisticRecommendations(dynamic analysis) async {
    final recommendations = StringBuffer();
    recommendations.writeln('🌿 **Enhanced Holistic Approach:**');

    // Use the most confident detected condition for recommendations
    if (analysis.detectedConditions.isNotEmpty) {
      final primaryCondition = analysis.detectedConditions.first;
      final category = _mapConditionToCategory(primaryCondition.name);

      switch (category) {
        case 'digestive_health':
          recommendations.writeln('Focus on gut health through probiotics, digestive enzymes, and healing herbs like chamomile and peppermint.');
          break;
        case 'immune_support':
          recommendations.writeln('Strengthen immunity with elderberry, echinacea, vitamin C, and adaptogenic herbs.');
          break;
        case 'stress_anxiety':
          recommendations.writeln('Calm the nervous system with ashwagandha, lemon balm, and magnesium supplementation.');
          break;
        case 'sleep_disorders':
          recommendations.writeln('Promote restful sleep with valerian, chamomile, and melatonin support.');
          break;
        case 'pain_inflammation':
          recommendations.writeln('Reduce inflammation naturally with turmeric, ginger, and omega-3 fatty acids.');
          break;
        default:
          recommendations.writeln('Support overall wellness with a balanced approach to nutrition, movement, and stress management.');
      }
    } else {
      recommendations.writeln('Support overall wellness with a balanced approach to nutrition, movement, and stress management.');
    }

    // Add confidence indicator
    final confidence = analysis.confidenceScores['overall'] ?? 0.0;
    if (confidence > 0.8) {
      recommendations.writeln('\n✅ **High Confidence Analysis** - These recommendations are well-matched to your query.');
    } else if (confidence > 0.6) {
      recommendations.writeln('\n⚡ **Good Analysis Match** - These recommendations should be helpful for your situation.');
    } else {
      recommendations.writeln('\n💡 **General Guidance** - Consider consulting with a healthcare provider for personalized advice.');
    }

    return recommendations.toString();
  }

  /// Map condition name to category
  String _mapConditionToCategory(String conditionName) {
    final conditionMap = {
      'diabetes': 'hormonal_balance',
      'hypertension': 'cardiovascular',
      'arthritis': 'pain_inflammation',
      'depression': 'mental_health',
      'anxiety': 'stress_anxiety',
      'insomnia': 'sleep_disorders',
      'ibs': 'digestive_health',
      'migraine': 'pain_inflammation',
      'eczema': 'skin_conditions',
      'asthma': 'respiratory_health',
    };

    return conditionMap[conditionName] ?? 'general_wellness';
  }

  /// Get enhanced plant recommendations
  Future<String> _getEnhancedPlantRecommendations(dynamic analysis) async {
    final recommendations = StringBuffer();
    recommendations.writeln('🌱 **AI-Recommended Plants & Herbs:**');

    final relevantPlants = analysis.identifiedPlants.take(3);

    for (final identifiedPlant in relevantPlants) {
      final plant = identifiedPlant.plant;
      final confidence = identifiedPlant.confidence;

      recommendations.writeln();
      recommendations.writeln('**${plant.name}** *(${plant.scientificName})* - ${(confidence * 100).toStringAsFixed(0)}% match');
      recommendations.writeln('• **Benefits**: ${plant.benefits.join(", ")}');
      recommendations.writeln('• **Usage**: Use as tea, tincture, or supplement as directed.');
      recommendations.writeln('• **Dosage**: ${_getPlantDosageRecommendations(plant)}');
      recommendations.writeln('• **Preparation**: ${_getPreparationMethods(plant)}');

      if (confidence > 0.8) {
        recommendations.writeln('• **AI Note**: Highly recommended based on your query analysis');
      }
    }

    return recommendations.toString();
  }

  /// Get enhanced lifestyle recommendations
  String _getEnhancedLifestyleRecommendations(dynamic analysis) {
    final recommendations = StringBuffer();
    recommendations.writeln('🏃‍♀️ **Personalized Lifestyle Modifications:**');

    // Base recommendations on detected conditions and symptoms
    final hasStressSymptoms = analysis.detectedSymptoms.any((s) =>
      s.name.contains('anxiety') || s.name.contains('stress'));
    final hasSleepIssues = analysis.detectedSymptoms.any((s) =>
      s.name.contains('sleep') || s.name.contains('fatigue'));
    final hasPainSymptoms = analysis.detectedSymptoms.any((s) =>
      s.name.contains('pain') || s.name.contains('ache'));

    if (hasStressSymptoms) {
      recommendations.writeln('• **Stress Management**: Daily meditation (10-20 minutes)');
      recommendations.writeln('• **Breathing Exercises**: 4-7-8 breathing technique');
      recommendations.writeln('• **Nature Therapy**: Spend 20+ minutes outdoors daily');
    }

    if (hasSleepIssues) {
      recommendations.writeln('• **Sleep Hygiene**: Consistent bedtime routine');
      recommendations.writeln('• **Digital Detox**: No screens 1 hour before bed');
      recommendations.writeln('• **Environment**: Cool, dark, quiet bedroom');
    }

    if (hasPainSymptoms) {
      recommendations.writeln('• **Gentle Movement**: Tai chi, yoga, or walking');
      recommendations.writeln('• **Heat/Cold Therapy**: Alternating temperatures');
      recommendations.writeln('• **Posture**: Ergonomic workspace setup');
    }

    // General recommendations
    recommendations.writeln('• **Hydration**: 8-10 glasses of pure water daily');
    recommendations.writeln('• **Movement**: Regular, moderate exercise');
    recommendations.writeln('• **Social Connection**: Maintain supportive relationships');

    return recommendations.toString();
  }

  /// Get enhanced nutritional guidance
  String _getEnhancedNutritionalGuidance(dynamic analysis) {
    final recommendations = StringBuffer();
    recommendations.writeln('🥗 **Targeted Nutritional Support:**');

    // Customize based on detected conditions
    final hasInflammation = analysis.detectedConditions.any((c) =>
      c.name.contains('arthritis') || c.name.contains('pain'));
    final hasDigestiveIssues = analysis.detectedConditions.any((c) =>
      c.name.contains('ibs') || c.name.contains('digestive'));
    final hasImmuneNeeds = analysis.detectedSymptoms.any((s) =>
      s.name.contains('infection') || s.name.contains('cold'));

    if (hasInflammation) {
      recommendations.writeln('• **Anti-Inflammatory Protocol**:');
      recommendations.writeln('  - Fatty fish 3x weekly (omega-3s)');
      recommendations.writeln('  - Colorful berries daily (antioxidants)');
      recommendations.writeln('  - Turmeric with black pepper');
      recommendations.writeln('  - Avoid: processed foods, excess sugar');
    }

    if (hasDigestiveIssues) {
      recommendations.writeln('• **Gut Healing Protocol**:');
      recommendations.writeln('  - Fermented foods daily (probiotics)');
      recommendations.writeln('  - Bone broth 2-3x weekly');
      recommendations.writeln('  - Prebiotic foods (garlic, onions, asparagus)');
      recommendations.writeln('  - Avoid: food triggers, NSAIDs');
    }

    if (hasImmuneNeeds) {
      recommendations.writeln('• **Immune Support Protocol**:');
      recommendations.writeln('  - Vitamin C rich foods (citrus, berries)');
      recommendations.writeln('  - Zinc sources (pumpkin seeds, nuts)');
      recommendations.writeln('  - Medicinal mushrooms (shiitake, reishi)');
      recommendations.writeln('  - Adequate protein for antibody production');
    }

    // General nutritional wisdom
    recommendations.writeln('• **Foundation**: Whole foods, minimal processing');
    recommendations.writeln('• **Timing**: Regular meals to support circadian rhythm');
    recommendations.writeln('• **Quality**: Organic when possible, especially for herbs');

    return recommendations.toString();
  }

  /// Get enhanced mind-body practices
  String _getEnhancedMindBodyPractices(dynamic analysis) {
    final recommendations = StringBuffer();
    recommendations.writeln('🧘‍♀️ **Personalized Mind-Body Practices:**');

    // Customize based on urgency and symptoms
    final urgencyLevel = analysis.urgencyLevel.toString();
    final hasAnxiety = analysis.detectedSymptoms.any((s) => s.name.contains('anxiety'));
    final hasPain = analysis.detectedSymptoms.any((s) => s.name.contains('pain'));

    if (urgencyLevel.contains('high') || hasAnxiety) {
      recommendations.writeln('• **Immediate Relief**:');
      recommendations.writeln('  - 4-7-8 breathing (inhale 4, hold 7, exhale 8)');
      recommendations.writeln('  - Progressive muscle relaxation');
      recommendations.writeln('  - Grounding techniques (5-4-3-2-1 method)');
    }

    if (hasPain) {
      recommendations.writeln('• **Pain Management**:');
      recommendations.writeln('  - Guided imagery for pain relief');
      recommendations.writeln('  - Gentle yoga or stretching');
      recommendations.writeln('  - Mindfulness meditation');
    }

    // Long-term practices
    recommendations.writeln('• **Daily Practices**:');
    recommendations.writeln('  - Morning meditation (5-20 minutes)');
    recommendations.writeln('  - Gratitude journaling (3 things daily)');
    recommendations.writeln('  - Evening reflection and intention setting');

    // Add confidence-based recommendations
    final confidence = analysis.confidenceScores['overall'] ?? 0.0;
    if (confidence > 0.7) {
      recommendations.writeln('• **AI Insight**: These practices are specifically chosen based on your query patterns');
    }

    return recommendations.toString();
  }

  /// Add enhanced safety disclaimer
  String _addEnhancedSafetyDisclaimer(String response, dynamic analysis) {
    final urgencyLevel = analysis.urgencyLevel.toString();
    final confidence = analysis.confidenceScores['overall'] ?? 0.0;

    String disclaimer = '';

    if (urgencyLevel.contains('emergency')) {
      disclaimer = '''

🚨 **EMERGENCY MEDICAL DISCLAIMER**
This appears to be an emergency situation. Please contact emergency services immediately (911) or visit your nearest emergency room. The information provided is for educational purposes only and should not delay emergency medical care.''';
    } else if (urgencyLevel.contains('high') || analysis.detectedConditions.isNotEmpty) {
      disclaimer = '''

⚠️ **IMPORTANT MEDICAL DISCLAIMER**
This AI-enhanced analysis is for educational purposes only and should not replace professional medical advice. For serious symptoms, persistent conditions, or if you're taking medications, please consult with a qualified healthcare provider. Always inform your doctor about any herbs or supplements you're considering.

**AI Confidence Level**: ${(confidence * 100).toStringAsFixed(0)}% - ${confidence > 0.7 ? 'High confidence in analysis' : 'Consider professional consultation for personalized advice'}''';
    } else {
      disclaimer = '''

💡 **Enhanced Health Information Notice**
This AI-powered guidance combines traditional wisdom with modern research. Individual responses may vary. Please consult with a healthcare professional before starting any new herbal regimen.

**Analysis Confidence**: ${(confidence * 100).toStringAsFixed(0)}%''';
    }

    return response + disclaimer;
  }

  /// Analyze user query to understand intent and medical context
  Future<QueryAnalysis> _analyzeUserQuery(String message) async {
    final lowerMessage = message.toLowerCase();
    
    // Detect medical conditions
    final conditions = _detectMedicalConditions(lowerMessage);
    
    // Detect symptoms
    final symptoms = _detectSymptoms(lowerMessage);
    
    // Detect plants/herbs mentioned
    final plants = _detectMentionedPlants(lowerMessage);
    
    // Determine urgency level
    final urgency = _assessUrgency(lowerMessage);
    
    // Categorize the query
    final category = _categorizeQuery(lowerMessage, conditions, symptoms);
    
    return QueryAnalysis(
      originalMessage: message,
      detectedConditions: conditions,
      detectedSymptoms: symptoms,
      mentionedPlants: plants,
      urgencyLevel: urgency,
      category: category,
      requiresDisclaimer: _requiresMedicalDisclaimer(lowerMessage),
    );
  }

  /// Generate comprehensive holistic response
  Future<String> _generateResponse(QueryAnalysis analysis) async {
    final responseBuilder = StringBuffer();
    
    // Add personalized greeting
    responseBuilder.writeln(_getPersonalizedGreeting(analysis));
    responseBuilder.writeln();
    
    // Add main holistic recommendations
    final recommendations = await _getHolisticRecommendations(analysis);
    responseBuilder.writeln(recommendations);
    responseBuilder.writeln();
    
    // Add specific plant recommendations
    if (analysis.detectedConditions.isNotEmpty || analysis.detectedSymptoms.isNotEmpty) {
      final plantRecommendations = await _getPlantRecommendations(analysis);
      responseBuilder.writeln(plantRecommendations);
      responseBuilder.writeln();
    }
    
    // Add lifestyle recommendations
    final lifestyleAdvice = _getLifestyleRecommendations(analysis);
    responseBuilder.writeln(lifestyleAdvice);
    responseBuilder.writeln();
    
    // Add nutritional guidance
    final nutritionAdvice = _getNutritionalGuidance(analysis);
    responseBuilder.writeln(nutritionAdvice);
    responseBuilder.writeln();
    
    // Add mind-body practices
    final mindBodyPractices = _getMindBodyPractices(analysis);
    responseBuilder.writeln(mindBodyPractices);
    
    return responseBuilder.toString().trim();
  }

  /// Get holistic recommendations based on analysis
  Future<String> _getHolisticRecommendations(QueryAnalysis analysis) async {
    final recommendations = StringBuffer();
    recommendations.writeln('🌿 **Holistic Approach:**');
    
    switch (analysis.category) {
      case 'digestive_health':
        recommendations.writeln(_getDigestiveHealthRecommendations(analysis));
        break;
      case 'immune_support':
        recommendations.writeln(_getImmuneSystemRecommendations(analysis));
        break;
      case 'stress_anxiety':
        recommendations.writeln(_getStressAnxietyRecommendations(analysis));
        break;
      case 'sleep_disorders':
        recommendations.writeln(_getSleepDisorderRecommendations(analysis));
        break;
      case 'pain_inflammation':
        recommendations.writeln(_getPainInflammationRecommendations(analysis));
        break;
      case 'respiratory_health':
        recommendations.writeln(_getRespiratoryHealthRecommendations(analysis));
        break;
      case 'hormonal_balance':
        recommendations.writeln(_getHormonalBalanceRecommendations(analysis));
        break;
      case 'skin_conditions':
        recommendations.writeln(_getSkinConditionRecommendations(analysis));
        break;
      default:
        recommendations.writeln(_getGeneralWellnessRecommendations(analysis));
    }
    
    return recommendations.toString();
  }

  /// Get specific plant recommendations
  Future<String> _getPlantRecommendations(QueryAnalysis analysis) async {
    final recommendations = StringBuffer();
    recommendations.writeln('🌱 **Recommended Plants & Herbs:**');
    
    final relevantPlants = await _findRelevantPlants(analysis);
    
    for (final plant in relevantPlants.take(3)) {
      recommendations.writeln();
      recommendations.writeln('**${plant.name}** *(${plant.scientificName})*');
      recommendations.writeln('• **Benefits**: ${plant.benefits.join(", ")}');
      recommendations.writeln('• **Usage**: ${_getPlantUsageInstructions(plant, analysis)}');
      recommendations.writeln('• **Dosage**: ${_getPlantDosageRecommendations(plant)}');
      recommendations.writeln('• **Preparation**: ${_getPreparationMethods(plant)}');
    }
    
    return recommendations.toString();
  }

  /// Get lifestyle recommendations
  String _getLifestyleRecommendations(QueryAnalysis analysis) {
    final recommendations = StringBuffer();
    recommendations.writeln('🏃‍♀️ **Lifestyle Modifications:**');
    
    switch (analysis.category) {
      case 'digestive_health':
        recommendations.writeln('• Eat mindfully and chew thoroughly');
        recommendations.writeln('• Avoid eating when stressed');
        recommendations.writeln('• Include fermented foods daily');
        recommendations.writeln('• Stay hydrated between meals');
        break;
      case 'stress_anxiety':
        recommendations.writeln('• Practice daily meditation (10-20 minutes)');
        recommendations.writeln('• Establish regular sleep schedule');
        recommendations.writeln('• Limit caffeine and alcohol');
        recommendations.writeln('• Spend time in nature daily');
        break;
      case 'immune_support':
        recommendations.writeln('• Prioritize 7-9 hours of quality sleep');
        recommendations.writeln('• Exercise regularly but avoid overtraining');
        recommendations.writeln('• Manage stress through relaxation techniques');
        recommendations.writeln('• Maintain social connections');
        break;
      default:
        recommendations.writeln('• Maintain regular sleep schedule');
        recommendations.writeln('• Exercise moderately and consistently');
        recommendations.writeln('• Practice stress management techniques');
        recommendations.writeln('• Stay hydrated throughout the day');
    }
    
    return recommendations.toString();
  }

  /// Get nutritional guidance
  String _getNutritionalGuidance(QueryAnalysis analysis) {
    final recommendations = StringBuffer();
    recommendations.writeln('🥗 **Nutritional Support:**');
    
    switch (analysis.category) {
      case 'digestive_health':
        recommendations.writeln('• **Fiber-rich foods**: Vegetables, fruits, whole grains');
        recommendations.writeln('• **Probiotics**: Yogurt, kefir, sauerkraut, kimchi');
        recommendations.writeln('• **Prebiotics**: Garlic, onions, asparagus, bananas');
        recommendations.writeln('• **Avoid**: Processed foods, excessive sugar, artificial additives');
        break;
      case 'immune_support':
        recommendations.writeln('• **Vitamin C**: Citrus fruits, berries, bell peppers');
        recommendations.writeln('• **Zinc**: Pumpkin seeds, nuts, legumes');
        recommendations.writeln('• **Vitamin D**: Sunlight exposure, fatty fish');
        recommendations.writeln('• **Antioxidants**: Colorful fruits and vegetables');
        break;
      case 'pain_inflammation':
        recommendations.writeln('• **Anti-inflammatory foods**: Fatty fish, leafy greens, berries');
        recommendations.writeln('• **Omega-3s**: Walnuts, flaxseeds, chia seeds');
        recommendations.writeln('• **Spices**: Turmeric, ginger, garlic');
        recommendations.writeln('• **Avoid**: Processed foods, excess sugar, trans fats');
        break;
      default:
        recommendations.writeln('• **Whole foods**: Emphasize unprocessed, natural foods');
        recommendations.writeln('• **Hydration**: 8-10 glasses of pure water daily');
        recommendations.writeln('• **Balance**: Include variety of colors and nutrients');
        recommendations.writeln('• **Timing**: Regular meal times to support circadian rhythm');
    }
    
    return recommendations.toString();
  }

  /// Get mind-body practices
  String _getMindBodyPractices(QueryAnalysis analysis) {
    final recommendations = StringBuffer();
    recommendations.writeln('🧘‍♀️ **Mind-Body Practices:**');
    
    switch (analysis.category) {
      case 'stress_anxiety':
        recommendations.writeln('• **Deep breathing**: 4-7-8 breathing technique');
        recommendations.writeln('• **Meditation**: Mindfulness or loving-kindness meditation');
        recommendations.writeln('• **Yoga**: Gentle, restorative poses');
        recommendations.writeln('• **Progressive muscle relaxation**: Full-body tension release');
        break;
      case 'sleep_disorders':
        recommendations.writeln('• **Sleep hygiene**: Cool, dark, quiet environment');
        recommendations.writeln('• **Bedtime routine**: Consistent wind-down activities');
        recommendations.writeln('• **Relaxation**: Gentle stretching or meditation');
        recommendations.writeln('• **Digital detox**: No screens 1 hour before bed');
        break;
      case 'pain_inflammation':
        recommendations.writeln('• **Gentle movement**: Tai chi, qigong, or gentle yoga');
        recommendations.writeln('• **Heat/cold therapy**: Alternating temperatures');
        recommendations.writeln('• **Massage**: Self-massage or professional therapy');
        recommendations.writeln('• **Visualization**: Guided imagery for pain relief');
        break;
      default:
        recommendations.writeln('• **Daily meditation**: Even 5-10 minutes helps');
        recommendations.writeln('• **Gratitude practice**: Write 3 things daily');
        recommendations.writeln('• **Nature connection**: Spend time outdoors');
        recommendations.writeln('• **Breathwork**: Conscious breathing exercises');
    }
    
    return recommendations.toString();
  }

  /// Add appropriate safety disclaimer
  String _addSafetyDisclaimer(String response, QueryAnalysis analysis) {
    final disclaimerLevel = _getDisclaimerLevel(analysis);
    String disclaimer = '';
    
    switch (disclaimerLevel) {
      case DisclaimerLevel.high:
        disclaimer = '''

⚠️ **IMPORTANT MEDICAL DISCLAIMER**
This information is for educational purposes only and should not replace professional medical advice. For serious symptoms, persistent conditions, or emergency situations, please consult with a qualified healthcare provider immediately. Always inform your doctor about any herbs or supplements you're considering, especially if you're taking medications or have existing health conditions.''';
        break;
      case DisclaimerLevel.medium:
        disclaimer = '''

💡 **Health Information Notice**
This guidance is based on traditional and scientific knowledge about natural remedies. Individual responses may vary. Please consult with a healthcare professional before starting any new herbal regimen, especially if you have health conditions or take medications.''';
        break;
      case DisclaimerLevel.low:
        disclaimer = '''

🌿 **Natural Health Information**
This information is for educational purposes. Always consult with a healthcare provider for personalized medical advice.''';
        break;
    }
    
    return response + disclaimer;
  }

  /// Detect medical conditions mentioned in the message
  List<String> _detectMedicalConditions(String message) {
    final conditions = <String>[];
    final conditionKeywords = {
      'diabetes': ['diabetes', 'diabetic', 'blood sugar', 'glucose'],
      'hypertension': ['high blood pressure', 'hypertension', 'blood pressure'],
      'arthritis': ['arthritis', 'joint pain', 'rheumatoid'],
      'depression': ['depression', 'depressed', 'sad', 'hopeless'],
      'anxiety': ['anxiety', 'anxious', 'panic', 'worry'],
      'insomnia': ['insomnia', 'sleep problems', 'can\'t sleep'],
      'ibs': ['ibs', 'irritable bowel', 'digestive issues'],
      'migraine': ['migraine', 'headache', 'head pain'],
      'eczema': ['eczema', 'skin rash', 'dermatitis'],
      'asthma': ['asthma', 'breathing problems', 'wheezing'],
    };
    
    for (final entry in conditionKeywords.entries) {
      if (entry.value.any((keyword) => message.contains(keyword))) {
        conditions.add(entry.key);
      }
    }
    
    return conditions;
  }

  /// Detect symptoms mentioned in the message
  List<String> _detectSymptoms(String message) {
    final symptoms = <String>[];
    final symptomKeywords = [
      'pain', 'ache', 'sore', 'tired', 'fatigue', 'nausea', 'headache',
      'fever', 'cough', 'congestion', 'bloating', 'constipation', 'diarrhea',
      'rash', 'itching', 'swelling', 'inflammation', 'stress', 'anxiety'
    ];
    
    for (final symptom in symptomKeywords) {
      if (message.contains(symptom)) {
        symptoms.add(symptom);
      }
    }
    
    return symptoms;
  }

  /// Detect plants/herbs mentioned in the message
  List<String> _detectMentionedPlants(String message) {
    final plants = <String>[];
    final allPlants = PlantDataset.getAllPlants();
    
    for (final plant in allPlants) {
      if (message.toLowerCase().contains(plant.name.toLowerCase()) ||
          message.toLowerCase().contains(plant.scientificName.toLowerCase())) {
        plants.add(plant.name);
      }
    }
    
    return plants;
  }

  /// Assess urgency level of the query
  UrgencyLevel _assessUrgency(String message) {
    final emergencyKeywords = [
      'emergency', 'urgent', 'severe', 'intense', 'unbearable', 'chest pain',
      'difficulty breathing', 'bleeding', 'poisoning', 'overdose'
    ];
    
    final highUrgencyKeywords = [
      'persistent', 'chronic', 'worsening', 'getting worse', 'weeks', 'months'
    ];
    
    if (emergencyKeywords.any((keyword) => message.contains(keyword))) {
      return UrgencyLevel.emergency;
    } else if (highUrgencyKeywords.any((keyword) => message.contains(keyword))) {
      return UrgencyLevel.high;
    } else {
      return UrgencyLevel.normal;
    }
  }

  /// Categorize the query into medical categories
  String _categorizeQuery(String message, List<String> conditions, List<String> symptoms) {
    // Implementation would analyze the message and return appropriate category
    // For now, return a default category
    if (conditions.contains('diabetes') || symptoms.contains('blood sugar')) {
      return 'hormonal_balance';
    } else if (symptoms.any((s) => ['pain', 'ache', 'inflammation'].contains(s))) {
      return 'pain_inflammation';
    } else if (symptoms.any((s) => ['stress', 'anxiety', 'worry'].contains(s))) {
      return 'stress_anxiety';
    } else if (symptoms.any((s) => ['tired', 'fatigue', 'sleep'].contains(s))) {
      return 'sleep_disorders';
    } else {
      return 'general_wellness';
    }
  }

  /// Check if medical disclaimer is required
  bool _requiresMedicalDisclaimer(String message) {
    final medicalKeywords = [
      'treatment', 'cure', 'medicine', 'medication', 'doctor', 'hospital',
      'disease', 'condition', 'symptoms', 'diagnosis', 'therapy'
    ];
    
    return medicalKeywords.any((keyword) => message.contains(keyword));
  }

  /// Get personalized greeting based on analysis
  String _getPersonalizedGreeting(QueryAnalysis analysis) {
    if (analysis.urgencyLevel == UrgencyLevel.emergency) {
      return '🚨 **For emergency situations, please contact emergency services immediately (911) or visit your nearest emergency room.**';
    } else if (analysis.detectedConditions.isNotEmpty) {
      return 'I understand you\'re seeking natural support for ${analysis.detectedConditions.join(" and ")}. Here\'s a comprehensive holistic approach:';
    } else if (analysis.detectedSymptoms.isNotEmpty) {
      return 'I can help you explore natural approaches for ${analysis.detectedSymptoms.join(" and ")}. Here\'s what I recommend:';
    } else {
      return 'I\'m here to provide holistic and naturopathic guidance. Here\'s a comprehensive approach:';
    }
  }

  /// Get default response for errors
  String _getDefaultResponse() {
    return '''I'm here to help with natural health questions! I can provide information about:

🌿 **Medicinal plants and herbs**
💊 **Natural remedies and supplements**  
🍃 **Holistic wellness approaches**
🌱 **Nutritional guidance**
🧘‍♀️ **Mind-body practices**

Please ask me about specific symptoms, conditions, or plants you'd like to learn about!

💡 **Health Information Notice**
This guidance is for educational purposes. Always consult with a healthcare professional for personalized medical advice.''';
  }

  // Additional helper methods would be implemented here...
  String _getDigestiveHealthRecommendations(QueryAnalysis analysis) => 'Focus on gut health through probiotics, digestive enzymes, and healing herbs like chamomile and peppermint.';
  String _getImmuneSystemRecommendations(QueryAnalysis analysis) => 'Strengthen immunity with elderberry, echinacea, vitamin C, and adaptogenic herbs.';
  String _getStressAnxietyRecommendations(QueryAnalysis analysis) => 'Calm the nervous system with ashwagandha, lemon balm, and magnesium supplementation.';
  String _getSleepDisorderRecommendations(QueryAnalysis analysis) => 'Promote restful sleep with valerian, chamomile, and melatonin support.';
  String _getPainInflammationRecommendations(QueryAnalysis analysis) => 'Reduce inflammation naturally with turmeric, ginger, and omega-3 fatty acids.';
  String _getRespiratoryHealthRecommendations(QueryAnalysis analysis) => 'Support respiratory function with eucalyptus, thyme, and breathing exercises.';
  String _getHormonalBalanceRecommendations(QueryAnalysis analysis) => 'Balance hormones with adaptogenic herbs, healthy fats, and stress management.';
  String _getSkinConditionRecommendations(QueryAnalysis analysis) => 'Heal skin naturally with aloe vera, calendula, and anti-inflammatory nutrition.';
  String _getGeneralWellnessRecommendations(QueryAnalysis analysis) => 'Support overall wellness with a balanced approach to nutrition, movement, and stress management.';
  
  Future<List<Plant>> _findRelevantPlants(QueryAnalysis analysis) async => PlantDataset.getAllPlants().take(3).toList();
  String _getPlantUsageInstructions(Plant plant, QueryAnalysis analysis) => 'Use as tea, tincture, or supplement as directed.';
  String _getPlantDosageRecommendations(Plant plant) => 'Follow package directions or consult with a qualified herbalist.';
  String _getPreparationMethods(Plant plant) => 'Tea: 1-2 tsp dried herb per cup of hot water, steep 5-10 minutes.';
  
  DisclaimerLevel _getDisclaimerLevel(QueryAnalysis analysis) {
    if (analysis.urgencyLevel == UrgencyLevel.emergency || analysis.detectedConditions.isNotEmpty) {
      return DisclaimerLevel.high;
    } else if (analysis.detectedSymptoms.isNotEmpty) {
      return DisclaimerLevel.medium;
    } else {
      return DisclaimerLevel.low;
    }
  }
}

/// Query analysis result
class QueryAnalysis {
  final String originalMessage;
  final List<String> detectedConditions;
  final List<String> detectedSymptoms;
  final List<String> mentionedPlants;
  final UrgencyLevel urgencyLevel;
  final String category;
  final bool requiresDisclaimer;

  QueryAnalysis({
    required this.originalMessage,
    required this.detectedConditions,
    required this.detectedSymptoms,
    required this.mentionedPlants,
    required this.urgencyLevel,
    required this.category,
    required this.requiresDisclaimer,
  });
}

enum UrgencyLevel { normal, high, emergency }
enum DisclaimerLevel { low, medium, high }
