import 'package:flutter/material.dart';
import '../providers/app_state.dart';
import '../data/plant_dataset.dart';
import '../data/world_plant_encyclopedia.dart';

class AdvancedSearchWidget extends StatefulWidget {
  final Function(List<Plant>) onSearchResults;
  
  const AdvancedSearchWidget({
    super.key,
    required this.onSearchResults,
  });

  @override
  State<AdvancedSearchWidget> createState() => _AdvancedSearchWidgetState();
}

class _AdvancedSearchWidgetState extends State<AdvancedSearchWidget> {
  final TextEditingController _searchController = TextEditingController();
  String? _selectedCategory;
  String? _selectedBenefit;
  String? _selectedOrigin;
  String? _selectedMedicineSystem;
  double _minRating = 0.0;
  bool? _isEndangered;
  bool _showAdvancedFilters = false;

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 4,
      margin: const EdgeInsets.all(16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Search header
            Row(
              children: [
                const Icon(Icons.search, color: Colors.green),
                const SizedBox(width: 8),
                const Text(
                  'Advanced Plant Search',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                IconButton(
                  icon: Icon(
                    _showAdvancedFilters ? Icons.expand_less : Icons.expand_more,
                  ),
                  onPressed: () {
                    setState(() {
                      _showAdvancedFilters = !_showAdvancedFilters;
                    });
                  },
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            // Main search bar
            TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: 'Search plants by name, benefit, or condition...',
                prefixIcon: const Icon(Icons.search),
                suffixIcon: IconButton(
                  icon: const Icon(Icons.clear),
                  onPressed: () {
                    _searchController.clear();
                    _performSearch();
                  },
                ),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              onChanged: (value) => _performSearch(),
            ),
            
            // Quick filter chips
            const SizedBox(height: 16),
            Wrap(
              spacing: 8,
              children: [
                _buildQuickFilterChip('Adaptogens', 'Adaptogenic'),
                _buildQuickFilterChip('Immune Boosters', 'Immune Support'),
                _buildQuickFilterChip('Stress Relief', 'Stress Relief'),
                _buildQuickFilterChip('Energy Herbs', 'Energy Enhancement'),
                _buildQuickFilterChip('Endangered', null, isEndangered: true),
              ],
            ),
            
            // Advanced filters
            if (_showAdvancedFilters) ...[
              const SizedBox(height: 20),
              const Divider(),
              const SizedBox(height: 16),
              
              // Category filter
              _buildDropdownFilter(
                'Category',
                _selectedCategory,
                WorldPlantEncyclopedia.getAllCategories(),
                (value) => setState(() => _selectedCategory = value),
              ),
              
              const SizedBox(height: 12),
              
              // Benefit filter
              _buildDropdownFilter(
                'Healing Benefit',
                _selectedBenefit,
                WorldPlantEncyclopedia.getAllBenefits(),
                (value) => setState(() => _selectedBenefit = value),
              ),
              
              const SizedBox(height: 12),
              
              // Origin filter
              _buildDropdownFilter(
                'Origin/Region',
                _selectedOrigin,
                WorldPlantEncyclopedia.getAllOrigins(),
                (value) => setState(() => _selectedOrigin = value),
              ),
              
              const SizedBox(height: 12),
              
              // Medicine system filter
              _buildDropdownFilter(
                'Traditional Medicine System',
                _selectedMedicineSystem,
                WorldPlantEncyclopedia.getMedicineSystems(),
                (value) => setState(() => _selectedMedicineSystem = value),
              ),
              
              const SizedBox(height: 16),
              
              // Rating filter
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const Text(
                        'Minimum Rating:',
                        style: TextStyle(fontWeight: FontWeight.w500),
                      ),
                      Text(
                        _minRating.toStringAsFixed(1),
                        style: const TextStyle(fontWeight: FontWeight.bold),
                      ),
                    ],
                  ),
                  Slider(
                    value: _minRating,
                    min: 0.0,
                    max: 5.0,
                    divisions: 10,
                    label: _minRating.toStringAsFixed(1),
                    onChanged: (value) {
                      setState(() => _minRating = value);
                      _performSearch();
                    },
                  ),
                ],
              ),
              
              const SizedBox(height: 12),
              
              // Endangered filter
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Conservation Status:',
                    style: TextStyle(fontWeight: FontWeight.w500),
                  ),
                  const SizedBox(height: 8),
                  Wrap(
                    spacing: 8,
                    children: [
                      ChoiceChip(
                        label: const Text('All'),
                        selected: _isEndangered == null,
                        onSelected: (selected) {
                          if (selected) {
                            setState(() => _isEndangered = null);
                            _performSearch();
                          }
                        },
                      ),
                      ChoiceChip(
                        label: const Text('Endangered'),
                        selected: _isEndangered == true,
                        onSelected: (selected) {
                          setState(() => _isEndangered = selected ? true : null);
                          _performSearch();
                        },
                      ),
                      ChoiceChip(
                        label: const Text('Safe'),
                        selected: _isEndangered == false,
                        onSelected: (selected) {
                          setState(() => _isEndangered = selected ? false : null);
                          _performSearch();
                        },
                      ),
                    ],
                  ),
                ],
              ),
              
              const SizedBox(height: 16),
              
              // Clear filters button
              Center(
                child: ElevatedButton.icon(
                  icon: const Icon(Icons.clear_all),
                  label: const Text('Clear All Filters'),
                  onPressed: _clearAllFilters,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildQuickFilterChip(String label, String? benefit, {bool? isEndangered}) {
    bool isSelected = false;
    
    if (benefit != null) {
      isSelected = _selectedBenefit == benefit;
    } else if (isEndangered != null) {
      isSelected = _isEndangered == isEndangered;
    }
    
    return FilterChip(
      label: Text(label),
      selected: isSelected,
      onSelected: (selected) {
        setState(() {
          if (benefit != null) {
            _selectedBenefit = selected ? benefit : null;
          } else if (isEndangered != null) {
            _isEndangered = selected ? isEndangered : null;
          }
        });
        _performSearch();
      },
    );
  }

  Widget _buildDropdownFilter(
    String label,
    String? value,
    List<String> options,
    Function(String?) onChanged,
  ) {
    return Row(
      children: [
        SizedBox(
          width: 120,
          child: Text(
            '$label:',
            style: const TextStyle(fontWeight: FontWeight.w500),
          ),
        ),
        Expanded(
          child: DropdownButton<String>(
            value: value,
            hint: Text('Select $label'),
            isExpanded: true,
            items: options.map((String option) {
              return DropdownMenuItem<String>(
                value: option,
                child: Text(option),
              );
            }).toList(),
            onChanged: (newValue) {
              onChanged(newValue);
              _performSearch();
            },
          ),
        ),
      ],
    );
  }

  void _performSearch() {
    final results = PlantDataset.advancedSearch(
      query: _searchController.text,
      category: _selectedCategory,
      benefit: _selectedBenefit,
      origin: _selectedOrigin,
      medicineSystem: _selectedMedicineSystem,
      minRating: _minRating > 0 ? _minRating : null,
      isEndangered: _isEndangered,
    );
    
    widget.onSearchResults(results);
  }

  void _clearAllFilters() {
    setState(() {
      _searchController.clear();
      _selectedCategory = null;
      _selectedBenefit = null;
      _selectedOrigin = null;
      _selectedMedicineSystem = null;
      _minRating = 0.0;
      _isEndangered = null;
    });
    _performSearch();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }
}
