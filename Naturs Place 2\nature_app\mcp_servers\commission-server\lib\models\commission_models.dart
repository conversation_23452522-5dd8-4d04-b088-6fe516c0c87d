enum CommissionStatus {
  pending,
  approved,
  paid,
  rejected,
}

class Commission {
  final double saleAmount;
  final String vendorTier;
  final double platformFee;
  final double affiliateCommission;
  final double vendorPayout;
  final CommissionStatus status;

  Commission({
    required this.saleAmount,
    required this.vendorTier,
    required this.platformFee,
    required this.affiliateCommission,
    required this.vendorPayout,
    required this.status,
  });
}
