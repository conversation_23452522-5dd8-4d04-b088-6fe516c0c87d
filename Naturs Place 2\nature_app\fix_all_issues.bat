@echo off
echo 🌿 Nature's Place - Complete Issue Resolution Script 🌿
echo.
echo This script will attempt to fix all common Android build issues
echo.

:menu
echo ========================================
echo Choose an option:
echo ========================================
echo 1. Quick Fix (Clean + Build + Run)
echo 2. Deep Clean (Remove all build artifacts)
echo 3. Fix MainActivity Error
echo 4. Setup Device/Emulator
echo 5. Run Diagnostics
echo 6. Build APK Only
echo 7. Run App Only
echo 8. Fix Memory Issues (Gradle/Dart OOM)
echo 9. Ultimate Memory Fix (All optimizations)
echo 10. Enable Low-Memory Mode
echo 11. Fix Directionality Error (Stack widgets)
echo 12. Fix Element Issues (naming, parameters, deprecated APIs)
echo 13. Exit
echo.
set /p choice="Enter your choice (1-13): "

if "%choice%"=="1" goto quickfix
if "%choice%"=="2" goto deepclean
if "%choice%"=="3" goto fixmain
if "%choice%"=="4" goto setupdevice
if "%choice%"=="5" goto diagnostics
if "%choice%"=="6" goto buildonly
if "%choice%"=="7" goto runonly
if "%choice%"=="8" goto fixmemory
if "%choice%"=="9" goto ultimatefix
if "%choice%"=="10" goto lowmemory
if "%choice%"=="11" goto fixdirectionality
if "%choice%"=="12" goto fixelements
if "%choice%"=="13" goto exit
echo Invalid choice. Please try again.
goto menu

:quickfix
echo.
echo 🚀 Running Quick Fix...
call clean_build.bat
call build_and_run.bat
goto menu

:deepclean
echo.
echo 🧹 Running Deep Clean...
call clean_build.bat
echo.
echo Removing additional cache files...
if exist "%USERPROFILE%\.gradle\caches" rmdir /s /q "%USERPROFILE%\.gradle\caches"
if exist "%USERPROFILE%\.android\build-cache" rmdir /s /q "%USERPROFILE%\.android\build-cache"
echo ✅ Deep clean completed
goto menu

:fixmain
echo.
echo 🔧 Fixing MainActivity Error...
call fix_mainactivity.bat
goto menu

:setupdevice
echo.
echo 📱 Setting up Device/Emulator...
call setup_device.bat
goto menu

:diagnostics
echo.
echo 🔍 Running Diagnostics...
call troubleshoot.bat
goto menu

:buildonly
echo.
echo 🔨 Building APK...
flutter clean
flutter pub get
flutter build apk --debug
echo ✅ Build completed
goto menu

:runonly
echo.
echo 🏃 Running App...
flutter run --debug --verbose
goto menu

:fixmemory
echo.
echo 🧠 Fixing Memory Issues...
call fix_memory_issues.bat
goto menu

:ultimatefix
echo.
echo 🚀 Running Ultimate Memory Fix...
call ultimate_memory_fix.bat
goto menu

:lowmemory
echo.
echo 🧠 Enabling Low-Memory Mode...
call enable_low_memory_mode.bat
goto menu

:fixdirectionality
echo.
echo 🧭 Fixing Directionality Error...
call fix_directionality.bat
goto menu

:fixelements
echo.
echo 🔧 Fixing Element Issues...
call fix_element_issues.bat
goto menu

:exit
echo.
echo 👋 Goodbye! Your Nature's Place app should be working now.
pause
exit
