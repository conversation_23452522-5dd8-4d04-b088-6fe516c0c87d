import 'package:flutter/material.dart';
import '../services/commission_monitoring_service.dart';
import '../services/payout_scheduler_service.dart';

/// Admin dashboard for monitoring commission system and partner performance
class AdminCommissionDashboard extends StatefulWidget {
  const AdminCommissionDashboard({Key? key}) : super(key: key);

  @override
  State<AdminCommissionDashboard> createState() => _AdminCommissionDashboardState();
}

class _AdminCommissionDashboardState extends State<AdminCommissionDashboard> {
  final CommissionMonitoringService _monitoringService = CommissionMonitoringService();
  final PayoutSchedulerService _payoutScheduler = PayoutSchedulerService();
  
  Map<String, dynamic> _systemAnalytics = {};
  bool _isLoading = true;
  bool _isMonitoring = false;

  @override
  void initState() {
    super.initState();
    _loadDashboardData();
    _initializeMonitoring();
  }

  Future<void> _loadDashboardData() async {
    setState(() => _isLoading = true);

    try {
      final analytics = await _monitoringService.getSystemAnalytics();
      
      setState(() {
        _systemAnalytics = analytics;
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Failed to load dashboard: $e')),
      );
    }
  }

  Future<void> _initializeMonitoring() async {
    await _monitoringService.initialize();
    _monitoringService.startMonitoring();
    setState(() => _isMonitoring = true);
  }

  @override
  void dispose() {
    _monitoringService.stopMonitoring();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Scaffold(
        body: Center(child: CircularProgressIndicator()),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('Commission System Admin'),
        backgroundColor: Colors.indigo[700],
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: Icon(_isMonitoring ? Icons.monitor_heart : Icons.monitor_heart_outlined),
            onPressed: () {
              if (_isMonitoring) {
                _monitoringService.stopMonitoring();
              } else {
                _monitoringService.startMonitoring();
              }
              setState(() => _isMonitoring = !_isMonitoring);
            },
            tooltip: _isMonitoring ? 'Stop Monitoring' : 'Start Monitoring',
          ),
        ],
      ),
      body: RefreshIndicator(
        onRefresh: _loadDashboardData,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildSystemHealthCard(),
              const SizedBox(height: 16),
              _buildCommissionOverviewCard(),
              const SizedBox(height: 16),
              _buildPartnerPerformanceCard(),
              const SizedBox(height: 16),
              _buildPayoutManagementCard(),
              const SizedBox(height: 16),
              _buildFraudDetectionCard(),
              const SizedBox(height: 16),
              _buildQuickActionsCard(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSystemHealthCard() {
    final healthScore = (_systemAnalytics['system_health'] ?? 0.0) * 100;
    final healthColor = healthScore >= 90 ? Colors.green : healthScore >= 70 ? Colors.orange : Colors.red;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.health_and_safety, color: healthColor),
                const SizedBox(width: 8),
                const Text(
                  'System Health',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
                const Spacer(),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: _isMonitoring ? Colors.green[100] : Colors.grey[200],
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        _isMonitoring ? Icons.circle : Icons.circle_outlined,
                        color: _isMonitoring ? Colors.green : Colors.grey,
                        size: 12,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        _isMonitoring ? 'Monitoring' : 'Stopped',
                        style: TextStyle(
                          fontSize: 12,
                          color: _isMonitoring ? Colors.green[700] : Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text('Health Score', style: TextStyle(color: Colors.grey)),
                      Text(
                        '${healthScore.toStringAsFixed(1)}%',
                        style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold, color: healthColor),
                      ),
                    ],
                  ),
                ),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text('Active Partners', style: TextStyle(color: Colors.grey)),
                      Text(
                        '${_systemAnalytics['active_partners'] ?? 0}',
                        style: const TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCommissionOverviewCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.analytics, color: Colors.blue[700]),
                const SizedBox(width: 8),
                const Text(
                  'Commission Overview',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildMetricCard(
                    'Last 24h',
                    '${_systemAnalytics['total_commissions_24h'] ?? 0}',
                    'commissions',
                    Colors.blue,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildMetricCard(
                    'Last 7 days',
                    '${_systemAnalytics['total_commissions_7d'] ?? 0}',
                    'commissions',
                    Colors.green,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: _buildMetricCard(
                    'Cancellation Rate',
                    '${((_systemAnalytics['cancellation_rate_24h'] ?? 0.0) * 100).toStringAsFixed(1)}%',
                    '24h average',
                    Colors.orange,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildMetricCard(
                    'Avg Conversion',
                    '${((_systemAnalytics['avg_conversion_rate'] ?? 0.0) * 100).toStringAsFixed(1)}%',
                    'all partners',
                    Colors.purple,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPartnerPerformanceCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.people, color: Colors.green[700]),
                const SizedBox(width: 8),
                const Text(
                  'Partner Performance',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
                const Spacer(),
                TextButton(
                  onPressed: () => _showPartnerDetails(),
                  child: const Text('View All'),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildPerformanceIndicator('High Performers (>10%)', 3, Colors.green),
            _buildPerformanceIndicator('Good Performers (5-10%)', 8, Colors.blue),
            _buildPerformanceIndicator('Needs Improvement (<5%)', 4, Colors.orange),
            _buildPerformanceIndicator('At Risk (<2%)', 1, Colors.red),
          ],
        ),
      ),
    );
  }

  Widget _buildPayoutManagementCard() {
    final pendingAmount = _systemAnalytics['pending_payouts'] ?? 0.0;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.payment, color: Colors.indigo[700]),
                const SizedBox(width: 8),
                const Text(
                  'Payout Management',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text('Pending Payouts', style: TextStyle(color: Colors.grey)),
                      Text(
                        '\$${pendingAmount.toStringAsFixed(2)}',
                        style: const TextStyle(fontSize: 24, fontWeight: FontWeight.bold, color: Colors.indigo),
                      ),
                    ],
                  ),
                ),
                ElevatedButton(
                  onPressed: () => _processDuePayouts(),
                  style: ElevatedButton.styleFrom(backgroundColor: Colors.indigo[700]),
                  child: const Text('Process Due Payouts', style: TextStyle(color: Colors.white)),
                ),
              ],
            ),
            const SizedBox(height: 12),
            const Text(
              'Next automated payout processing: Daily at 9:00 AM',
              style: TextStyle(fontSize: 12, color: Colors.grey),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFraudDetectionCard() {
    final fraudAlerts = _systemAnalytics['fraud_alerts_24h'] ?? 0;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.security, color: fraudAlerts > 0 ? Colors.red : Colors.green),
                const SizedBox(width: 8),
                const Text(
                  'Fraud Detection',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text('Alerts (24h)', style: TextStyle(color: Colors.grey)),
                      Text(
                        '$fraudAlerts',
                        style: TextStyle(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                          color: fraudAlerts > 0 ? Colors.red : Colors.green,
                        ),
                      ),
                    ],
                  ),
                ),
                if (fraudAlerts > 0)
                  ElevatedButton(
                    onPressed: () => _showFraudAlerts(),
                    style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
                    child: const Text('Review Alerts', style: TextStyle(color: Colors.white)),
                  ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Icon(Icons.check_circle, color: Colors.green, size: 16),
                const SizedBox(width: 4),
                const Text('Self-referral detection', style: TextStyle(fontSize: 12)),
                const SizedBox(width: 16),
                Icon(Icons.check_circle, color: Colors.green, size: 16),
                const SizedBox(width: 4),
                const Text('Rapid refund monitoring', style: TextStyle(fontSize: 12)),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickActionsCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.flash_on, color: Colors.amber[700]),
                const SizedBox(width: 8),
                const Text(
                  'Quick Actions',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: [
                _buildActionChip('Export Analytics', Icons.download, () => _exportAnalytics()),
                _buildActionChip('Partner Report', Icons.people_alt, () => _generatePartnerReport()),
                _buildActionChip('System Health', Icons.health_and_safety, () => _runHealthCheck()),
                _buildActionChip('Test Payouts', Icons.payment, () => _testPayoutSystem()),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMetricCard(String title, String value, String subtitle, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(title, style: TextStyle(color: color, fontSize: 12, fontWeight: FontWeight.bold)),
          const SizedBox(height: 4),
          Text(value, style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold, color: color)),
          Text(subtitle, style: const TextStyle(fontSize: 10, color: Colors.grey)),
        ],
      ),
    );
  }

  Widget _buildPerformanceIndicator(String label, int count, Color color) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Container(
            width: 12,
            height: 12,
            decoration: BoxDecoration(color: color, shape: BoxShape.circle),
          ),
          const SizedBox(width: 8),
          Expanded(child: Text(label, style: const TextStyle(fontSize: 14))),
          Text('$count', style: TextStyle(fontWeight: FontWeight.bold, color: color)),
        ],
      ),
    );
  }

  Widget _buildActionChip(String label, IconData icon, VoidCallback onPressed) {
    return ActionChip(
      avatar: Icon(icon, size: 16),
      label: Text(label, style: const TextStyle(fontSize: 12)),
      onPressed: onPressed,
    );
  }

  // Action methods
  void _showPartnerDetails() {
    // Implementation would show detailed partner list
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Partner details view would open here')),
    );
  }

  Future<void> _processDuePayouts() async {
    try {
      await _payoutScheduler.processDuePayouts();
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Due payouts processed successfully')),
      );
      _loadDashboardData();
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Failed to process payouts: $e')),
      );
    }
  }

  void _showFraudAlerts() {
    // Implementation would show fraud alert details
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Fraud alerts view would open here')),
    );
  }

  void _exportAnalytics() {
    // Implementation would export analytics data
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Analytics export started')),
    );
  }

  void _generatePartnerReport() {
    // Implementation would generate partner performance report
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Partner report generation started')),
    );
  }

  void _runHealthCheck() {
    // Implementation would run system health check
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('System health check completed')),
    );
  }

  void _testPayoutSystem() {
    // Implementation would test payout system
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Payout system test completed')),
    );
  }
}
