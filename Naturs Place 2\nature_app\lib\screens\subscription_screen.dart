import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import '../services/in_app_purchase_service.dart' as iap;
import 'plan_features_screen.dart';

class SubscriptionScreen extends StatefulWidget {
  const SubscriptionScreen({super.key});

  @override
  State<SubscriptionScreen> createState() => _SubscriptionScreenState();
}

class _SubscriptionScreenState extends State<SubscriptionScreen> {
  @override
  void initState() {
    super.initState();
    // Initialize in-app purchases when screen loads
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Provider.of<iap.InAppPurchaseService>(context, listen: false).initialize();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Choose Your Plan'),
        backgroundColor: const Color(0xFF22c55e),
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: Consumer<iap.InAppPurchaseService>(
        builder: (context, purchaseService, child) {
          if (!purchaseService.isAvailable) {
            return _buildErrorState(purchaseService.queryProductError);
          }

          if (purchaseService.products.isEmpty) {
            return const Center(
              child: CircularProgressIndicator(),
            );
          }

          return SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(24),
                  decoration: BoxDecoration(
                    gradient: const LinearGradient(
                      colors: [Color(0xFF22c55e), Color(0xFF84cc16)],
                    ),
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Column(
                    children: [
                      const Icon(
                        Icons.workspace_premium,
                        size: 48,
                        color: Colors.white,
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'Unlock Your Natural Health Journey',
                        style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Choose the perfect plan for your holistic health needs',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Colors.white.withValues(alpha: 0.9),
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 32),

                // Current Subscription Status
                if (purchaseService.hasActiveSubscription()) ...[
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.green[50],
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(color: Colors.green[200]!),
                    ),
                    child: Row(
                      children: [
                        const Icon(Icons.check_circle, color: Colors.green),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Current Plan: ${purchaseService.getCurrentSubscriptionTier()?.name ?? 'Unknown'}',
                                style: const TextStyle(
                                  fontWeight: FontWeight.bold,
                                  color: Colors.green,
                                ),
                              ),
                              const Text(
                                'Your subscription is active and ready to use!',
                                style: TextStyle(color: Colors.green),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 24),
                ],

                // Subscription Plans
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Available Plans',
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    TextButton.icon(
                      onPressed: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => const PlanFeaturesScreen(),
                          ),
                        );
                      },
                      icon: const Icon(Icons.info_outline, size: 16),
                      label: const Text('View All Features'),
                    ),
                  ],
                ),
                const SizedBox(height: 16),

                ...iap.SubscriptionTiers.all.map((tier) {
                  final productDetails = purchaseService.getProductDetails(tier.productId);
                  final isCurrentPlan = purchaseService.hasSubscription(tier.productId);
                  
                  return _buildSubscriptionCard(
                    context,
                    tier,
                    productDetails,
                    isCurrentPlan,
                    purchaseService.purchasePending,
                    () => _purchaseSubscription(context, purchaseService, productDetails!),
                  );
                }),

                const SizedBox(height: 32),

                // Features Comparison
                _buildFeaturesComparison(),

                const SizedBox(height: 32),

                // Terms and Privacy
                _buildTermsAndPrivacy(),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildErrorState(String? error) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.error_outline,
            size: 64,
            color: Colors.grey,
          ),
          const SizedBox(height: 16),
          Text(
            'Store Not Available',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            error ?? 'In-app purchases are not available on this device',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.grey[500],
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Go Back'),
          ),
        ],
      ),
    );
  }

  Widget _buildSubscriptionCard(
    BuildContext context,
    iap.SubscriptionTier tier,
    dynamic productDetails,
    bool isCurrentPlan,
    bool purchasePending,
    VoidCallback onPurchase,
  ) {
    final bool isPremium = tier.id == 'premium';
    
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: isPremium ? const Color(0xFF22c55e) : Colors.grey[300]!,
          width: isPremium ? 2 : 1,
        ),
        boxShadow: isPremium ? [
          BoxShadow(
            color: const Color(0xFF22c55e).withValues(alpha: 0.2),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ] : null,
      ),
      child: Stack(
        children: [
          // Popular Badge
          if (isPremium)
            Positioned(
              top: 0,
              right: 0,
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: const BoxDecoration(
                  color: Color(0xFF22c55e),
                  borderRadius: BorderRadius.only(
                    topRight: Radius.circular(16),
                    bottomLeft: Radius.circular(16),
                  ),
                ),
                child: const Text(
                  'MOST POPULAR',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 10,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),

          Padding(
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Plan Name and Price
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            tier.name,
                            style: Theme.of(context).textTheme.titleLarge?.copyWith(
                              fontWeight: FontWeight.bold,
                              color: isPremium ? const Color(0xFF22c55e) : null,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            tier.description,
                            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              color: Colors.grey[600],
                            ),
                          ),
                        ],
                      ),
                    ),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        Text(
                          productDetails?.price ?? '\$${tier.price.toStringAsFixed(2)}',
                          style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: isPremium ? const Color(0xFF22c55e) : null,
                          ),
                        ),
                        Text(
                          '/month',
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
                const SizedBox(height: 20),

                // Key Features
                ...tier.features.take(3).map((feature) => Padding(
                  padding: const EdgeInsets.symmetric(vertical: 4),
                  child: Row(
                    children: [
                      Icon(
                        Icons.check_circle,
                        color: isPremium ? const Color(0xFF22c55e) : Colors.green,
                        size: 20,
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Text(
                          feature,
                          style: Theme.of(context).textTheme.bodyMedium,
                        ),
                      ),
                    ],
                  ),
                )),

                const SizedBox(height: 20),

                // Subscribe Button
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: isCurrentPlan || purchasePending || productDetails == null
                        ? null
                        : onPurchase,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: isPremium ? const Color(0xFF22c55e) : Colors.grey[800],
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: Text(
                      isCurrentPlan
                          ? 'Current Plan'
                          : purchasePending
                              ? 'Processing...'
                              : 'Subscribe Now',
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFeaturesComparison() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Why Choose a Premium Plan?',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),

          const Row(
            children: [
              FaIcon(FontAwesomeIcons.infinity, color: Color(0xFF22c55e)),
              SizedBox(width: 12),
              Expanded(
                child: Text('Unlimited plant scans and identification'),
              ),
            ],
          ),
          const SizedBox(height: 12),

          const Row(
            children: [
              FaIcon(FontAwesomeIcons.stethoscope, color: Color(0xFF22c55e)),
              SizedBox(width: 12),
              Expanded(
                child: Text('Advanced disease detection and treatment recommendations'),
              ),
            ],
          ),
          const SizedBox(height: 12),

          const Row(
            children: [
              FaIcon(FontAwesomeIcons.robot, color: Color(0xFF22c55e)),
              SizedBox(width: 12),
              Expanded(
                child: Text('Unlimited AI health assistant with personalized advice'),
              ),
            ],
          ),
          const SizedBox(height: 12),

          const Row(
            children: [
              FaIcon(FontAwesomeIcons.download, color: Color(0xFF22c55e)),
              SizedBox(width: 12),
              Expanded(
                child: Text('Offline plant database for use anywhere'),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildTermsAndPrivacy() {
    return Column(
      children: [
        Text(
          'By subscribing, you agree to our Terms of Service and Privacy Policy. Subscriptions automatically renew unless cancelled at least 24 hours before the end of the current period.',
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: Colors.grey[600],
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 16),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            TextButton(
              onPressed: () {
                // Open Terms of Service
              },
              child: const Text('Terms of Service'),
            ),
            const Text(' • '),
            TextButton(
              onPressed: () {
                // Open Privacy Policy
              },
              child: const Text('Privacy Policy'),
            ),
          ],
        ),
      ],
    );
  }

  void _purchaseSubscription(
    BuildContext context,
    iap.InAppPurchaseService purchaseService,
    dynamic productDetails,
  ) async {
    final scaffoldMessenger = ScaffoldMessenger.of(context);
    final success = await purchaseService.buySubscription(productDetails);

    if (!success) {
      if (mounted) {
        scaffoldMessenger.showSnackBar(
          const SnackBar(
            content: Text('Failed to start purchase. Please try again.'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
