import 'package:flutter/foundation.dart';
import '../models/commission_models.dart';
import 'postgres_service.dart';
import 'commission_service.dart';

/// Service to handle 30-day partner payout scheduling
class PayoutSchedulerService {
  static final PayoutSchedulerService _instance = PayoutSchedulerService._internal();
  factory PayoutSchedulerService() => _instance;
  PayoutSchedulerService._internal();

  final PostgreSQLService _postgresService = PostgreSQLService();

  /// Initialize payout scheduler
  Future<void> initialize() async {
    await _postgresService.initialize();
    debugPrint('✅ Payout Scheduler Service initialized');
  }

  /// Create payout schedule for a new partner (30-day cycle)
  Future<void> createPartnerPayoutSchedule(String partnerId) async {
    final now = DateTime.now();
    final cycleStart = DateTime(now.year, now.month, now.day);
    final cycleEnd = cycleStart.add(const Duration(days: 30));
    final nextPayoutDate = cycleEnd;

    final scheduleId = 'schedule_${partnerId}_${now.millisecondsSinceEpoch}';

    try {
      await _postgresService.insertPayoutSchedule(
        id: scheduleId,
        partnerId: partnerId,
        partnerType: 'affiliate',
        scheduleType: 'monthly_30_days',
        cycleStartDate: cycleStart,
        cycleEndDate: cycleEnd,
        nextPayoutDate: nextPayoutDate,
        status: 'active',
      );

      debugPrint('✅ Created 30-day payout schedule for partner: $partnerId');
      debugPrint('   Next payout date: ${nextPayoutDate.toIso8601String().split('T')[0]}');
    } catch (e) {
      debugPrint('❌ Failed to create payout schedule: $e');
      rethrow;
    }
  }

  /// Get partners due for payout (30 days completed)
  Future<List<Map<String, dynamic>>> getPartnersDueForPayout() async {
    final today = DateTime.now();
    
    try {
      return await _postgresService.getPartnersDueForPayout(today);
    } catch (e) {
      debugPrint('❌ Failed to get partners due for payout: $e');
      return [];
    }
  }

  /// Calculate pending commission amount for a partner
  Future<double> calculatePendingCommissions(String partnerId) async {
    try {
      final commissions = await _postgresService.getPendingCommissions(partnerId, 'affiliate');
      return commissions.fold<double>(0.0, (sum, commission) => sum + (commission['total_amount'] as num).toDouble());
    } catch (e) {
      debugPrint('❌ Failed to calculate pending commissions: $e');
      return 0.0;
    }
  }

  /// Process 30-day partner payout
  Future<String?> processPartnerPayout(String partnerId) async {
    try {
      // Get pending commissions for the partner
      final pendingCommissions = await _postgresService.getPendingCommissions(partnerId, 'affiliate');
      
      if (pendingCommissions.isEmpty) {
        debugPrint('⚠️ No pending commissions for partner: $partnerId');
        return null;
      }

      // Calculate total payout amount
      final totalAmount = pendingCommissions.fold<double>(0.0, (sum, commission) => sum + (commission['total_amount'] as num).toDouble());
      final fees = _calculatePayoutFees('stripe', totalAmount);
      final netAmount = totalAmount - fees;

      // Create payout record
      final payoutId = 'payout_${partnerId}_${DateTime.now().millisecondsSinceEpoch}';
      final commissionIds = pendingCommissions.map((c) => c['id'].toString()).toList();

      final payout = Payout(
        id: payoutId,
        partnerId: partnerId,
        partnerType: 'affiliate',
        commissionIds: commissionIds,
        totalAmount: totalAmount,
        fees: fees,
        netAmount: netAmount,
        status: PayoutStatus.pending,
        paymentMethod: 'stripe',
        createdAt: DateTime.now(),
        metadata: {
          'payout_cycle': '30_days',
          'commission_count': pendingCommissions.length.toString(),
          'cycle_end_date': DateTime.now().toIso8601String().split('T')[0],
        },
      );

      // Insert payout record
      await _postgresService.insertPayout(payout);

      // Mark commissions as paid
      for (final commission in pendingCommissions) {
        await _postgresService.markCommissionAsPaid(commission['id'], payoutId);
      }

      // Update payout schedule for next cycle
      await _updatePayoutScheduleForNextCycle(partnerId);

      debugPrint('✅ Processed 30-day payout for partner: $partnerId');
      debugPrint('   Payout ID: $payoutId');
      debugPrint('   Total Amount: \$${totalAmount.toStringAsFixed(2)}');
      debugPrint('   Net Amount: \$${netAmount.toStringAsFixed(2)}');
      debugPrint('   Commissions Paid: ${pendingCommissions.length}');

      return payoutId;
    } catch (e) {
      debugPrint('❌ Failed to process partner payout: $e');
      rethrow;
    }
  }

  /// Update payout schedule for next 30-day cycle
  Future<void> _updatePayoutScheduleForNextCycle(String partnerId) async {
    final now = DateTime.now();
    final nextCycleStart = DateTime(now.year, now.month, now.day);
    final nextCycleEnd = nextCycleStart.add(const Duration(days: 30));
    final nextPayoutDate = nextCycleEnd;

    try {
      await _postgresService.updatePayoutSchedule(
        partnerId: partnerId,
        cycleStartDate: nextCycleStart,
        cycleEndDate: nextCycleEnd,
        nextPayoutDate: nextPayoutDate,
      );

      debugPrint('🔄 Updated payout schedule for partner: $partnerId');
      debugPrint('   Next payout date: ${nextPayoutDate.toIso8601String().split('T')[0]}');
    } catch (e) {
      debugPrint('❌ Failed to update payout schedule: $e');
    }
  }

  /// Calculate payout fees (Stripe charges ~2.9% + $0.30)
  double _calculatePayoutFees(String paymentMethod, double amount) {
    switch (paymentMethod.toLowerCase()) {
      case 'stripe':
        return (amount * 0.029) + 0.30; // 2.9% + $0.30
      case 'bank_transfer':
        return 2.50; // Flat fee for bank transfers
      case 'paypal':
        return amount * 0.025; // 2.5%
      default:
        return 0.0;
    }
  }

  /// Get payout schedule for a partner
  Future<Map<String, dynamic>?> getPartnerPayoutSchedule(String partnerId) async {
    try {
      return await _postgresService.getPayoutSchedule(partnerId);
    } catch (e) {
      debugPrint('❌ Failed to get payout schedule: $e');
      return null;
    }
  }

  /// Get payout history for a partner
  Future<List<Map<String, dynamic>>> getPartnerPayoutHistory(String partnerId) async {
    try {
      return await _postgresService.getPayoutHistory(partnerId);
    } catch (e) {
      debugPrint('❌ Failed to get payout history: $e');
      return [];
    }
  }

  /// Process all due payouts (run daily)
  Future<void> processDuePayouts() async {
    debugPrint('🔄 Processing due payouts...');
    
    try {
      final partnersDue = await getPartnersDueForPayout();
      
      if (partnersDue.isEmpty) {
        debugPrint('✅ No partners due for payout today');
        return;
      }

      debugPrint('💰 Found ${partnersDue.length} partners due for payout');

      for (final partner in partnersDue) {
        final partnerId = partner['partner_id'];
        final pendingAmount = await calculatePendingCommissions(partnerId);
        
        if (pendingAmount > 0) {
          debugPrint('💸 Processing payout for partner: $partnerId (\$${pendingAmount.toStringAsFixed(2)})');
          await processPartnerPayout(partnerId);
        } else {
          debugPrint('⚠️ Partner $partnerId has no pending commissions');
        }
      }

      debugPrint('✅ Completed processing due payouts');
    } catch (e) {
      debugPrint('❌ Failed to process due payouts: $e');
    }
  }

  /// Get payout statistics
  Map<String, dynamic> getPayoutStatistics() {
    return {
      'payout_frequency': '30_days',
      'payment_method': 'stripe',
      'fee_structure': '2.9% + \$0.30',
      'processing_day': 'automatic_on_cycle_completion',
      'minimum_payout': 1.00,
    };
  }
}
