@echo off
echo 🧹 Cleaning Build Cache for Nature's Place App 🧹
echo.

echo Cleaning Flutter cache...
flutter clean
echo ✅ Flutter cache cleaned
echo.

echo Cleaning Android Gradle cache...
cd android
call gradlew clean
cd ..
echo ✅ Android Gradle cache cleaned
echo.

echo Removing build directories...
if exist "build" rmdir /s /q "build"
if exist "android\app\build" rmdir /s /q "android\app\build"
if exist "android\.gradle" rmdir /s /q "android\.gradle"
echo ✅ Build directories removed
echo.

echo Getting fresh dependencies...
flutter pub get
echo ✅ Dependencies updated
echo.

echo 🎉 Build cache cleaned successfully!
echo You can now run: flutter run
pause
