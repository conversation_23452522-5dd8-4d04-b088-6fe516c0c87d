import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import '../widgets/performance_monitor.dart';

/// Debug screen for performance monitoring tools
/// Only available in debug builds
class DebugPerformanceScreen extends StatefulWidget {
  const DebugPerformanceScreen({super.key});

  @override
  State<DebugPerformanceScreen> createState() => _DebugPerformanceScreenState();
}

class _DebugPerformanceScreenState extends State<DebugPerformanceScreen> {
  bool _showPerformanceMonitor = false;
  bool _showFPSMonitor = false;

  @override
  Widget build(BuildContext context) {
    // Only show in debug mode
    if (!kDebugMode) {
      return Scaffold(
        appBar: AppBar(
          title: const Text('Debug Tools'),
        ),
        body: const Center(
          child: Text(
            'Debug tools are only available in debug builds.',
            style: TextStyle(fontSize: 16),
          ),
        ),
      );
    }

    Widget child = Scaffold(
      appBar: AppBar(
        title: const Text('Performance Debug Tools'),
        backgroundColor: Colors.orange,
      ),
      body: ListView(
        padding: const EdgeInsets.all(16.0),
        children: [
          // Performance Monitor Section
          Card(
            child: Column(
              children: [
                SwitchListTile(
                  secondary: const Icon(Icons.speed),
                  title: const Text('Performance Monitor'),
                  subtitle: const Text('Show memory and CPU usage'),
                  value: _showPerformanceMonitor,
                  onChanged: (value) {
                    setState(() {
                      _showPerformanceMonitor = value;
                    });
                  },
                ),
                if (_showPerformanceMonitor) ...[
                  const Divider(height: 1),
                  const Padding(
                    padding: EdgeInsets.all(16.0),
                    child: Text(
                      'Performance monitor is active. Check the overlay on your screen.',
                      style: TextStyle(color: Colors.green),
                    ),
                  ),
                ],
              ],
            ),
          ),

          const SizedBox(height: 16),

          // FPS Monitor Section
          Card(
            child: Column(
              children: [
                SwitchListTile(
                  secondary: const Icon(Icons.timeline),
                  title: const Text('FPS Monitor'),
                  subtitle: const Text('Show frames per second'),
                  value: _showFPSMonitor,
                  onChanged: (value) {
                    setState(() {
                      _showFPSMonitor = value;
                    });
                  },
                ),
                if (_showFPSMonitor) ...[
                  const Divider(height: 1),
                  const Padding(
                    padding: EdgeInsets.all(16.0),
                    child: Text(
                      'FPS monitor is active. Check the overlay on your screen.',
                      style: TextStyle(color: Colors.blue),
                    ),
                  ),
                ],
              ],
            ),
          ),

          const SizedBox(height: 16),

          // Debug Info Section
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Debug Information',
                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 16),
                  Text('Debug Mode: ${kDebugMode ? "✅ Enabled" : "❌ Disabled"}'),
                  Text('Profile Mode: ${kProfileMode ? "✅ Enabled" : "❌ Disabled"}'),
                  Text('Release Mode: ${kReleaseMode ? "✅ Enabled" : "❌ Disabled"}'),
                  const SizedBox(height: 16),
                  const Text(
                    'Performance Tools Status:',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 8),
                  Text('Performance Monitor: ${_showPerformanceMonitor ? "🟢 Active" : "🔴 Inactive"}'),
                  Text('FPS Monitor: ${_showFPSMonitor ? "🟢 Active" : "🔴 Inactive"}'),
                ],
              ),
            ),
          ),

          const SizedBox(height: 16),

          // Test Performance Section
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Performance Test',
                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 16),
                  const Text('Test app performance with heavy animations:'),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () {
                      _showPerformanceTestDialog();
                    },
                    child: const Text('Run Performance Test'),
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(height: 32),

          // Warning
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.orange.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.orange),
            ),
            child: const Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(Icons.warning, color: Colors.orange),
                    SizedBox(width: 8),
                    Text(
                      'Debug Tools Warning',
                      style: TextStyle(fontWeight: FontWeight.bold),
                    ),
                  ],
                ),
                SizedBox(height: 8),
                Text('These tools are for development purposes only and may impact app performance. They are automatically disabled in release builds.'),
              ],
            ),
          ),
        ],
      ),
    );

    // Wrap with performance monitors if enabled
    if (_showPerformanceMonitor) {
      child = PerformanceMonitor(child: child);
    }
    if (_showFPSMonitor) {
      child = FPSMonitor(child: child);
    }

    return child;
  }

  void _showPerformanceTestDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Performance Test'),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('This will create heavy animations to test performance monitoring.'),
            SizedBox(height: 16),
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('Watch the performance monitors for changes in FPS and memory usage.'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );

    // Auto close after 3 seconds
    Future.delayed(const Duration(seconds: 3), () {
      if (mounted) {
        Navigator.of(context).pop();
      }
    });
  }
}
