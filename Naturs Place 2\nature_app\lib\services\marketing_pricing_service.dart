import 'package:flutter/foundation.dart';
import '../models/user_models.dart';
import 'postgres_service.dart';

/// Service for managing marketing services as paid add-ons
class MarketingPricingService {
  static final MarketingPricingService _instance = MarketingPricingService._internal();
  factory MarketingPricingService() => _instance;
  MarketingPricingService._internal();

  final PostgreSQLService _postgresService = PostgreSQLService();

  /// Initialize marketing pricing service
  Future<void> initialize() async {
    await _postgresService.initialize();
    debugPrint('✅ Marketing Pricing Service initialized');
  }

  /// Get all available marketing services with pricing
  Map<String, MarketingService> getMarketingServices() {
    return {
      // Featured Product Services
      'featured_product_basic': MarketingService(
        id: 'featured_product_basic',
        name: 'Featured Product - Basic',
        description: 'Feature your product in category listings',
        price: 19.99,
        duration: Duration(days: 7),
        category: MarketingCategory.featuredProducts,
        tierRequirement: VendorTier.basic,
      ),
      'featured_product_premium': MarketingService(
        id: 'featured_product_premium',
        name: 'Featured Product - Premium',
        description: 'Feature your product on homepage and category',
        price: 39.99,
        duration: Duration(days: 7),
        category: MarketingCategory.featuredProducts,
        tierRequirement: VendorTier.standard,
      ),
      'featured_product_spotlight': MarketingService(
        id: 'featured_product_spotlight',
        name: 'Product Spotlight',
        description: 'Top placement with enhanced visibility',
        price: 79.99,
        duration: Duration(days: 7),
        category: MarketingCategory.featuredProducts,
        tierRequirement: VendorTier.premium,
      ),

      // Homepage Banner Services
      'homepage_banner_small': MarketingService(
        id: 'homepage_banner_small',
        name: 'Homepage Banner - Small',
        description: 'Small banner on homepage sidebar',
        price: 149.99,
        duration: Duration(days: 7),
        category: MarketingCategory.banners,
        tierRequirement: VendorTier.standard,
      ),
      'homepage_banner_large': MarketingService(
        id: 'homepage_banner_large',
        name: 'Homepage Banner - Large',
        description: 'Large banner on homepage header',
        price: 299.99,
        duration: Duration(days: 7),
        category: MarketingCategory.banners,
        tierRequirement: VendorTier.premium,
      ),
      'homepage_banner_hero': MarketingService(
        id: 'homepage_banner_hero',
        name: 'Hero Banner',
        description: 'Full-width hero banner with prime placement',
        price: 599.99,
        duration: Duration(days: 7),
        category: MarketingCategory.banners,
        tierRequirement: VendorTier.premium,
      ),

      // Email Marketing Services
      'email_campaign_basic': MarketingService(
        id: 'email_campaign_basic',
        name: 'Email Campaign - Basic',
        description: 'Send to 1,000 subscribers',
        price: 49.99,
        duration: Duration(days: 1), // One-time send
        category: MarketingCategory.email,
        tierRequirement: VendorTier.standard,
      ),
      'email_campaign_premium': MarketingService(
        id: 'email_campaign_premium',
        name: 'Email Campaign - Premium',
        description: 'Send to 5,000 subscribers with analytics',
        price: 99.99,
        duration: Duration(days: 1),
        category: MarketingCategory.email,
        tierRequirement: VendorTier.premium,
      ),
      'email_campaign_enterprise': MarketingService(
        id: 'email_campaign_enterprise',
        name: 'Email Campaign - Enterprise',
        description: 'Send to 20,000 subscribers with full analytics',
        price: 199.99,
        duration: Duration(days: 1),
        category: MarketingCategory.email,
        tierRequirement: VendorTier.enterprise,
      ),

      // Social Media Services
      'social_media_basic': MarketingService(
        id: 'social_media_basic',
        name: 'Social Media Promotion - Basic',
        description: 'Post on 2 platforms (Instagram, Facebook)',
        price: 79.99,
        duration: Duration(days: 1),
        category: MarketingCategory.socialMedia,
        tierRequirement: VendorTier.premium,
      ),
      'social_media_premium': MarketingService(
        id: 'social_media_premium',
        name: 'Social Media Promotion - Premium',
        description: 'Post on 4 platforms with engagement boost',
        price: 149.99,
        duration: Duration(days: 1),
        category: MarketingCategory.socialMedia,
        tierRequirement: VendorTier.premium,
      ),

      // SEO Services
      'seo_optimization_basic': MarketingService(
        id: 'seo_optimization_basic',
        name: 'SEO Optimization - Basic',
        description: 'Optimize 5 products for search',
        price: 99.99,
        duration: Duration(days: 30),
        category: MarketingCategory.seo,
        tierRequirement: VendorTier.standard,
      ),
      'seo_optimization_premium': MarketingService(
        id: 'seo_optimization_premium',
        name: 'SEO Optimization - Premium',
        description: 'Optimize 20 products with keyword research',
        price: 299.99,
        duration: Duration(days: 30),
        category: MarketingCategory.seo,
        tierRequirement: VendorTier.premium,
      ),

      // Content Creation Services
      'content_creation_basic': MarketingService(
        id: 'content_creation_basic',
        name: 'Content Creation - Basic',
        description: 'Professional product descriptions (5 products)',
        price: 149.99,
        duration: Duration(days: 7), // Delivery time
        category: MarketingCategory.content,
        tierRequirement: VendorTier.standard,
      ),
      'content_creation_premium': MarketingService(
        id: 'content_creation_premium',
        name: 'Content Creation - Premium',
        description: 'Professional photos + descriptions (10 products)',
        price: 399.99,
        duration: Duration(days: 14),
        category: MarketingCategory.content,
        tierRequirement: VendorTier.premium,
      ),
    };
  }

  /// Get marketing services available to a specific vendor tier
  List<MarketingService> getAvailableServices(VendorTier vendorTier) {
    final allServices = getMarketingServices();
    return allServices.values
        .where((service) => service.tierRequirement.index <= vendorTier.index)
        .toList();
  }

  /// Get marketing services by category
  List<MarketingService> getServicesByCategory(MarketingCategory category, VendorTier vendorTier) {
    return getAvailableServices(vendorTier)
        .where((service) => service.category == category)
        .toList();
  }

  /// Purchase a marketing service
  Future<MarketingPurchaseResult> purchaseMarketingService({
    required String vendorId,
    required String serviceId,
    required Map<String, dynamic> serviceDetails,
  }) async {
    try {
      final services = getMarketingServices();
      final service = services[serviceId];
      
      if (service == null) {
        return MarketingPurchaseResult(
          success: false,
          error: 'Marketing service not found',
        );
      }

      // Validate vendor tier requirement
      final vendor = await _getVendorById(vendorId);
      if (vendor == null) {
        return MarketingPurchaseResult(
          success: false,
          error: 'Vendor not found',
        );
      }

      if (vendor.tier.index < service.tierRequirement.index) {
        return MarketingPurchaseResult(
          success: false,
          error: 'Service requires ${service.tierRequirement.displayName} tier or higher',
          requiredTier: service.tierRequirement,
        );
      }

      // Process payment
      final paymentResult = await _processMarketingPayment(vendorId, service.price);
      if (!paymentResult.success) {
        return MarketingPurchaseResult(
          success: false,
          error: 'Payment failed: ${paymentResult.error}',
        );
      }

      // Create marketing campaign
      final campaignId = await _createMarketingCampaign(
        vendorId: vendorId,
        service: service,
        details: serviceDetails,
        paymentId: paymentResult.transactionId!,
      );

      return MarketingPurchaseResult(
        success: true,
        campaignId: campaignId,
        service: service,
        totalCost: service.price,
        message: 'Marketing service purchased successfully',
      );

    } catch (e) {
      debugPrint('❌ Failed to purchase marketing service: $e');
      return MarketingPurchaseResult(
        success: false,
        error: 'Purchase failed',
      );
    }
  }

  /// Get vendor's marketing spending history
  Future<MarketingSpendingReport> getVendorSpendingReport(String vendorId) async {
    try {
      final campaigns = await _getVendorMarketingCampaigns(vendorId);
      
      double totalSpent = 0;
      final spendingByCategory = <MarketingCategory, double>{};
      final monthlySpending = <String, double>{};
      
      for (final campaign in campaigns) {
        totalSpent += campaign.cost;
        
        // Category spending
        spendingByCategory[campaign.category] = 
            (spendingByCategory[campaign.category] ?? 0) + campaign.cost;
        
        // Monthly spending
        final monthKey = '${campaign.createdAt.year}-${campaign.createdAt.month.toString().padLeft(2, '0')}';
        monthlySpending[monthKey] = (monthlySpending[monthKey] ?? 0) + campaign.cost;
      }

      return MarketingSpendingReport(
        vendorId: vendorId,
        totalSpent: totalSpent,
        campaignCount: campaigns.length,
        spendingByCategory: spendingByCategory,
        monthlySpending: monthlySpending,
        averageCampaignCost: campaigns.isNotEmpty ? totalSpent / campaigns.length : 0,
        lastCampaignDate: campaigns.isNotEmpty ? campaigns.last.createdAt : null,
      );
    } catch (e) {
      debugPrint('❌ Failed to get spending report: $e');
      return MarketingSpendingReport.empty(vendorId);
    }
  }

  /// Get marketing ROI analytics
  Future<MarketingROIReport> getMarketingROI(String vendorId) async {
    try {
      final campaigns = await _getVendorMarketingCampaigns(vendorId);

      double totalMarketingSpend = campaigns.fold(0.0, (sum, campaign) => sum + campaign.cost);
      double attributedRevenue = 0.0;

      // Calculate attributed revenue (simplified - in production, use proper attribution)
      for (final campaign in campaigns) {
        attributedRevenue += await _calculateCampaignRevenue(campaign.id);
      }

      final double roi = totalMarketingSpend > 0 ? (attributedRevenue - totalMarketingSpend) / totalMarketingSpend : 0.0;

      return MarketingROIReport(
        vendorId: vendorId,
        totalSpent: totalMarketingSpend,
        attributedRevenue: attributedRevenue,
        roi: roi,
        roiPercentage: roi * 100.0,
        campaignCount: campaigns.length,
        averageRevenuePerCampaign: campaigns.isNotEmpty ? attributedRevenue / campaigns.length.toDouble() : 0.0,
      );
    } catch (e) {
      debugPrint('❌ Failed to get ROI report: $e');
      return MarketingROIReport.empty(vendorId);
    }
  }

  // Private helper methods
  Future<VendorUser?> _getVendorById(String vendorId) async {
    // Mock implementation - replace with actual database query
    return null;
  }

  Future<PaymentResult> _processMarketingPayment(String vendorId, double amount) async {
    // Mock payment processing - replace with actual Stripe integration
    return PaymentResult(
      success: true,
      transactionId: 'mkt_${DateTime.now().millisecondsSinceEpoch}',
      amountCharged: amount,
    );
  }

  Future<String> _createMarketingCampaign({
    required String vendorId,
    required MarketingService service,
    required Map<String, dynamic> details,
    required String paymentId,
  }) async {
    // Mock campaign creation - replace with actual database insertion
    return 'campaign_${DateTime.now().millisecondsSinceEpoch}';
  }

  Future<List<MarketingCampaignRecord>> _getVendorMarketingCampaigns(String vendorId) async {
    // Mock implementation - replace with actual database query
    return [];
  }

  Future<double> _calculateCampaignRevenue(String campaignId) async {
    // Mock implementation - replace with actual revenue attribution
    return 0.0;
  }
}

/// Marketing service definition
class MarketingService {
  final String id;
  final String name;
  final String description;
  final double price;
  final Duration duration;
  final MarketingCategory category;
  final VendorTier tierRequirement;

  MarketingService({
    required this.id,
    required this.name,
    required this.description,
    required this.price,
    required this.duration,
    required this.category,
    required this.tierRequirement,
  });
}

/// Marketing service categories
enum MarketingCategory {
  featuredProducts,
  banners,
  email,
  socialMedia,
  seo,
  content,
}

/// Marketing purchase result
class MarketingPurchaseResult {
  final bool success;
  final String? campaignId;
  final MarketingService? service;
  final double? totalCost;
  final String? message;
  final String? error;
  final VendorTier? requiredTier;

  MarketingPurchaseResult({
    required this.success,
    this.campaignId,
    this.service,
    this.totalCost,
    this.message,
    this.error,
    this.requiredTier,
  });
}

/// Payment processing result
class PaymentResult {
  final bool success;
  final String? transactionId;
  final double? amountCharged;
  final String? error;

  PaymentResult({
    required this.success,
    this.transactionId,
    this.amountCharged,
    this.error,
  });
}

/// Marketing campaign record
class MarketingCampaignRecord {
  final String id;
  final String vendorId;
  final String serviceId;
  final double cost;
  final MarketingCategory category;
  final DateTime createdAt;
  final DateTime? endDate;

  MarketingCampaignRecord({
    required this.id,
    required this.vendorId,
    required this.serviceId,
    required this.cost,
    required this.category,
    required this.createdAt,
    this.endDate,
  });
}

/// Marketing spending report
class MarketingSpendingReport {
  final String vendorId;
  final double totalSpent;
  final int campaignCount;
  final Map<MarketingCategory, double> spendingByCategory;
  final Map<String, double> monthlySpending;
  final double averageCampaignCost;
  final DateTime? lastCampaignDate;

  MarketingSpendingReport({
    required this.vendorId,
    required this.totalSpent,
    required this.campaignCount,
    required this.spendingByCategory,
    required this.monthlySpending,
    required this.averageCampaignCost,
    this.lastCampaignDate,
  });

  factory MarketingSpendingReport.empty(String vendorId) {
    return MarketingSpendingReport(
      vendorId: vendorId,
      totalSpent: 0,
      campaignCount: 0,
      spendingByCategory: {},
      monthlySpending: {},
      averageCampaignCost: 0,
    );
  }
}

/// Marketing ROI report
class MarketingROIReport {
  final String vendorId;
  final double totalSpent;
  final double attributedRevenue;
  final double roi;
  final double roiPercentage;
  final int campaignCount;
  final double averageRevenuePerCampaign;

  MarketingROIReport({
    required this.vendorId,
    required this.totalSpent,
    required this.attributedRevenue,
    required this.roi,
    required this.roiPercentage,
    required this.campaignCount,
    required this.averageRevenuePerCampaign,
  });

  factory MarketingROIReport.empty(String vendorId) {
    return MarketingROIReport(
      vendorId: vendorId,
      totalSpent: 0,
      attributedRevenue: 0,
      roi: 0,
      roiPercentage: 0,
      campaignCount: 0,
      averageRevenuePerCampaign: 0,
    );
  }
}
