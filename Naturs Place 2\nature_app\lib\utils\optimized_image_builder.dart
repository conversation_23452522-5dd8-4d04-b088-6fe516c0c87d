import 'package:flutter/material.dart';
import 'network_image_handler.dart';

/// Optimized image builder with proper error handling and caching
class OptimizedImageBuilder {
  
  /// Build image for product cards with error handling
  static Widget buildCardImage({
    required String imageUrl,
    BoxFit fit = BoxFit.cover,
    double? width,
    double? height,
  }) {
    return NetworkImageHandler.buildRobustNetworkImage(
      imageUrl: imageUrl,
      fit: fit,
      width: width,
      height: height,
      borderRadius: 8.0,
    );
  }

  /// Build image for list items with error handling
  static Widget buildListImage({
    required String imageUrl,
    BoxFit fit = BoxFit.cover,
    double? width,
    double? height,
  }) {
    return NetworkImageHandler.buildRobustNetworkImage(
      imageUrl: imageUrl,
      fit: fit,
      width: width,
      height: height,
      borderRadius: 6.0,
    );
  }

  /// Build image for detail views with error handling
  static Widget buildDetailImage({
    required String imageUrl,
    BoxFit fit = BoxFit.cover,
    double? width,
    double? height,
  }) {
    return NetworkImageHandler.buildRobustNetworkImage(
      imageUrl: imageUrl,
      fit: fit,
      width: width,
      height: height,
      borderRadius: 12.0,
    );
  }

  // Legacy methods removed - now using NetworkImageHandler for robust image loading

  /// Validate if URL is properly formatted
  static bool _isValidUrl(String url) {
    try {
      final uri = Uri.parse(url);
      return uri.hasScheme && (uri.scheme == 'http' || uri.scheme == 'https');
    } catch (e) {
      return false;
    }
  }

  /// Preload image for better performance
  static Future<void> preloadImage(String imageUrl, BuildContext context) async {
    return NetworkImageHandler.preloadImage(imageUrl, context);
  }

  /// Check if image URL is accessible
  static Future<bool> isImageAccessible(String imageUrl) async {
    if (!_isValidUrl(imageUrl)) return false;

    try {
      // Simple validation - just check if URL can be parsed
      Uri.parse(imageUrl);
      debugPrint('🔍 Checking image accessibility: $imageUrl');
      return true; // Assume accessible for now
    } catch (e) {
      debugPrint('🔥 Image accessibility check failed: $imageUrl - $e');
      return false;
    }
  }

  /// Get fallback image URL for specific domains
  static String? getFallbackImageUrl(String originalUrl) {
    if (originalUrl.contains('pixabay.com')) {
      // Return a generic nature placeholder
      return 'https://images.unsplash.com/photo-1441974231531-c6227db76b6e?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=80';
    }
    if (originalUrl.contains('unsplash.com')) {
      // Return a different Unsplash nature image
      return 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=80';
    }
    return null;
  }

  /// Clear image cache
  static Future<void> clearCache() async {
    return NetworkImageHandler.clearCache();
  }

  /// Get cache size information
  static Future<String> getCacheInfo() async {
    try {
      // This is a placeholder - CachedNetworkImage doesn't provide direct cache size info
      return 'Cache info not available';
    } catch (e) {
      return 'Error getting cache info: $e';
    }
  }
}

/// Extension for easy image building
extension ImageUrlExtension on String {
  /// Build card image from URL string
  Widget toCardImage({BoxFit fit = BoxFit.cover}) {
    return OptimizedImageBuilder.buildCardImage(
      imageUrl: this,
      fit: fit,
    );
  }

  /// Build list image from URL string
  Widget toListImage({BoxFit fit = BoxFit.cover}) {
    return OptimizedImageBuilder.buildListImage(
      imageUrl: this,
      fit: fit,
    );
  }

  /// Build detail image from URL string
  Widget toDetailImage({BoxFit fit = BoxFit.cover}) {
    return OptimizedImageBuilder.buildDetailImage(
      imageUrl: this,
      fit: fit,
    );
  }
}
