import 'package:flutter/foundation.dart';
import 'lib/services/commission_service.dart';
import 'lib/models/commission_models.dart';
import 'lib/models/vendor_models.dart';

/// Test partner referral scenario:
/// - Vendor owes $10
/// - Partner earns $5  
/// - We keep $5
void main() async {
  print('🎯 Testing Partner Referral Commission Logic\n');

  try {
    // Initialize commission service
    print('🚀 Initializing commission service...');
    final commissionService = CommissionService();
    await commissionService.initialize();
    print('✅ Commission service initialized!\n');

    // Test scenario: $100 sale with both vendor and partner involved
    print('📋 Test Scenario:');
    print('   • Customer buys $100 product via partner referral');
    print('   • Vendor commission rate: 10% (vendor owes us $10)');
    print('   • Partner commission rate: 5% (we pay partner $5)');
    print('   • Expected net profit: $5\n');

    // Process transaction with both vendor and affiliate
    print('💳 Processing partner referral transaction...');
    final testTransaction = Transaction(
      id: 'txn_partner_referral_${DateTime.now().millisecondsSinceEpoch}',
      orderId: 'order_referral_001',
      customerId: 'customer_referred_001',
      vendorId: 'vendor_healing_herbs',
      affiliateId: 'partner_wellness_guru', // Partner referral
      productId: 'product_vitamin_c',
      productName: 'Organic Vitamin C - 90 Capsules',
      category: ProductCategory.vitamins,
      productPrice: 100.00, // $100 product
      quantity: 1,
      subtotal: 100.00,
      tax: 0.00,
      shipping: 0.00,
      total: 100.00, // Total $100
      status: TransactionStatus.completed,
      createdAt: DateTime.now(),
      completedAt: DateTime.now(),
      metadata: '{"referral_source": "partner_link", "campaign": "wellness_promotion"}',
    );

    await commissionService.processTransaction(testTransaction);
    print('✅ Transaction processed successfully!\n');

    // Analyze commissions created
    print('📊 Commission Analysis:');
    final commissions = commissionService.getCommissions();
    final transactionCommissions = commissions.where((c) => c.transactionId == testTransaction.id).toList();

    double totalVendorOwesUs = 0.0;
    double totalWePayPartners = 0.0;
    double netProfit = 0.0;

    for (final commission in transactionCommissions) {
      print('\n🔍 Commission Details:');
      print('   Type: ${commission.partnerType}');
      print('   Partner ID: ${commission.partnerId}');
      print('   Commission Rate: ${commission.commissionRate}%');
      print('   Commission Amount: \$${commission.commissionAmount.toStringAsFixed(2)}');
      print('   App Revenue Impact: \$${commission.appRevenue.toStringAsFixed(2)}');
      print('   Status: ${commission.status.name}');
      
      if (commission.notes != null) {
        print('   Notes: ${commission.notes}');
      }

      if (commission.partnerType == 'vendor') {
        totalVendorOwesUs += commission.commissionAmount;
        print('   ✅ Vendor owes us: \$${commission.commissionAmount.toStringAsFixed(2)}');
      } else if (commission.partnerType == 'affiliate') {
        totalWePayPartners += commission.commissionAmount;
        print('   ✅ We pay partner: \$${commission.commissionAmount.toStringAsFixed(2)}');
      }

      netProfit += commission.appRevenue;
    }

    // Verify the assertion
    print('\n🎯 Assertion Verification:');
    print('   Expected: Vendor owes \$10, Partner earns \$5, We keep \$5');
    print('   Actual Results:');
    print('   • Vendor owes us: \$${totalVendorOwesUs.toStringAsFixed(2)}');
    print('   • We pay partner: \$${totalWePayPartners.toStringAsFixed(2)}');
    print('   • Net profit for us: \$${netProfit.toStringAsFixed(2)}');

    // Check if assertion passes
    final vendorOwesCorrect = (totalVendorOwesUs - 10.0).abs() < 0.01;
    final partnerEarnsCorrect = (totalWePayPartners - 5.0).abs() < 0.01;
    final netProfitCorrect = (netProfit - 5.0).abs() < 0.01;

    print('\n✅ Assertion Results:');
    print('   Vendor owes \$10: ${vendorOwesCorrect ? "✅ PASS" : "❌ FAIL"}');
    print('   Partner earns \$5: ${partnerEarnsCorrect ? "✅ PASS" : "❌ FAIL"}');
    print('   We keep \$5: ${netProfitCorrect ? "✅ PASS" : "❌ FAIL"}');

    if (vendorOwesCorrect && partnerEarnsCorrect && netProfitCorrect) {
      print('\n🎉 ALL ASSERTIONS PASSED!');
      print('📈 Partner referral commission logic is working correctly!');
    } else {
      print('\n❌ ASSERTION FAILED!');
      print('🔧 Commission calculations need adjustment.');
    }

    // Test PostgreSQL sync
    print('\n🔄 Syncing to PostgreSQL...');
    await commissionService.syncToPostgreSQL();
    print('✅ Data synchronized to PostgreSQL successfully!');

    // Show financial summary
    print('\n💰 Financial Summary:');
    print('   Transaction Amount: \$${testTransaction.total}');
    print('   Revenue from Vendor: +\$${totalVendorOwesUs.toStringAsFixed(2)}');
    print('   Payout to Partner: -\$${totalWePayPartners.toStringAsFixed(2)}');
    print('   Net Profit: \$${netProfit.toStringAsFixed(2)}');
    print('   Profit Margin: ${((netProfit / testTransaction.total) * 100).toStringAsFixed(1)}%');

  } catch (e, stackTrace) {
    print('❌ Test failed with error: $e');
    print('📋 Stack trace: $stackTrace');
    print('\n🔧 Troubleshooting:');
    print('   • Check commission rate configurations');
    print('   • Verify vendor and partner exist in system');
    print('   • Ensure PostgreSQL connection is working');
  }
}

/// Helper function to create test vendor if needed
Future<void> createTestVendor() async {
  // This would create a test vendor with 10% commission rate
  // Implementation depends on your vendor service
}

/// Helper function to create test partner if needed  
Future<void> createTestPartner() async {
  // This would create a test partner with 5% commission rate
  // Implementation depends on your partner service
}
