import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../services/performance_optimization_service.dart';

/// Performance monitoring widget for debug builds
class PerformanceMonitor extends StatefulWidget {
  final Widget child;
  final bool showOverlay;

  const PerformanceMonitor({
    super.key,
    required this.child,
    this.showOverlay = kDebugMode,
  });

  @override
  State<PerformanceMonitor> createState() => _PerformanceMonitorState();
}

class _PerformanceMonitorState extends State<PerformanceMonitor> {
  Timer? _monitoringTimer;
  final Map<String, dynamic> _performanceData = {};
  bool _isVisible = false;

  @override
  void initState() {
    super.initState();
    if (widget.showOverlay && kDebugMode) {
      _startMonitoring();
    }
  }

  @override
  void dispose() {
    _monitoringTimer?.cancel();
    super.dispose();
  }

  void _startMonitoring() {
    _monitoringTimer = Timer.periodic(const Duration(seconds: 2), (timer) async {
      await _updatePerformanceData();
      if (mounted) {
        setState(() {});
      }
    });
  }

  Future<void> _updatePerformanceData() async {
    try {
      // Memory usage
      if (Platform.isAndroid || Platform.isIOS || Platform.isLinux || Platform.isMacOS || Platform.isWindows) {
        _performanceData['memoryUsage'] = ProcessInfo.currentRss / 1024 / 1024; // MB
      }

      // Cache statistics
      final cacheStats = await PerformanceOptimizationService.getCacheStats();
      _performanceData['cacheStats'] = cacheStats;

      // Frame rate (approximate)
      _performanceData['timestamp'] = DateTime.now();

      // Widget count (approximate)
      _performanceData['widgetCount'] = _getApproximateWidgetCount();

    } catch (e) {
      debugPrint('❌ Error updating performance data: $e');
    }
  }

  int _getApproximateWidgetCount() {
    // This is a rough estimation
    return context.findRenderObject()?.debugDescribeChildren().length ?? 0;
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        widget.child,
        if (widget.showOverlay && kDebugMode) _buildPerformanceOverlay(),
      ],
    );
  }

  Widget _buildPerformanceOverlay() {
    return Positioned(
      top: MediaQuery.of(context).padding.top + 10,
      right: 10,
      child: GestureDetector(
        onTap: () => setState(() => _isVisible = !_isVisible),
        child: Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Colors.black.withValues(alpha: 0.8),
            borderRadius: BorderRadius.circular(8),
          ),
          child: _isVisible ? _buildDetailedStats() : _buildCompactStats(),
        ),
      ),
    );
  }

  Widget _buildCompactStats() {
    final memoryUsage = _performanceData['memoryUsage'] ?? 0.0;
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        const Icon(Icons.memory, color: Colors.white, size: 16),
        const SizedBox(width: 4),
        Text(
          '${memoryUsage.toStringAsFixed(1)}MB',
          style: const TextStyle(
            color: Colors.white,
            fontSize: 12,
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }

  Widget _buildDetailedStats() {
    final memoryUsage = _performanceData['memoryUsage'] ?? 0.0;
    final cacheStats = _performanceData['cacheStats'] as Map<String, dynamic>? ?? {};
    final widgetCount = _performanceData['widgetCount'] ?? 0;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        _buildStatRow('Memory', '${memoryUsage.toStringAsFixed(1)} MB', Icons.memory),
        _buildStatRow('Cache Entries', '${cacheStats['validEntries'] ?? 0}', Icons.storage),
        _buildStatRow('Memory Cache', '${cacheStats['memoryCacheSize'] ?? 0}', Icons.cached),
        _buildStatRow('Widgets', '$widgetCount', Icons.widgets),
        const SizedBox(height: 8),
        Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildActionButton('Clear Cache', Icons.clear_all, _clearCache),
            const SizedBox(width: 8),
            _buildActionButton('GC', Icons.delete_sweep, _forceGarbageCollection),
          ],
        ),
      ],
    );
  }

  Widget _buildStatRow(String label, String value, IconData icon) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, color: Colors.white, size: 14),
          const SizedBox(width: 6),
          Text(
            '$label: ',
            style: const TextStyle(color: Colors.white70, fontSize: 11),
          ),
          Text(
            value,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 11,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButton(String label, IconData icon, VoidCallback onPressed) {
    return GestureDetector(
      onTap: onPressed,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 4),
        decoration: BoxDecoration(
          color: const Color(0xFF22c55e),
          borderRadius: BorderRadius.circular(4),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(icon, color: Colors.white, size: 12),
            const SizedBox(width: 4),
            Text(
              label,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 10,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _clearCache() async {
    await PerformanceOptimizationService.clearAllCache();
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Cache cleared'),
          duration: Duration(seconds: 1),
        ),
      );
    }
  }

  void _forceGarbageCollection() {
    // Force garbage collection (development only)
    if (kDebugMode) {
      SystemChannels.platform.invokeMethod('SystemNavigator.pop');
    }
  }
}

/// Performance metrics collector
class PerformanceMetrics {
  static final Map<String, List<double>> _metrics = {};
  static final Map<String, DateTime> _startTimes = {};

  /// Start timing an operation
  static void startTiming(String operation) {
    _startTimes[operation] = DateTime.now();
  }

  /// End timing an operation and record the duration
  static void endTiming(String operation) {
    final startTime = _startTimes[operation];
    if (startTime != null) {
      final duration = DateTime.now().difference(startTime).inMicroseconds / 1000.0; // ms
      _recordMetric(operation, duration);
      _startTimes.remove(operation);
    }
  }

  /// Record a custom metric
  static void recordMetric(String name, double value) {
    _recordMetric(name, value);
  }

  static void _recordMetric(String name, double value) {
    _metrics[name] ??= [];
    _metrics[name]!.add(value);
    
    // Keep only last 100 measurements
    if (_metrics[name]!.length > 100) {
      _metrics[name]!.removeAt(0);
    }
  }

  /// Get average for a metric
  static double getAverage(String name) {
    final values = _metrics[name];
    if (values == null || values.isEmpty) return 0.0;
    return values.reduce((a, b) => a + b) / values.length;
  }

  /// Get latest value for a metric
  static double getLatest(String name) {
    final values = _metrics[name];
    if (values == null || values.isEmpty) return 0.0;
    return values.last;
  }

  /// Get all metrics
  static Map<String, Map<String, double>> getAllMetrics() {
    final result = <String, Map<String, double>>{};
    for (final entry in _metrics.entries) {
      if (entry.value.isNotEmpty) {
        result[entry.key] = {
          'average': getAverage(entry.key),
          'latest': getLatest(entry.key),
          'min': entry.value.reduce((a, b) => a < b ? a : b),
          'max': entry.value.reduce((a, b) => a > b ? a : b),
          'count': entry.value.length.toDouble(),
        };
      }
    }
    return result;
  }

  /// Clear all metrics
  static void clearMetrics() {
    _metrics.clear();
    _startTimes.clear();
  }
}

/// Widget performance wrapper
class PerformanceWrapper extends StatefulWidget {
  final Widget child;
  final String name;

  const PerformanceWrapper({
    super.key,
    required this.child,
    required this.name,
  });

  @override
  State<PerformanceWrapper> createState() => _PerformanceWrapperState();
}

class _PerformanceWrapperState extends State<PerformanceWrapper> {
  @override
  void initState() {
    super.initState();
    if (kDebugMode) {
      PerformanceMetrics.startTiming('${widget.name}_build');
    }
  }

  @override
  Widget build(BuildContext context) {
    if (kDebugMode) {
      PerformanceMetrics.endTiming('${widget.name}_build');
    }
    return widget.child;
  }
}

/// FPS monitor
class FPSMonitor extends StatefulWidget {
  final Widget child;

  const FPSMonitor({super.key, required this.child});

  @override
  State<FPSMonitor> createState() => _FPSMonitorState();
}

class _FPSMonitorState extends State<FPSMonitor> with TickerProviderStateMixin {
  late AnimationController _controller;
  int _frameCount = 0;
  DateTime _lastTime = DateTime.now();
  double _fps = 0.0;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(seconds: 1),
      vsync: this,
    )..addListener(_onFrame);
    _controller.repeat();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void _onFrame() {
    _frameCount++;
    final now = DateTime.now();
    final elapsed = now.difference(_lastTime);
    
    if (elapsed.inMilliseconds >= 1000) {
      setState(() {
        _fps = _frameCount / elapsed.inSeconds;
        _frameCount = 0;
        _lastTime = now;
      });
      
      if (kDebugMode) {
        PerformanceMetrics.recordMetric('fps', _fps);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        widget.child,
        if (kDebugMode)
          Positioned(
            top: MediaQuery.of(context).padding.top + 50,
            left: 10,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: _fps < 30 ? Colors.red : _fps < 50 ? Colors.orange : Colors.green,
                borderRadius: BorderRadius.circular(4),
              ),
              child: Text(
                'FPS: ${_fps.toStringAsFixed(1)}',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
      ],
    );
  }
}
