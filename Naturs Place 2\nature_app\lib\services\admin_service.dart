import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';

class AdminService extends ChangeNotifier {
  static final AdminService _instance = AdminService._internal();
  factory AdminService() => _instance;
  AdminService._internal();

  static const String _adminPin = '2024';
  static const String _adminSessionKey = 'admin_session';
  bool _isAdminAuthenticated = false;
  DateTime? _sessionStartTime;
  static const Duration _sessionDuration = Duration(hours: 2); // Admin session expires after 2 hours

  bool get isAdminAuthenticated => _isAdminAuthenticated && isSessionValid;
  bool get isSessionValid => _sessionStartTime != null &&
      DateTime.now().difference(_sessionStartTime!) < _sessionDuration;

  Future<void> initialize() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final sessionData = prefs.getString(_adminSessionKey);
      
      if (sessionData != null) {
        final sessionTime = DateTime.parse(sessionData);
        if (DateTime.now().difference(sessionTime) < _sessionDuration) {
          _isAdminAuthenticated = true;
          _sessionStartTime = sessionTime;
        } else {
          // Session expired, clear it
          await clearAdminSession();
        }
      }
    } catch (e) {
      debugPrint('Error loading admin session: $e');
    }
    notifyListeners();
  }

  Future<bool> authenticateAdmin(String pin) async {
    if (pin == _adminPin) {
      _isAdminAuthenticated = true;
      _sessionStartTime = DateTime.now();
      
      try {
        final prefs = await SharedPreferences.getInstance();
        await prefs.setString(_adminSessionKey, _sessionStartTime!.toIso8601String());
      } catch (e) {
        debugPrint('Error saving admin session: $e');
      }
      
      notifyListeners();
      return true;
    }
    return false;
  }

  Future<void> clearAdminSession() async {
    _isAdminAuthenticated = false;
    _sessionStartTime = null;
    
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_adminSessionKey);
    } catch (e) {
      debugPrint('Error clearing admin session: $e');
    }
    
    notifyListeners();
  }

  String? getRemainingSessionTime() {
    if (!isSessionValid) return null;
    
    final remaining = _sessionDuration - DateTime.now().difference(_sessionStartTime!);
    final hours = remaining.inHours;
    final minutes = remaining.inMinutes % 60;
    
    if (hours > 0) {
      return '${hours}h ${minutes}m';
    } else {
      return '${minutes}m';
    }
  }

  // Admin-only features access control
  bool canAccessForceUpdate() => isAdminAuthenticated;
  bool canAccessAdminPanel() => isAdminAuthenticated;
  bool canViewAppRevenue() => isAdminAuthenticated;
  bool canManageVendors() => isAdminAuthenticated;
  bool canViewCommissionData() => isAdminAuthenticated;
}
