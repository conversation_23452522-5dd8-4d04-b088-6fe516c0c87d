import '../providers/app_state.dart';

class ProductDataset {
  static List<Product> getAllProducts() {
    return [
      Product(
        id: '1',
        name: 'Organic Turmeric Powder',
        description: 'Premium turmeric powder from Kerala, India. Rich in curcumin with anti-inflammatory properties.',
        imageUrl: 'https://images.unsplash.com/photo-1597362925123-77861d3fbac7?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=80',
        price: 12.99,
        category: 'Dried Herbs',
        rating: 4.5,
        reviewCount: 128,
        seller: 'Kerala Spices Co.',
      ),
      Product(
        id: '2',
        name: 'Pure Aloe Vera Gel',
        description: 'Cold pressed, organic aloe vera gel for skin healing and moisturizing.',
        imageUrl: 'https://images.unsplash.com/photo-1595152772835-219674b2a8a6?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=80',
        price: 9.99,
        category: 'Salves & Balms',
        rating: 4.0,
        reviewCount: 97,
        seller: 'Natural Remedies Inc.',
      ),
      Product(
        id: '3',
        name: 'Ginseng Root Extract',
        description: 'Premium Korean red ginseng extract for energy and vitality support.',
        imageUrl: 'https://images.unsplash.com/photo-**********-7f47ccb76574?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=80',
        price: 24.99,
        category: 'Tinctures',
        rating: 4.8,
        reviewCount: 203,
        seller: 'Asian Botanicals',
      ),
      Product(
        id: '4',
        name: 'Lavender Essential Oil',
        description: 'Pure therapeutic grade lavender oil for relaxation and aromatherapy.',
        imageUrl: 'https://images.unsplash.com/photo-1611909023032-2d6b3134ecba?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=80',
        price: 18.99,
        category: 'Essential Oils',
        rating: 4.7,
        reviewCount: 156,
        seller: 'Aromatherapy Plus',
      ),
      Product(
        id: '5',
        name: 'Echinacea Vitamins & Minerals',
        description: 'Organic echinacea vitamins & minerals for immune system support and wellness.',
        imageUrl: 'https://images.unsplash.com/photo-1584308666744-24d5c474f2ae?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=80',
        price: 16.99,
        category: 'Vitamins & Minerals',
        rating: 4.6,
        reviewCount: 89,
        seller: 'Herbal Health Co.',
      ),
      Product(
        id: '6',
        name: 'Chamomile Tea Blend',
        description: 'Organic chamomile tea blend for relaxation and better sleep.',
        imageUrl: 'https://images.unsplash.com/photo-**********-7f47ccb76574?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=80',
        price: 8.99,
        category: 'Teas',
        rating: 4.4,
        reviewCount: 67,
        seller: 'Mountain Tea Co.',
      ),
      Product(
        id: '7',
        name: 'Manuka Honey',
        description: 'Raw Manuka honey with natural antibacterial and healing properties.',
        imageUrl: 'https://images.unsplash.com/photo-1587049352846-4a222e784d38?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=80',
        price: 34.99,
        category: 'Natural Sweeteners',
        rating: 4.9,
        reviewCount: 245,
        seller: 'New Zealand Naturals',
      ),
      Product(
        id: '8',
        name: 'Spirulina Powder',
        description: 'Organic spirulina powder superfood rich in protein and nutrients.',
        imageUrl: 'https://images.unsplash.com/photo-1609501676725-7186f734b2b0?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=80',
        price: 19.99,
        category: 'Superfoods',
        rating: 4.3,
        reviewCount: 112,
        seller: 'Green Superfood Co.',
      ),
      Product(
        id: '9',
        name: 'Arnica Cream',
        description: 'Natural arnica cream for muscle soreness and bruise relief.',
        imageUrl: 'https://images.unsplash.com/photo-1556228720-195a672e8a03?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=80',
        price: 13.99,
        category: 'Salves & Balms',
        rating: 4.2,
        reviewCount: 78,
        seller: 'Mountain Remedies',
      ),
      Product(
        id: '10',
        name: 'Elderberry Syrup',
        description: 'Organic elderberry syrup for immune support and cold prevention.',
        imageUrl: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=80',
        price: 21.99,
        category: 'Syrups',
        rating: 4.6,
        reviewCount: 134,
        seller: 'Elderberry Farms',
      ),
    ];
  }

  static List<String> getAllCategories() {
    return [
      'All Products',
      'Dried Herbs',
      'Essential Oils',
      'Tinctures',
      'Vitamins & Minerals',
      'Teas',
      'Salves & Balms',
      'Superfoods',
      'Natural Sweeteners',
      'Syrups',
    ];
  }

  static List<Product> getProductsByCategory(String category) {
    if (category == 'All Products') return getAllProducts();
    return getAllProducts().where((product) => product.category == category).toList();
  }

  static List<Product> searchProducts(String query) {
    if (query.isEmpty) return getAllProducts();
    
    final lowercaseQuery = query.toLowerCase();
    return getAllProducts().where((product) =>
        product.name.toLowerCase().contains(lowercaseQuery) ||
        product.description.toLowerCase().contains(lowercaseQuery) ||
        product.category.toLowerCase().contains(lowercaseQuery) ||
        product.seller.toLowerCase().contains(lowercaseQuery)
    ).toList();
  }

  static Product? getProductById(String id) {
    try {
      return getAllProducts().firstWhere((product) => product.id == id);
    } catch (e) {
      return null;
    }
  }

  static List<Product> getFeaturedProducts() {
    // Return products with highest ratings
    final products = getAllProducts();
    products.sort((a, b) => b.rating.compareTo(a.rating));
    return products.take(6).toList();
  }

  static List<Product> getPopularProducts() {
    // Return products with most reviews
    final products = getAllProducts();
    products.sort((a, b) => b.reviewCount.compareTo(a.reviewCount));
    return products.take(8).toList();
  }

  static List<Product> getTopRatedProducts() {
    // Return products with rating >= 4.5
    return getAllProducts().where((product) => product.rating >= 4.5).toList();
  }

  static List<Product> getNewProducts() {
    // Return last 5 products (simulating newest additions)
    final products = getAllProducts();
    return products.reversed.take(5).toList();
  }
}
