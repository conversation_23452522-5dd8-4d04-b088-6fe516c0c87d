import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import '../providers/app_state.dart';
import 'subscription_screen.dart';

/// Comprehensive plan features screen showing all available features
class PlanFeaturesScreen extends StatelessWidget {
  const PlanFeaturesScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Plan Features'),
        backgroundColor: const Color(0xFF22c55e),
        foregroundColor: Colors.white,
      ),
      body: Consumer<AppState>(
        builder: (context, appState, child) {
          return SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Current plan status
                _buildCurrentPlanCard(appState),
                const SizedBox(height: 24),
                
                // Feature categories
                _buildFeatureCategory(
                  'Plant Scanning',
                  FontAwesomeIcons.camera,
                  Colors.green,
                  [
                    _FeatureItem('Monthly plant scans', free: '5 per month', premium: 'Unlimited', pro: 'Unlimited'),
                    _FeatureItem('Plant identification accuracy', free: 'Basic (70%)', premium: 'Advanced (95%)', pro: 'Professional (99%)'),
                    _FeatureItem('Scan history', free: 'Last 10 scans', premium: 'Unlimited history', pro: 'Unlimited + export'),
                    _FeatureItem('Offline scanning', free: '❌', premium: '✅', pro: '✅'),
                  ],
                ),
                
                const SizedBox(height: 24),
                
                _buildFeatureCategory(
                  'Plant Encyclopedia',
                  FontAwesomeIcons.book,
                  Colors.blue,
                  [
                    _FeatureItem('Plant database access', free: 'Limited (500 plants)', premium: 'Full (10,000+ plants)', pro: 'Professional (25,000+ plants)'),
                    _FeatureItem('Detailed plant information', free: 'Basic info', premium: 'Comprehensive details', pro: 'Clinical data included'),
                    _FeatureItem('High-resolution images', free: '1 per plant', premium: '5+ per plant', pro: '10+ per plant'),
                    _FeatureItem('Scientific research access', free: '❌', premium: 'Basic studies', pro: 'Full research database'),
                  ],
                ),
                
                const SizedBox(height: 24),
                
                _buildFeatureCategory(
                  'AI Health Assistant',
                  FontAwesomeIcons.robot,
                  Colors.purple,
                  [
                    _FeatureItem('Monthly questions', free: '3 questions', premium: 'Unlimited', pro: 'Unlimited + priority'),
                    _FeatureItem('Health recommendations', free: 'Basic suggestions', premium: 'Personalized advice', pro: 'Clinical-grade recommendations'),
                    _FeatureItem('Symptom analysis', free: '❌', premium: '✅', pro: '✅ + severity assessment'),
                    _FeatureItem('Drug interaction warnings', free: '❌', premium: 'Basic warnings', pro: 'Comprehensive analysis'),
                  ],
                ),
                
                const SizedBox(height: 24),
                
                _buildFeatureCategory(
                  'Disease Detection',
                  FontAwesomeIcons.stethoscope,
                  Colors.red,
                  [
                    _FeatureItem('Plant disease identification', free: '❌', premium: '✅', pro: '✅ + treatment plans'),
                    _FeatureItem('Treatment recommendations', free: '❌', premium: 'Basic treatments', pro: 'Professional protocols'),
                    _FeatureItem('Prevention tips', free: '❌', premium: '✅', pro: '✅ + seasonal guides'),
                    _FeatureItem('Disease progression tracking', free: '❌', premium: '❌', pro: '✅'),
                  ],
                ),
                
                const SizedBox(height: 24),
                
                _buildFeatureCategory(
                  'Professional Features',
                  FontAwesomeIcons.userDoctor,
                  Colors.orange,
                  [
                    _FeatureItem('Dosage calculations', free: '❌', premium: 'Basic guidelines', pro: 'Precise calculations'),
                    _FeatureItem('Preparation methods', free: 'Basic', premium: 'Detailed instructions', pro: 'Professional protocols'),

                  ],
                ),
                
                const SizedBox(height: 24),
                
                _buildFeatureCategory(
                  'Support & Community',
                  FontAwesomeIcons.users,
                  Colors.teal,
                  [
                    _FeatureItem('Customer support', free: 'Email only', premium: 'Priority email', pro: 'Phone + email priority'),
                    _FeatureItem('Community access', free: '✅', premium: '✅', pro: '✅ + expert forums'),
                    _FeatureItem('Educational content', free: 'Basic articles', premium: 'Premium content', pro: 'Professional courses'),
                    _FeatureItem('App updates', free: 'Standard', premium: 'Early access', pro: 'Beta features'),
                  ],
                ),
                
                const SizedBox(height: 32),
                
                // Upgrade button
                if (!appState.isPro)
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => const SubscriptionScreen(),
                          ),
                        );
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: const Color(0xFF22c55e),
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      child: Text(
                        appState.subscriptionTier == SubscriptionTier.free
                            ? 'Upgrade to Premium'
                            : 'Upgrade to Pro',
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
                
                const SizedBox(height: 16),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildCurrentPlanCard(AppState appState) {
    String planName = appState.subscriptionTier.name.toUpperCase();
    Color planColor = appState.isPro ? Colors.purple : appState.isPremium ? Colors.green : Colors.grey;
    
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [planColor, planColor.withValues(alpha: 0.7)],
        ),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                appState.isPro ? Icons.diamond : appState.isPremium ? Icons.star : Icons.person,
                color: Colors.white,
                size: 24,
              ),
              const SizedBox(width: 12),
              Text(
                'Current Plan: $planName',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            _getPlanDescription(appState.subscriptionTier),
            style: const TextStyle(
              color: Colors.white,
              fontSize: 14,
            ),
          ),
          if (!appState.isPremium) ...[
            const SizedBox(height: 12),
            Row(
              children: [
                const Icon(Icons.camera_alt, color: Colors.white, size: 16),
                const SizedBox(width: 8),
                Text(
                  '${appState.scansRemainingThisMonth} scans remaining this month',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }

  String _getPlanDescription(SubscriptionTier tier) {
    switch (tier) {
      case SubscriptionTier.free:
        return 'Perfect for casual plant enthusiasts - 5 scans per month';
      case SubscriptionTier.premium:
        return 'Ideal for plant lovers and health enthusiasts';
      case SubscriptionTier.pro:
        return 'For health practitioners and serious herbalists';
    }
  }

  Widget _buildFeatureCategory(String title, IconData icon, Color color, List<_FeatureItem> features) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            FaIcon(icon, color: color, size: 20),
            const SizedBox(width: 12),
            Text(
              title,
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        ...features.map((feature) => _buildFeatureRow(feature)),
      ],
    );
  }

  Widget _buildFeatureRow(_FeatureItem feature) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[200]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            feature.name,
            style: const TextStyle(
              fontWeight: FontWeight.w600,
              fontSize: 14,
            ),
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Expanded(
                child: _buildPlanColumn('Free', feature.free, Colors.grey),
              ),
              Expanded(
                child: _buildPlanColumn('Premium', feature.premium, Colors.green),
              ),
              Expanded(
                child: _buildPlanColumn('Pro', feature.pro, Colors.purple),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildPlanColumn(String planName, String featureValue, Color color) {
    return Column(
      children: [
        Text(
          planName,
          style: TextStyle(
            color: color,
            fontWeight: FontWeight.bold,
            fontSize: 12,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          featureValue,
          style: const TextStyle(
            fontSize: 11,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }
}

class _FeatureItem {
  final String name;
  final String free;
  final String premium;
  final String pro;

  _FeatureItem(this.name, {required this.free, required this.premium, required this.pro});
}
