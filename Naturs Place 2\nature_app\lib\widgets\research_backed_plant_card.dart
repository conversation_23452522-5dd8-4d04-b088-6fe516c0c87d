import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import '../models/research_backed_plant.dart';

/// Widget to display research-backed plant information with scientific evidence
class ResearchBackedPlantCard extends StatelessWidget {
  final ResearchBackedPlant plant;
  final VoidCallback? onTap;

  const ResearchBackedPlantCard({
    super.key,
    required this.plant,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 4,
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(16),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header with plant image and basic info
              Row(
                children: [
                  // Plant image
                  ClipRRect(
                    borderRadius: BorderRadius.circular(12),
                    child: Image.network(
                      plant.imageUrl,
                      width: 80,
                      height: 80,
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) {
                        return Container(
                          width: 80,
                          height: 80,
                          decoration: BoxDecoration(
                            color: Colors.grey[300],
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: const Icon(Icons.local_florist, size: 40),
                        );
                      },
                    ),
                  ),
                  
                  const SizedBox(width: 16),
                  
                  // Plant info
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          plant.name,
                          style: Theme.of(context).textTheme.titleLarge?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          plant.scientificName,
                          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            fontStyle: FontStyle.italic,
                            color: Colors.grey[600],
                          ),
                        ),
                        const SizedBox(height: 8),
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                          decoration: BoxDecoration(
                            color: const Color(0xFF22c55e).withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Text(
                            plant.category,
                            style: const TextStyle(
                              color: Color(0xFF22c55e),
                              fontSize: 12,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              
              const SizedBox(height: 16),
              
              // Evidence level indicator
              _buildEvidenceIndicator(),
              
              const SizedBox(height: 12),
              
              // Research summary
              _buildResearchSummary(),
              
              const SizedBox(height: 12),
              
              // Benefits
              _buildBenefits(),
              
              const SizedBox(height: 12),
              
              // Clinical studies indicator
              _buildClinicalStudiesIndicator(),
              
              if (plant.nutritionalProfile != null) ...[
                const SizedBox(height: 12),
                _buildNutritionalHighlights(),
              ],
            ],
          ),
        ),
      ),
    );
  }

  /// Build evidence level indicator
  Widget _buildEvidenceIndicator() {
    final evidenceLevel = plant.researchData.evidenceLevel;
    final efficacyScore = plant.researchData.efficacyScore;
    
    Color evidenceColor;
    String evidenceText;
    IconData evidenceIcon;
    
    switch (evidenceLevel) {
      case 'A':
        evidenceColor = Colors.green;
        evidenceText = 'Strong Evidence';
        evidenceIcon = FontAwesomeIcons.award;
        break;
      case 'B':
        evidenceColor = Colors.blue;
        evidenceText = 'Good Evidence';
        evidenceIcon = FontAwesomeIcons.certificate;
        break;
      case 'C':
        evidenceColor = Colors.orange;
        evidenceText = 'Moderate Evidence';
        evidenceIcon = FontAwesomeIcons.medal;
        break;
      default:
        evidenceColor = Colors.grey;
        evidenceText = 'Limited Evidence';
        evidenceIcon = FontAwesomeIcons.circleQuestion;
    }

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: evidenceColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: evidenceColor.withValues(alpha: 0.3)),
      ),
      child: Row(
        children: [
          FaIcon(evidenceIcon, color: evidenceColor, size: 16),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  evidenceText,
                  style: TextStyle(
                    color: evidenceColor,
                    fontWeight: FontWeight.bold,
                    fontSize: 14,
                  ),
                ),
                Text(
                  'Efficacy Score: ${efficacyScore.toStringAsFixed(1)}/10',
                  style: TextStyle(
                    color: evidenceColor,
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ),
          CircularProgressIndicator(
            value: efficacyScore / 10,
            backgroundColor: evidenceColor.withValues(alpha: 0.2),
            valueColor: AlwaysStoppedAnimation<Color>(evidenceColor),
            strokeWidth: 3,
          ),
        ],
      ),
    );
  }

  /// Build research summary
  Widget _buildResearchSummary() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.blue.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const FaIcon(FontAwesomeIcons.microscope, color: Colors.blue, size: 16),
              const SizedBox(width: 8),
              const Text(
                'Research Summary',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: Colors.blue,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              _buildResearchStat('Studies', plant.researchData.totalStudies.toString()),
              _buildResearchStat('Human Trials', plant.researchData.humanTrials.toString()),
              _buildResearchStat('Publications', plant.publications.length.toString()),
            ],
          ),
        ],
      ),
    );
  }

  /// Build research statistic
  Widget _buildResearchStat(String label, String value) {
    return Column(
      children: [
        Text(
          value,
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Colors.blue,
          ),
        ),
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey[600],
          ),
        ),
      ],
    );
  }

  /// Build benefits section
  Widget _buildBenefits() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Row(
          children: [
            FaIcon(FontAwesomeIcons.heartPulse, color: Color(0xFF22c55e), size: 16),
            SizedBox(width: 8),
            Text(
              'Proven Benefits',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: Color(0xFF22c55e),
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          runSpacing: 4,
          children: plant.researchData.provenBenefits.take(4).map((benefit) {
            return Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: const Color(0xFF22c55e).withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(16),
              ),
              child: Text(
                benefit,
                style: const TextStyle(
                  color: Color(0xFF22c55e),
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                ),
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  /// Build clinical studies indicator
  Widget _buildClinicalStudiesIndicator() {
    final studyCount = plant.clinicalStudies.length;
    final avgQuality = studyCount > 0
        ? plant.clinicalStudies.map((s) => s.qualityScore).reduce((a, b) => a + b) / studyCount
        : 0.0;

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.purple.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        children: [
          const FaIcon(FontAwesomeIcons.flask, color: Colors.purple, size: 16),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Clinical Studies',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: Colors.purple,
                  ),
                ),
                Text(
                  '$studyCount studies • Avg Quality: ${avgQuality.toStringAsFixed(1)}/10',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
          if (studyCount > 0)
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: Colors.purple.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                'Peer Reviewed',
                style: const TextStyle(
                  color: Colors.purple,
                  fontSize: 10,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
        ],
      ),
    );
  }

  /// Build nutritional highlights for fruits
  Widget _buildNutritionalHighlights() {
    final nutrition = plant.nutritionalProfile!;
    
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.orange.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Row(
            children: [
              FaIcon(FontAwesomeIcons.apple, color: Colors.orange, size: 16),
              SizedBox(width: 8),
              Text(
                'Nutritional Highlights',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: Colors.orange,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              _buildNutritionStat('Calories', '${nutrition.calories.round()}'),
              _buildNutritionStat('Vitamin C', '${nutrition.vitamins['vitamin_c']?.round() ?? 0}mg'),
              _buildNutritionStat('Fiber', '${nutrition.macronutrients['fiber']?.round() ?? 0}g'),
            ],
          ),
        ],
      ),
    );
  }

  /// Build nutrition statistic
  Widget _buildNutritionStat(String label, String value) {
    return Column(
      children: [
        Text(
          value,
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.bold,
            color: Colors.orange,
          ),
        ),
        Text(
          label,
          style: TextStyle(
            fontSize: 10,
            color: Colors.grey[600],
          ),
        ),
      ],
    );
  }
}
