import 'dart:convert';

import 'package:shelf/shelf.dart';
import 'package:shelf_router/shelf_router.dart';

import '../controllers/commission_controller.dart';

Router commissionRoutes() {
  final router = Router();

  // Calculate commissions
  router.post('/calculate', (Request request) async {
    final data = await request.readAsString();
    final json = jsonDecode(data) as Map<String, dynamic>;

    final result = CommissionController.calculateCommissions(
      json['saleAmount'] as double,
      json['vendorTier'] as String,
    );

    return Response.ok(jsonEncode(result));
  });

  return router;
}
