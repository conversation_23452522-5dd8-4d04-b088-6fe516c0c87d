import 'package:flutter/material.dart';
import 'package:camera/camera.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:provider/provider.dart';
import '../services/enhanced_camera_service.dart';
import '../services/simplified_plant_identification_service.dart';
import '../providers/app_state.dart';
import 'plant_identification_result_screen.dart';
import 'subscription_screen.dart';

/// Enhanced Plant Scanner Screen with 100% reliability
class EnhancedPlantScannerScreen extends StatefulWidget {
  const EnhancedPlantScannerScreen({super.key});

  @override
  State<EnhancedPlantScannerScreen> createState() => _EnhancedPlantScannerScreenState();
}

class _EnhancedPlantScannerScreenState extends State<EnhancedPlantScannerScreen> {
  CameraController? _cameraController;
  final SimplifiedPlantIdentificationService _identificationService = SimplifiedPlantIdentificationService();
  
  bool _isInitializing = true;
  bool _isScanning = false;
  bool _hasError = false;
  String _errorMessage = '';
  CameraState _cameraState = CameraState.uninitialized;

  @override
  void initState() {
    super.initState();
    _initializeServices();
  }

  /// Initialize camera and identification services
  Future<void> _initializeServices() async {
    try {
      setState(() {
        _isInitializing = true;
        _hasError = false;
      });

      // Listen to camera state changes
      EnhancedCameraService.stateStream.listen((state) {
        if (mounted) {
          setState(() {
            _cameraState = state;
          });
        }
      });

      // Initialize identification service
      await _identificationService.initialize();

      // Initialize camera
      _cameraController = await EnhancedCameraService.initializeCamera();

      if (_cameraController != null) {
        setState(() {
          _isInitializing = false;
          _hasError = false;
        });
      } else {
        setState(() {
          _isInitializing = false;
          _hasError = true;
          _errorMessage = 'Failed to initialize camera. Please check permissions.';
        });
      }

    } catch (e) {
      setState(() {
        _isInitializing = false;
        _hasError = true;
        _errorMessage = 'Error initializing scanner: $e';
      });
    }
  }

  /// Capture and identify plant
  Future<void> _captureAndIdentify() async {
    if (_isScanning) return;

    // Check scan limits
    final appState = Provider.of<AppState>(context, listen: false);
    if (!appState.canScanThisMonth) {
      _showScanLimitDialog();
      return;
    }

    setState(() {
      _isScanning = true;
    });

    try {
      // Use a scan
      appState.useScan();

      // Take picture
      final imageFile = await EnhancedCameraService.takePicture();

      if (imageFile != null) {
        // Identify plant
        final result = await _identificationService.identifyPlant(imageFile);

        // Navigate to results screen
        if (mounted) {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => PlantIdentificationResultScreen(
                result: result,
                imageFile: imageFile,
              ),
            ),
          );
        }
      } else {
        _showErrorSnackBar('Failed to capture image. Please try again.');
      }

    } catch (e) {
      _showErrorSnackBar('Error during scanning: $e');
    } finally {
      if (mounted) {
        setState(() {
          _isScanning = false;
        });
      }
    }
  }

  /// Pick image from gallery and identify
  Future<void> _pickFromGallery() async {
    if (_isScanning) return;

    // Check scan limits
    final appState = Provider.of<AppState>(context, listen: false);
    if (!appState.canScanThisMonth) {
      _showScanLimitDialog();
      return;
    }

    setState(() {
      _isScanning = true;
    });

    try {
      // Use a scan
      appState.useScan();

      final imageFile = await EnhancedCameraService.pickImageFromGallery();

      if (imageFile != null) {
        final result = await _identificationService.identifyPlant(imageFile);

        if (mounted) {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => PlantIdentificationResultScreen(
                result: result,
                imageFile: imageFile,
              ),
            ),
          );
        }
      }

    } catch (e) {
      _showErrorSnackBar('Error picking image: $e');
    } finally {
      if (mounted) {
        setState(() {
          _isScanning = false;
        });
      }
    }
  }

  /// Switch camera
  Future<void> _switchCamera() async {
    try {
      _cameraController = await EnhancedCameraService.switchCamera();
      setState(() {});
    } catch (e) {
      _showErrorSnackBar('Error switching camera: $e');
    }
  }

  /// Show error snackbar
  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        action: SnackBarAction(
          label: 'Retry',
          textColor: Colors.white,
          onPressed: _initializeServices,
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        title: Consumer<AppState>(
          builder: (context, appState, child) {
            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text('Plant Scanner'),
                if (!appState.isPremium)
                  Text(
                    '${appState.scansRemainingThisMonth} scans remaining this month',
                    style: const TextStyle(
                      fontSize: 12,
                      color: Colors.white70,
                    ),
                  ),
              ],
            );
          },
        ),
        backgroundColor: Colors.transparent,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.flip_camera_ios),
            onPressed: _switchCamera,
          ),
          IconButton(
            icon: const Icon(Icons.info_outline),
            onPressed: _showScannerInfo,
          ),
        ],
      ),
      body: Stack(
        children: [
          // Camera preview or error state
          _buildCameraPreview(),
          
          // Overlay UI
          _buildOverlayUI(),
          
          // Loading indicator
          if (_isInitializing || _isScanning) _buildLoadingOverlay(),
        ],
      ),
    );
  }

  /// Build camera preview
  Widget _buildCameraPreview() {
    if (_hasError) {
      return _buildErrorState();
    }

    if (_cameraController == null || !_cameraController!.value.isInitialized) {
      return const Center(
        child: CircularProgressIndicator(color: Colors.white),
      );
    }

    return CameraPreview(_cameraController!);
  }

  /// Build error state
  Widget _buildErrorState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.error_outline,
            color: Colors.white,
            size: 64,
          ),
          const SizedBox(height: 16),
          Text(
            'Camera Error',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              color: Colors.white,
            ),
          ),
          const SizedBox(height: 8),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 32),
            child: Text(
              _errorMessage,
              textAlign: TextAlign.center,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.white70,
              ),
            ),
          ),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: _initializeServices,
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  /// Build overlay UI
  Widget _buildOverlayUI() {
    return Column(
      children: [
        // Top info bar
        Container(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: _getCameraStateColor(),
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      _getCameraStateIcon(),
                      color: Colors.white,
                      size: 16,
                    ),
                    const SizedBox(width: 6),
                    Text(
                      _getCameraStateText(),
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
              const Spacer(),
              // Scan status indicator
              Consumer<AppState>(
                builder: (context, appState, child) {
                  if (appState.isPremium) {
                    return Container(
                      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                      decoration: BoxDecoration(
                        color: Colors.green,
                        borderRadius: BorderRadius.circular(16),
                      ),
                      child: const Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(Icons.all_inclusive, color: Colors.white, size: 16),
                          SizedBox(width: 6),
                          Text(
                            'Unlimited',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 12,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                    );
                  } else {
                    return Container(
                      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                      decoration: BoxDecoration(
                        color: appState.canScanThisMonth ? Colors.blue : Colors.red,
                        borderRadius: BorderRadius.circular(16),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            appState.canScanThisMonth ? Icons.camera_alt : Icons.lock,
                            color: Colors.white,
                            size: 16,
                          ),
                          const SizedBox(width: 6),
                          Text(
                            '${appState.scansRemainingThisMonth} left',
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 12,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                    );
                  }
                },
              ),
            ],
          ),
        ),
        
        const Spacer(),
        
        // Scanning frame
        _buildScanningFrame(),
        
        const Spacer(),
        
        // Bottom controls
        _buildBottomControls(),
      ],
    );
  }

  /// Build scanning frame
  Widget _buildScanningFrame() {
    return Container(
      width: 280,
      height: 280,
      decoration: BoxDecoration(
        border: Border.all(
          color: _isScanning ? Colors.green : Colors.white,
          width: 3,
        ),
        borderRadius: BorderRadius.circular(20),
      ),
      child: Stack(
        children: [
          // Corner indicators
          ...List.generate(4, (index) => _buildCornerIndicator(index)),
          
          // Center instruction
          Center(
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              decoration: BoxDecoration(
                color: Colors.black54,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                _isScanning ? 'Analyzing...' : 'Position plant in frame',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Build corner indicator
  Widget _buildCornerIndicator(int index) {
    final positions = [
      const Alignment(-1, -1), // Top-left
      const Alignment(1, -1),  // Top-right
      const Alignment(-1, 1),  // Bottom-left
      const Alignment(1, 1),   // Bottom-right
    ];

    return Align(
      alignment: positions[index],
      child: Container(
        width: 20,
        height: 20,
        margin: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: _isScanning ? Colors.green : Colors.white,
          borderRadius: BorderRadius.circular(4),
        ),
      ),
    );
  }

  /// Build bottom controls
  Widget _buildBottomControls() {
    return Container(
      padding: const EdgeInsets.all(24),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          // Gallery button
          _buildControlButton(
            icon: FontAwesomeIcons.images,
            label: 'Gallery',
            onPressed: _pickFromGallery,
          ),
          
          // Capture button
          GestureDetector(
            onTap: _captureAndIdentify,
            child: Container(
              width: 80,
              height: 80,
              decoration: BoxDecoration(
                color: _isScanning ? Colors.green : Colors.white,
                shape: BoxShape.circle,
                border: Border.all(
                  color: Colors.white,
                  width: 4,
                ),
              ),
              child: Icon(
                _isScanning ? Icons.hourglass_empty : FontAwesomeIcons.camera,
                color: _isScanning ? Colors.white : Colors.black,
                size: 32,
              ),
            ),
          ),
          
          // Flash button (placeholder)
          _buildControlButton(
            icon: FontAwesomeIcons.bolt,
            label: 'Flash',
            onPressed: () {
              // Flash toggle would go here
            },
          ),
        ],
      ),
    );
  }

  /// Build control button
  Widget _buildControlButton({
    required IconData icon,
    required String label,
    required VoidCallback onPressed,
  }) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: 56,
          height: 56,
          decoration: BoxDecoration(
            color: Colors.black54,
            shape: BoxShape.circle,
            border: Border.all(color: Colors.white, width: 2),
          ),
          child: IconButton(
            icon: FaIcon(icon, color: Colors.white, size: 20),
            onPressed: onPressed,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          label,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 12,
          ),
        ),
      ],
    );
  }

  /// Build loading overlay
  Widget _buildLoadingOverlay() {
    return Container(
      color: Colors.black54,
      child: Center(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const CircularProgressIndicator(color: Colors.white),
            const SizedBox(height: 16),
            Text(
              _isInitializing ? 'Initializing scanner...' : 'Identifying plant...',
              style: const TextStyle(
                color: Colors.white,
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Get camera state color
  Color _getCameraStateColor() {
    switch (_cameraState) {
      case CameraState.ready:
        return Colors.green;
      case CameraState.error:
      case CameraState.permissionDenied:
        return Colors.red;
      case CameraState.initializing:
      case CameraState.takingPicture:
        return Colors.orange;
      default:
        return Colors.grey;
    }
  }

  /// Get camera state icon
  IconData _getCameraStateIcon() {
    switch (_cameraState) {
      case CameraState.ready:
        return Icons.check_circle;
      case CameraState.error:
      case CameraState.permissionDenied:
        return Icons.error;
      case CameraState.initializing:
      case CameraState.takingPicture:
        return Icons.hourglass_empty;
      default:
        return Icons.camera;
    }
  }

  /// Get camera state text
  String _getCameraStateText() {
    switch (_cameraState) {
      case CameraState.ready:
        return 'Ready';
      case CameraState.error:
        return 'Error';
      case CameraState.permissionDenied:
        return 'No Permission';
      case CameraState.initializing:
        return 'Initializing';
      case CameraState.takingPicture:
        return 'Capturing';
      default:
        return 'Unknown';
    }
  }

  /// Show scanner info
  void _showScannerInfo() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Plant Scanner Tips'),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('📸 For best results:'),
            SizedBox(height: 8),
            Text('• Hold camera steady'),
            Text('• Ensure good lighting'),
            Text('• Focus on leaves or flowers'),
            Text('• Fill the frame with the plant'),
            Text('• Avoid shadows and reflections'),
            SizedBox(height: 16),
            Text('🌿 This scanner specializes in healing plants and herbs!'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Got it'),
          ),
        ],
      ),
    );
  }

  /// Show scan limit dialog
  void _showScanLimitDialog() {
    final appState = Provider.of<AppState>(context, listen: false);

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.lock, color: Colors.orange),
            SizedBox(width: 8),
            Text('Monthly Scan Limit Reached'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('You\'ve used all ${appState.freePlanScanLimit} free scans for this month.'),
            const SizedBox(height: 16),
            const Text('Upgrade to Premium for:'),
            const SizedBox(height: 8),
            const Text('✅ Unlimited plant scans'),
            const Text('✅ Advanced plant identification'),
            const Text('✅ Disease detection & treatment'),
            const Text('✅ Unlimited AI assistant'),
            const Text('✅ Offline plant database'),
            const SizedBox(height: 16),
            const Text('Your scans will reset next month.'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Maybe Later'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const SubscriptionScreen(),
                ),
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF22c55e),
              foregroundColor: Colors.white,
            ),
            child: const Text('Upgrade Now'),
          ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    EnhancedCameraService.dispose();
    super.dispose();
  }
}
