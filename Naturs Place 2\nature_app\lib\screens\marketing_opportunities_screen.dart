import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import '../services/affiliate_marketplace_service.dart';
import '../models/user_models.dart';
import 'tools/price_comparison_tool_screen.dart';
import 'tools/ingredient_verification_tool_screen.dart';
import 'tools/content_optimization_tool_screen.dart';
import 'tools/marketing_automation_tool_screen.dart';

enum AccessLevel { basic, premium, enterprise }

class MarketingOpportunitiesScreen extends StatefulWidget {
  final UserRole userRole;
  final AccessLevel accessLevel;

  const MarketingOpportunitiesScreen({
    super.key,
    this.userRole = UserRole.vendor,
    this.accessLevel = AccessLevel.basic,
  });

  @override
  State<MarketingOpportunitiesScreen> createState() => _MarketingOpportunitiesScreenState();
}

class _MarketingOpportunitiesScreenState extends State<MarketingOpportunitiesScreen> with SingleTickerProviderStateMixin {
  late TabController _tabController;

  String _selectedCategory = 'All';
  String _sortBy = 'Popular';
  bool _showActiveOnly = true;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 5, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_getAppBarTitle()),
        bottom: TabBar(
          controller: _tabController,
          isScrollable: true,
          tabs: const [
            Tab(icon: FaIcon(FontAwesomeIcons.dollarSign, size: 20), text: 'Pricing'),
            Tab(icon: FaIcon(FontAwesomeIcons.bullhorn, size: 20), text: 'Campaigns'),
            Tab(icon: FaIcon(FontAwesomeIcons.chartLine, size: 20), text: 'Analytics'),
            Tab(icon: FaIcon(FontAwesomeIcons.screwdriverWrench, size: 20), text: 'Tools'),
            Tab(icon: FaIcon(FontAwesomeIcons.lightbulb, size: 20), text: 'Insights'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildPricingTab(),
          _buildCampaignsTab(),
          _buildAnalyticsTab(),
          _buildToolsTab(),
          _buildInsightsTab(),
        ],
      ),
    );
  }

  Widget _buildPricingTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Subscription Tiers
          Text(
            'Vendor Subscription Tiers',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          
          Row(
            children: [
              Expanded(child: _buildTierCard(VendorTier.basic)),
              const SizedBox(width: 16),
              Expanded(child: _buildTierCard(VendorTier.standard)),
            ],
          ),
          const SizedBox(height: 16),
          _buildTierCard(VendorTier.premium),
          
          const SizedBox(height: 32),
          
          // Platform Fees
          Text(
            'Platform Fee Structure',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  _buildFeeRow('Basic Tier', '15% of sales', Colors.orange),
                  const Divider(),
                  _buildFeeRow('Premium Tier', '10% of sales', Colors.blue),
                  const Divider(),
                  _buildFeeRow('Enterprise Tier', '5% of sales', Colors.green),
                ],
              ),
            ),
          ),
          
          const SizedBox(height: 16),
          
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.blue.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.blue),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    const Icon(Icons.info, color: Colors.blue),
                    const SizedBox(width: 8),
                    Text(
                      'Fee Calculator',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        color: Colors.blue[700],
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                const Text('Example: \$1,000 in monthly sales'),
                const SizedBox(height: 8),
                Text('• Basic: \$150 platform fee + \$0 monthly = \$150 total'),
                Text('• Premium: \$100 platform fee + \$299.99 monthly = \$399.99 total'),
                Text('• Enterprise: \$50 platform fee + \$999.99 monthly = \$1,049.99 total'),
                const SizedBox(height: 8),
                Text(
                  'Premium becomes cost-effective at \$2,000+ monthly sales',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: Colors.blue[700],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCampaignsTab() {
    final marketingOpportunities = [
      MarketingOpportunity(
        id: 'featured',
        title: 'Featured Product Placement',
        description: 'Get your products featured on category pages and search results',
        cost: 199.99,
        duration: 30,
        benefits: ['Increased visibility', '3x more clicks', 'Priority placement'],
        category: 'Placement',
        isActive: true,
      ),
      MarketingOpportunity(
        id: 'category',
        title: 'Category Sponsorship',
        description: 'Sponsor an entire product category for maximum exposure',
        cost: 499.99,
        duration: 30,
        benefits: ['Category page banner', 'Brand logo display', 'Exclusive positioning'],
        category: 'Sponsorship',
        isActive: true,
      ),
      MarketingOpportunity(
        id: 'homepage',
        title: 'Homepage Banner',
        description: 'Premium banner placement on the homepage',
        cost: 999.99,
        duration: 7,
        benefits: ['Maximum visibility', 'Brand awareness', 'Direct traffic'],
        category: 'Premium',
        isActive: true,
      ),
      MarketingOpportunity(
        id: 'email',
        title: 'Email Marketing Campaign',
        description: 'Feature your products in our newsletter to 50K+ subscribers',
        cost: 299.99,
        duration: 1,
        benefits: ['Newsletter feature', 'Targeted audience', 'High conversion'],
        category: 'Email',
        isActive: true,
      ),
      MarketingOpportunity(
        id: 'social',
        title: 'Social Media Boost',
        description: 'Promote your products across our social media channels',
        cost: 149.99,
        duration: 14,
        benefits: ['Social media posts', 'Story features', 'Hashtag campaigns'],
        category: 'Social',
        isActive: true,
      ),
      // Add more marketing opportunities
      MarketingOpportunity(
        id: 'influencer',
        title: 'Influencer Partnership',
        description: 'Partner with health and wellness influencers for authentic product promotion',
        cost: 799.99,
        duration: 30,
        benefits: ['Influencer collaboration', 'Authentic reviews', 'Extended reach', 'User-generated content'],
        category: 'Influencer',
        isActive: true,
      ),
      MarketingOpportunity(
        id: 'video',
        title: 'Video Marketing Campaign',
        description: 'Create professional product videos for enhanced engagement',
        cost: 599.99,
        duration: 21,
        benefits: ['Professional video production', 'Multiple formats', 'Social media ready', 'Higher engagement'],
        category: 'Video',
        isActive: true,
      ),
      MarketingOpportunity(
        id: 'seo',
        title: 'SEO Optimization Package',
        description: 'Boost your product visibility with advanced SEO optimization',
        cost: 399.99,
        duration: 60,
        benefits: ['Keyword optimization', 'Meta tag enhancement', 'Search ranking boost', 'Organic traffic increase'],
        category: 'SEO',
        isActive: true,
      ),
      MarketingOpportunity(
        id: 'retargeting',
        title: 'Retargeting Campaign',
        description: 'Re-engage visitors who viewed your products but didn\'t purchase',
        cost: 249.99,
        duration: 30,
        benefits: ['Pixel tracking', 'Custom audiences', 'Dynamic ads', 'Higher conversion rates'],
        category: 'Retargeting',
        isActive: true,
      ),
      MarketingOpportunity(
        id: 'seasonal',
        title: 'Seasonal Promotion',
        description: 'Special seasonal campaigns for holidays and health awareness months',
        cost: 349.99,
        duration: 14,
        benefits: ['Seasonal themes', 'Holiday targeting', 'Limited-time offers', 'Urgency marketing'],
        category: 'Seasonal',
        isActive: false,
      ),
    ];

    // Filter opportunities based on selection
    final filteredOpportunities = marketingOpportunities.where((opportunity) {
      if (!_showActiveOnly || opportunity.isActive) {
        if (_selectedCategory == 'All' || opportunity.category == _selectedCategory) {
          return true;
        }
      }
      return false;
    }).toList();

    // Sort opportunities
    filteredOpportunities.sort((a, b) {
      switch (_sortBy) {
        case 'Price (Low to High)':
          return a.cost.compareTo(b.cost);
        case 'Price (High to Low)':
          return b.cost.compareTo(a.cost);
        case 'Duration':
          return a.duration.compareTo(b.duration);
        case 'Popular':
        default:
          return a.title.compareTo(b.title);
      }
    });

    return Column(
      children: [
        // Filter and Sort Controls
        Container(
          padding: const EdgeInsets.all(16),
          color: Colors.grey[50],
          child: Column(
            children: [
              Row(
                children: [
                  Expanded(
                    child: DropdownButtonFormField<String>(
                      value: _selectedCategory,
                      decoration: const InputDecoration(
                        labelText: 'Category',
                        border: OutlineInputBorder(),
                        contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                      ),
                      items: ['All', 'Placement', 'Sponsorship', 'Premium', 'Email', 'Social', 'Influencer', 'Video', 'SEO', 'Retargeting', 'Seasonal']
                          .map((category) => DropdownMenuItem(
                                value: category,
                                child: Text(category),
                              ))
                          .toList(),
                      onChanged: (value) {
                        setState(() {
                          _selectedCategory = value!;
                        });
                      },
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: DropdownButtonFormField<String>(
                      value: _sortBy,
                      decoration: const InputDecoration(
                        labelText: 'Sort By',
                        border: OutlineInputBorder(),
                        contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                      ),
                      items: ['Popular', 'Price (Low to High)', 'Price (High to Low)', 'Duration']
                          .map((sort) => DropdownMenuItem(
                                value: sort,
                                child: Text(sort),
                              ))
                          .toList(),
                      onChanged: (value) {
                        setState(() {
                          _sortBy = value!;
                        });
                      },
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              Row(
                children: [
                  Checkbox(
                    value: _showActiveOnly,
                    onChanged: (value) {
                      setState(() {
                        _showActiveOnly = value!;
                      });
                    },
                  ),
                  const Text('Show active campaigns only'),
                  const Spacer(),
                  Text(
                    '${filteredOpportunities.length} campaigns available',
                    style: TextStyle(
                      color: Colors.grey[600],
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),

        // Campaign List
        Expanded(
          child: ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: filteredOpportunities.length,
            itemBuilder: (context, index) {
              final opportunity = filteredOpportunities[index];
              return Card(
          margin: const EdgeInsets.only(bottom: 16),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Expanded(
                      child: Text(
                        opportunity.title,
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                      decoration: BoxDecoration(
                        color: _getCategoryColor(opportunity.category),
                        borderRadius: BorderRadius.circular(16),
                      ),
                      child: Text(
                        opportunity.category,
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Text(
                  opportunity.description,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Colors.grey[600],
                  ),
                ),
                const SizedBox(height: 16),
                
                // Benefits
                Wrap(
                  spacing: 8,
                  runSpacing: 4,
                  children: opportunity.benefits.map((benefit) => Chip(
                    label: Text(
                      benefit,
                      style: const TextStyle(fontSize: 12),
                    ),
                    backgroundColor: Colors.green.withValues(alpha: 0.1),
                    side: BorderSide(color: Colors.green.withValues(alpha: 0.3)),
                  )).toList(),
                ),
                
                const SizedBox(height: 16),
                
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          '\$${opportunity.cost.toStringAsFixed(2)}',
                          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                            color: const Color(0xFF22c55e),
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        Text(
                          '${opportunity.duration} day${opportunity.duration > 1 ? 's' : ''}',
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                    ElevatedButton(
                      onPressed: () {
                        _showPurchaseDialog(context, opportunity);
                      },
                      child: const Text('Purchase'),
                    ),
                  ],
                ),
              ],
            ),
              ),
            );
          },
        ),
      ),
    ],
    );
  }

  Widget _buildAnalyticsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Marketing Analytics & Performance',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),

          // Time Period Selector
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.blue.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.blue.withValues(alpha: 0.3)),
            ),
            child: Row(
              children: [
                const Icon(Icons.date_range, color: Colors.blue),
                const SizedBox(width: 12),
                const Text(
                  'Analytics Period:',
                  style: TextStyle(fontWeight: FontWeight.w600),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Wrap(
                    spacing: 8,
                    children: ['7 Days', '30 Days', '90 Days', 'Year'].map((period) =>
                      ChoiceChip(
                        label: Text(period),
                        selected: period == '30 Days',
                        onSelected: (selected) {
                          // Handle period selection
                        },
                      ),
                    ).toList(),
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: 24),

          // Key Performance Metrics
          Row(
            children: [
              Expanded(
                child: _buildEnhancedMetricCard(
                  'Total Revenue',
                  '\$24,580',
                  '+18.5%',
                  'vs last month',
                  Icons.monetization_on,
                  Colors.green,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildEnhancedMetricCard(
                  'Campaign ROI',
                  '340%',
                  '+25%',
                  'vs last month',
                  Icons.trending_up,
                  Colors.blue,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: _buildEnhancedMetricCard(
                  'Conversion Rate',
                  '8.2%',
                  '+1.2%',
                  'vs last month',
                  Icons.shopping_cart,
                  Colors.orange,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildEnhancedMetricCard(
                  'Avg. Order Value',
                  '\$47.50',
                  '+\$3.20',
                  'vs last month',
                  Icons.attach_money,
                  Colors.purple,
                ),
              ),
            ],
          ),

          const SizedBox(height: 24),

          // Traffic Sources
          Text(
            'Traffic Sources',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),

          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  _buildTrafficSourceRow('Organic Search', '45.2%', '12,450 visits', Colors.green),
                  const Divider(),
                  _buildTrafficSourceRow('Social Media', '28.7%', '7,890 visits', Colors.blue),
                  const Divider(),
                  _buildTrafficSourceRow('Email Marketing', '15.3%', '4,210 visits', Colors.orange),
                  const Divider(),
                  _buildTrafficSourceRow('Direct Traffic', '10.8%', '2,970 visits', Colors.purple),
                ],
              ),
            ),
          ),

          const SizedBox(height: 24),

          // Campaign Performance Detailed
          Text(
            'Campaign Performance Details',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),

          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  _buildDetailedCampaignRow(
                    'Featured Placement',
                    '2,450 clicks',
                    '8.5% CTR',
                    '\$1,250 spent',
                    '340% ROI',
                    Colors.green,
                  ),
                  const Divider(),
                  _buildDetailedCampaignRow(
                    'Email Campaign',
                    '1,890 clicks',
                    '15.2% CTR',
                    '\$450 spent',
                    '280% ROI',
                    Colors.blue,
                  ),
                  const Divider(),
                  _buildDetailedCampaignRow(
                    'Social Media Boost',
                    '3,120 clicks',
                    '6.8% CTR',
                    '\$890 spent',
                    '195% ROI',
                    Colors.purple,
                  ),
                  const Divider(),
                  _buildDetailedCampaignRow(
                    'Influencer Partnership',
                    '1,650 clicks',
                    '12.3% CTR',
                    '\$2,100 spent',
                    '420% ROI',
                    Colors.pink,
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(height: 24),

          // Top Performing Products
          Text(
            'Top Performing Products',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),

          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  _buildProductPerformanceRow(
                    'Ashwagandha Vitamins & Minerals',
                    '1,245 sales',
                    '\$8,950 revenue',
                    '4.8★',
                    Colors.green,
                  ),
                  const Divider(),
                  _buildProductPerformanceRow(
                    'Organic Turmeric Capsules',
                    '890 sales',
                    '\$6,230 revenue',
                    '4.6★',
                    Colors.orange,
                  ),
                  const Divider(),
                  _buildProductPerformanceRow(
                    'Lavender Essential Oil Set',
                    '567 sales',
                    '\$4,120 revenue',
                    '4.9★',
                    Colors.purple,
                  ),
                  const Divider(),
                  _buildProductPerformanceRow(
                    'Vitamin D3 + K2 Complex',
                    '423 sales',
                    '\$3,890 revenue',
                    '4.7★',
                    Colors.blue,
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(height: 24),

          // Performance Insights
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [Colors.blue[50]!, Colors.green[50]!],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.blue.withValues(alpha: 0.3)),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    const Icon(Icons.lightbulb, color: Colors.blue),
                    const SizedBox(width: 8),
                    Text(
                      'Performance Insights',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: Colors.blue[700],
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                const Text('• Your email campaigns have the highest CTR (15.2%) - consider increasing email marketing budget'),
                const SizedBox(height: 4),
                const Text('• Ashwagandha products are trending - consider featuring them more prominently'),
                const SizedBox(height: 4),
                const Text('• Mobile traffic accounts for 68% of visits - ensure mobile optimization'),
                const SizedBox(height: 4),
                const Text('• Peak shopping hours are 7-9 PM - schedule campaigns accordingly'),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildToolsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Content Optimization & Marketing Tools',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          
          // Tool Cards
          _buildToolCard(
            'Price Comparison Tool',
            'Compare your prices with competitors and get optimization recommendations',
            Icons.compare_arrows,
            Colors.blue,
            ['Real-time price tracking', 'Competitor analysis', 'Pricing recommendations'],
            onTap: () => Navigator.of(context).push(
              MaterialPageRoute(
                builder: (context) => const PriceComparisonToolScreen(),
              ),
            ),
          ),
          const SizedBox(height: 16),
          
          _buildToolCard(
            'Ingredient Verification',
            'Verify product ingredients and certifications for transparency',
            Icons.verified,
            Colors.green,
            ['Organic certification check', 'Ingredient authenticity', 'Quality assurance'],
            onTap: () => Navigator.of(context).push(
              MaterialPageRoute(
                builder: (context) => const IngredientVerificationToolScreen(),
              ),
            ),
          ),
          const SizedBox(height: 16),

          _buildToolCard(
            'Content Optimization',
            'Optimize product descriptions and keywords for better visibility',
            Icons.edit,
            Colors.orange,
            ['SEO optimization', 'Keyword suggestions', 'Content scoring'],
            onTap: () => Navigator.of(context).push(
              MaterialPageRoute(
                builder: (context) => const ContentOptimizationToolScreen(),
              ),
            ),
          ),
          const SizedBox(height: 16),

          _buildToolCard(
            'Marketing Automation',
            'Automate your marketing campaigns and track performance',
            Icons.auto_awesome,
            Colors.purple,
            ['Campaign automation', 'Performance tracking', 'A/B testing'],
            onTap: () => Navigator.of(context).push(
              MaterialPageRoute(
                builder: (context) => const MarketingAutomationToolScreen(),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTierCard(VendorTier tier) {
    final tierInfo = _getTierInfo(tier);
    final isRecommended = tier == VendorTier.standard;
    
    return Card(
      elevation: isRecommended ? 8 : 2,
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          border: isRecommended ? Border.all(color: Colors.blue, width: 2) : null,
        ),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (isRecommended)
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.blue,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Text(
                    'RECOMMENDED',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 10,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              if (isRecommended) const SizedBox(height: 8),
              
              Text(
                tierInfo['name'],
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              
              Text(
                tierInfo['price'],
                style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                  color: const Color(0xFF22c55e),
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              
              Text(
                'Platform Fee: ${tierInfo['platformFee']}',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Colors.grey[600],
                ),
              ),
              const SizedBox(height: 16),
              
              ...tierInfo['benefits'].map<Widget>((benefit) => Padding(
                padding: const EdgeInsets.only(bottom: 4),
                child: Row(
                  children: [
                    const Icon(Icons.check, color: Color(0xFF22c55e), size: 16),
                    const SizedBox(width: 8),
                    Expanded(child: Text(benefit, style: const TextStyle(fontSize: 14))),
                  ],
                ),
              )).toList(),
            ],
          ),
        ),
      ),
    );
  }

  Map<String, dynamic> _getTierInfo(VendorTier tier) {
    switch (tier) {
      case VendorTier.basic:
        return {
          'name': 'Basic',
          'price': '\$29.99/month',
          'platformFee': '12%',
          'benefits': [
            'Up to 50 products',
            'Basic analytics',
            'Email support',
            'Standard product listings',
            'Basic marketing tools',
          ],
        };
      case VendorTier.standard:
        return {
          'name': 'Standard',
          'price': '\$59.99/month',
          'platformFee': '10%',
          'benefits': [
            'Up to 200 products',
            'Advanced analytics',
            'Priority support',
            'Featured product placement',
            'Marketing automation',
            'SEO optimization',
          ],
        };
      case VendorTier.premium:
        return {
          'name': 'Premium',
          'price': '\$99.99/month',
          'platformFee': '8%',
          'benefits': [
            'Up to 500 products',
            'Custom analytics dashboard',
            'Dedicated account manager',
            'Premium marketing tools',
            'Content optimization',
            'Priority customer support',
          ],
        };
      case VendorTier.enterprise:
        return {
          'name': 'Enterprise',
          'price': '\$199.99/month',
          'platformFee': '5%',
          'benefits': [
            'Up to 1,000 products',
            'White-label solutions',
            'API access',
            'Custom integrations',
            'Advanced reporting',
            'Multi-store management',
          ],
        };
    }
  }

  Widget _buildFeeRow(String tier, String fee, Color color) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            tier,
            style: Theme.of(context).textTheme.titleMedium,
          ),
          Text(
            fee,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: color,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }



  Widget _buildToolCard(String title, String description, IconData icon, Color color, List<String> features, {VoidCallback? onTap}) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: color.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(icon, color: color, size: 24),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        description,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Wrap(
              spacing: 8,
              runSpacing: 4,
              children: features.map((feature) => Chip(
                label: Text(
                  feature,
                  style: const TextStyle(fontSize: 12),
                ),
                backgroundColor: color.withValues(alpha: 0.1),
                side: BorderSide(color: color.withValues(alpha: 0.3)),
              )).toList(),
            ),
            const SizedBox(height: 16),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: onTap,
                child: const Text('Access Tool'),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Color _getCategoryColor(String category) {
    switch (category) {
      case 'Placement':
        return Colors.blue;
      case 'Sponsorship':
        return Colors.green;
      case 'Premium':
        return Colors.purple;
      case 'Email':
        return Colors.orange;
      case 'Social':
        return Colors.pink;
      default:
        return Colors.grey;
    }
  }

  void _showPurchaseDialog(BuildContext context, MarketingOpportunity opportunity) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Purchase ${opportunity.title}'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(opportunity.description),
            const SizedBox(height: 16),
            Text('Duration: ${opportunity.duration} day${opportunity.duration > 1 ? 's' : ''}'),
            Text('Cost: \$${opportunity.cost.toStringAsFixed(2)}'),
            const SizedBox(height: 16),
            const Text('Benefits:', style: TextStyle(fontWeight: FontWeight.bold)),
            ...opportunity.benefits.map((benefit) => Text('• $benefit')),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('${opportunity.title} campaign purchased successfully!'),
                  backgroundColor: Colors.green,
                ),
              );
            },
            child: const Text('Purchase'),
          ),
        ],
      ),
    );
  }

  Widget _buildInsightsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Marketing Insights & Recommendations',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),

          // Market Trends
          _buildInsightCard(
            'Market Trends',
            'Natural health products are experiencing 15% growth this quarter',
            Icons.trending_up,
            Colors.green,
            [
              'Ashwagandha demand up 25%',
              'Organic vitamins & minerals trending',
              'Immune support products popular',
              'Sustainable packaging preferred',
            ],
            'High Priority',
          ),

          const SizedBox(height: 16),

          // Competitor Analysis
          _buildInsightCard(
            'Competitor Analysis',
            'Your pricing is competitive, but product descriptions need optimization',
            Icons.analytics,
            Colors.blue,
            [
              'Average price 8% below market',
              'Product photos quality: Good',
              'SEO score: 72/100 (Needs improvement)',
              'Customer reviews: 4.2/5 stars',
            ],
            'Medium Priority',
          ),

          const SizedBox(height: 16),

          // Customer Behavior
          _buildInsightCard(
            'Customer Behavior',
            'Peak shopping times and customer preferences analysis',
            Icons.people,
            Colors.purple,
            [
              'Peak hours: 7-9 PM weekdays',
              'Mobile traffic: 68%',
              'Average session: 3.2 minutes',
              'Cart abandonment: 24% (Below average)',
            ],
            'Low Priority',
          ),

          const SizedBox(height: 16),

          // Seasonal Opportunities
          _buildInsightCard(
            'Seasonal Opportunities',
            'Upcoming health awareness events and seasonal trends',
            Icons.calendar_month,
            Colors.orange,
            [
              'Mental Health Awareness Month (May)',
              'Summer wellness prep (March-April)',
              'Back-to-school immunity (August)',
              'Holiday stress relief (November-December)',
            ],
            'High Priority',
          ),

          const SizedBox(height: 24),

          // Action Items
          Text(
            'Recommended Actions',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),

          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  _buildActionItem(
                    'Optimize Product SEO',
                    'Improve product descriptions and keywords',
                    'High Impact',
                    Colors.red,
                    Icons.search,
                  ),
                  const Divider(),
                  _buildActionItem(
                    'Launch Ashwagandha Campaign',
                    'Capitalize on trending ingredient',
                    'High Impact',
                    Colors.red,
                    Icons.trending_up,
                  ),
                  const Divider(),
                  _buildActionItem(
                    'Mobile Experience Enhancement',
                    'Optimize for mobile users (68% of traffic)',
                    'Medium Impact',
                    Colors.orange,
                    Icons.phone_android,
                  ),
                  const Divider(),
                  _buildActionItem(
                    'Customer Review Campaign',
                    'Encourage more customer reviews',
                    'Medium Impact',
                    Colors.orange,
                    Icons.star,
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(height: 24),

          // Performance Forecast
          Text(
            'Performance Forecast',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),

          Row(
            children: [
              Expanded(
                child: _buildForecastCard(
                  'Next Month',
                  '+12%',
                  'Sales Growth',
                  Colors.green,
                  Icons.arrow_upward,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildForecastCard(
                  'Q2 2024',
                  '+28%',
                  'Revenue Increase',
                  Colors.blue,
                  Icons.monetization_on,
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          Row(
            children: [
              Expanded(
                child: _buildForecastCard(
                  'Market Share',
                  '3.2%',
                  'Category Position',
                  Colors.purple,
                  Icons.pie_chart,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildForecastCard(
                  'Customer Growth',
                  '+45%',
                  'New Customers',
                  Colors.orange,
                  Icons.person_add,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildInsightCard(String title, String description, IconData icon, Color color, List<String> insights, String priority) {
    Color priorityColor;
    switch (priority) {
      case 'High Priority':
        priorityColor = Colors.red;
        break;
      case 'Medium Priority':
        priorityColor = Colors.orange;
        break;
      case 'Low Priority':
        priorityColor = Colors.green;
        break;
      default:
        priorityColor = Colors.grey;
    }

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: color.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(icon, color: color, size: 24),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            title,
                            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                            decoration: BoxDecoration(
                              color: priorityColor.withValues(alpha: 0.1),
                              borderRadius: BorderRadius.circular(12),
                              border: Border.all(color: priorityColor.withValues(alpha: 0.3)),
                            ),
                            child: Text(
                              priority,
                              style: TextStyle(
                                color: priorityColor,
                                fontSize: 10,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 4),
                      Text(
                        description,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ...insights.map((insight) => Padding(
              padding: const EdgeInsets.only(bottom: 4),
              child: Row(
                children: [
                  Icon(Icons.circle, size: 6, color: color),
                  const SizedBox(width: 8),
                  Expanded(child: Text(insight, style: const TextStyle(fontSize: 14))),
                ],
              ),
            )),
          ],
        ),
      ),
    );
  }

  Widget _buildActionItem(String title, String description, String impact, Color color, IconData icon) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          CircleAvatar(
            backgroundColor: color.withValues(alpha: 0.1),
            child: Icon(icon, color: color, size: 20),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(fontWeight: FontWeight.w600),
                ),
                Text(
                  description,
                  style: TextStyle(color: Colors.grey[600], fontSize: 12),
                ),
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              impact,
              style: TextStyle(
                color: color,
                fontSize: 10,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildForecastCard(String period, String value, String metric, Color color, IconData icon) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Icon(icon, color: color, size: 32),
            const SizedBox(height: 8),
            Text(
              value,
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            Text(
              metric,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                fontWeight: FontWeight.w600,
              ),
              textAlign: TextAlign.center,
            ),
            Text(
              period,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEnhancedMetricCard(String title, String value, String change, String period, IconData icon, Color color) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(icon, color: color, size: 24),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    title,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Text(
              value,
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            const SizedBox(height: 4),
            Row(
              children: [
                Text(
                  change,
                  style: TextStyle(
                    color: change.startsWith('+') ? Colors.green : Colors.red,
                    fontWeight: FontWeight.w600,
                    fontSize: 12,
                  ),
                ),
                const SizedBox(width: 4),
                Text(
                  period,
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTrafficSourceRow(String source, String percentage, String visits, Color color) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Container(
            width: 12,
            height: 12,
            decoration: BoxDecoration(
              color: color,
              shape: BoxShape.circle,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              source,
              style: Theme.of(context).textTheme.titleMedium,
            ),
          ),
          Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Text(
                percentage,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
              Text(
                visits,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildDetailedCampaignRow(String campaign, String clicks, String ctr, String spent, String roi, Color color) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                campaign,
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  roi,
                  style: TextStyle(
                    color: color,
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              _buildMetricChip(clicks, Icons.mouse, Colors.blue),
              _buildMetricChip(ctr, Icons.trending_up, Colors.green),
              _buildMetricChip(spent, Icons.attach_money, Colors.orange),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildProductPerformanceRow(String product, String sales, String revenue, String rating, Color color) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(Icons.local_pharmacy, color: color, size: 20),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  product,
                  style: const TextStyle(fontWeight: FontWeight.w600),
                ),
                const SizedBox(height: 2),
                Row(
                  children: [
                    Text(
                      sales,
                      style: TextStyle(color: Colors.grey[600], fontSize: 12),
                    ),
                    const SizedBox(width: 8),
                    Text(
                      '•',
                      style: TextStyle(color: Colors.grey[400]),
                    ),
                    const SizedBox(width: 8),
                    Text(
                      revenue,
                      style: TextStyle(color: Colors.grey[600], fontSize: 12),
                    ),
                  ],
                ),
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: Colors.amber.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Icon(Icons.star, color: Colors.amber, size: 14),
                const SizedBox(width: 4),
                Text(
                  rating,
                  style: const TextStyle(
                    color: Colors.amber,
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMetricChip(String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, color: color, size: 14),
          const SizedBox(width: 4),
          Text(
            value,
            style: TextStyle(
              color: color,
              fontSize: 11,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  // Role-based access control methods
  String _getAppBarTitle() {
    switch (widget.userRole) {
      case UserRole.vendor:
        return 'Vendor Marketing Hub';
      case UserRole.partner:
        return 'Partner Marketing Tools';
      case UserRole.customer:
        return 'Marketing Opportunities';
      case UserRole.admin:
        return 'Marketing Management';
    }
  }




}
