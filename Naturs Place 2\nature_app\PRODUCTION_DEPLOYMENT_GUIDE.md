# 🚀 Production Deployment Guide

## 📋 **Pre-Deployment Checklist**

### ✅ **System Requirements**
- [ ] PostgreSQL 12+ server running
- [ ] Stripe account with API keys
- [ ] SSL certificates for secure connections
- [ ] Environment variables configured
- [ ] Database backup strategy in place

### ✅ **Configuration Files Ready**
- [ ] `production.env` - Environment variables
- [ ] `database_config.json` - Database settings
- [ ] `stripe_config.json` - Payment processing
- [ ] `monitoring_config.json` - System monitoring

## 🗄️ **Step 1: PostgreSQL Production Setup**

### **Create Production Database:**
```sql
-- Connect to PostgreSQL as superuser
CREATE DATABASE "Natures Place Production"
    WITH
    OWNER = naturesplace_user
    ENCODING = 'UTF8'
    LC_COLLATE = 'en_US.UTF-8'
    LC_CTYPE = 'en_US.UTF-8'
    TABLESPACE = pg_default
    CONNECTION LIMIT = 100;

-- Create dedicated user
CREATE USER naturesplace_user WITH PASSWORD 'your_secure_password_here';
GRANT ALL PRIVILEGES ON DATABASE "Natures Place Production" TO naturesplace_user;

-- Grant schema permissions
\c "Natures Place Production"
GRANT ALL ON SCHEMA public TO naturesplace_user;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO naturesplace_user;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO naturesplace_user;
```

### **Production Database Configuration:**
```bash
# Set environment variables
export POSTGRES_HOST=your-production-db-host.com
export POSTGRES_PORT=5432
export POSTGRES_DB="Natures Place Production"
export POSTGRES_USER=naturesplace_user
export POSTGRES_PASSWORD=your_secure_password_here
export POSTGRES_SSL_MODE=require
export POSTGRES_MAX_CONNECTIONS=20
```

## 💳 **Step 2: Stripe Integration Setup**

### **Stripe Configuration:**
```bash
# Stripe API Keys (use your actual keys)
export STRIPE_PUBLISHABLE_KEY=pk_live_your_publishable_key_here
export STRIPE_SECRET_KEY=sk_live_your_secret_key_here
export STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret_here

# Payout settings
export STRIPE_PAYOUT_METHOD=instant  # or 'standard'
export STRIPE_PAYOUT_CURRENCY=usd
export STRIPE_MINIMUM_PAYOUT=5.00
```

### **Webhook Endpoints:**
```
POST /webhooks/stripe/payout-created
POST /webhooks/stripe/payout-paid
POST /webhooks/stripe/payout-failed
POST /webhooks/stripe/account-updated
```

## 🔧 **Step 3: Application Configuration**

### **Production Environment File:**
Create `production.env`:
```bash
# Environment
NODE_ENV=production
FLUTTER_ENV=production

# Database
POSTGRES_HOST=your-production-db-host.com
POSTGRES_PORT=5432
POSTGRES_DB="Natures Place Production"
POSTGRES_USER=naturesplace_user
POSTGRES_PASSWORD=your_secure_password_here
POSTGRES_SSL_MODE=require
POSTGRES_MAX_CONNECTIONS=20
POSTGRES_CONNECTION_TIMEOUT=30
POSTGRES_QUERY_TIMEOUT=60

# Stripe
STRIPE_PUBLISHABLE_KEY=pk_live_your_publishable_key_here
STRIPE_SECRET_KEY=sk_live_your_secret_key_here
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret_here
STRIPE_PAYOUT_METHOD=instant
STRIPE_MINIMUM_PAYOUT=5.00

# Commission Settings
VENDOR_COMMISSION_RATE=10.0
PARTNER_COMMISSION_RATE=5.0
PAYOUT_CYCLE_DAYS=30
MAX_DAILY_PURCHASES_PER_PARTNER=20
MAX_SINGLE_PURCHASE_AMOUNT=1000.00

# Monitoring
ENABLE_MONITORING=true
MONITORING_INTERVAL_HOURS=1
FRAUD_DETECTION_ENABLED=true
ADMIN_ALERT_EMAIL=<EMAIL>

# Security
JWT_SECRET=your_jwt_secret_here
API_RATE_LIMIT=1000
ENABLE_CORS=true
ALLOWED_ORIGINS=https://naturesplace.com,https://app.naturesplace.com

# Logging
LOG_LEVEL=info
LOG_FILE_PATH=/var/log/naturesplace/app.log
ENABLE_DATABASE_LOGGING=true
```

## 📊 **Step 4: Monitoring Setup**

### **System Health Monitoring:**
```bash
# Monitoring configuration
export MONITORING_ENABLED=true
export HEALTH_CHECK_INTERVAL=300  # 5 minutes
export ALERT_EMAIL=<EMAIL>
export ALERT_SLACK_WEBHOOK=https://hooks.slack.com/your-webhook
export SYSTEM_HEALTH_THRESHOLD=0.90  # 90%
export FRAUD_ALERT_THRESHOLD=5  # Max 5 fraud alerts per hour
```

### **Database Monitoring:**
```sql
-- Create monitoring views
CREATE VIEW commission_health AS
SELECT 
    DATE(created_at) as date,
    COUNT(*) as total_commissions,
    COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending,
    COUNT(CASE WHEN status = 'paid' THEN 1 END) as paid,
    COUNT(CASE WHEN status = 'cancelled' THEN 1 END) as cancelled,
    AVG(commission_amount) as avg_amount
FROM commissions 
WHERE created_at >= NOW() - INTERVAL '30 days'
GROUP BY DATE(created_at)
ORDER BY date DESC;

CREATE VIEW partner_performance AS
SELECT 
    partner_id,
    COUNT(*) as total_commissions,
    SUM(commission_amount) as total_earnings,
    AVG(commission_amount) as avg_commission,
    MAX(created_at) as last_commission
FROM commissions 
WHERE partner_type = 'affiliate' 
    AND created_at >= NOW() - INTERVAL '30 days'
GROUP BY partner_id
ORDER BY total_earnings DESC;
```

## 🔄 **Step 5: Automated Deployment**

### **Deployment Script:**
Create `deploy.sh`:
```bash
#!/bin/bash
set -e

echo "🚀 Deploying Nature's Place Commission System..."

# Load environment variables
source production.env

# Build application
echo "📦 Building application..."
flutter build web --release
flutter build apk --release

# Database migration
echo "🗄️ Running database migrations..."
dart run_migrations.dart

# Test database connection
echo "🔍 Testing database connection..."
dart test_postgresql_connection.dart

# Start services
echo "🔧 Starting services..."
systemctl start naturesplace-commission-service
systemctl start naturesplace-monitoring-service
systemctl start naturesplace-payout-scheduler

# Health check
echo "💚 Running health check..."
sleep 10
curl -f http://localhost:8080/health || exit 1

echo "✅ Deployment completed successfully!"
echo "📊 Monitor at: https://admin.naturesplace.com/dashboard"
```

## 📈 **Step 6: Performance Optimization**

### **Database Optimization:**
```sql
-- Create additional indexes for performance
CREATE INDEX CONCURRENTLY idx_commissions_created_at ON commissions(created_at);
CREATE INDEX CONCURRENTLY idx_commissions_partner_status ON commissions(partner_id, status);
CREATE INDEX CONCURRENTLY idx_transactions_affiliate_completed ON transactions(affiliate_id, status) WHERE status = 'completed';
CREATE INDEX CONCURRENTLY idx_payouts_partner_date ON payouts(partner_id, created_at);

-- Analyze tables for query optimization
ANALYZE commissions;
ANALYZE transactions;
ANALYZE payouts;
ANALYZE payout_schedules;
```

### **Connection Pooling:**
```dart
// Update PostgreSQL service for production
class ProductionPostgreSQLService extends PostgreSQLService {
  static const int _maxConnections = 20;
  static const int _minConnections = 5;
  
  @override
  Future<void> _establishConnection() async {
    final config = _configService.getPostgreSQLConfig();
    
    _connection = await Connection.open(
      Endpoint(
        host: config['host'],
        port: config['port'],
        database: config['database'],
        username: config['username'],
        password: config['password'],
      ),
      settings: ConnectionSettings(
        sslMode: SslMode.require, // Force SSL in production
        connectTimeout: Duration(seconds: 30),
        queryTimeout: Duration(seconds: 60),
        applicationName: 'NaturesPlace-Commission-System',
      ),
    );
  }
}
```

## 🔒 **Step 7: Security Configuration**

### **SSL/TLS Setup:**
```bash
# SSL certificate configuration
export SSL_CERT_PATH=/etc/ssl/certs/naturesplace.crt
export SSL_KEY_PATH=/etc/ssl/private/naturesplace.key
export SSL_CA_PATH=/etc/ssl/certs/ca-bundle.crt

# Database SSL
export POSTGRES_SSL_CERT=/etc/ssl/certs/postgresql.crt
export POSTGRES_SSL_KEY=/etc/ssl/private/postgresql.key
export POSTGRES_SSL_ROOT_CERT=/etc/ssl/certs/postgresql-ca.crt
```

### **Firewall Rules:**
```bash
# Allow only necessary ports
ufw allow 443/tcp  # HTTPS
ufw allow 80/tcp   # HTTP (redirect to HTTPS)
ufw allow 5432/tcp from your-app-server-ip  # PostgreSQL
ufw deny 5432/tcp  # Block PostgreSQL from other IPs
```

## 📱 **Step 8: Mobile App Configuration**

### **Production Build Configuration:**
```yaml
# pubspec.yaml - production configuration
name: natures_place
description: Nature's Place - Healing Plants & Herbs
version: 1.0.0+1

environment:
  sdk: ">=3.0.0 <4.0.0"
  flutter: ">=3.10.0"

dependencies:
  flutter:
    sdk: flutter
  postgres: ^3.5.6
  # ... other dependencies

flutter:
  uses-material-design: true
  assets:
    - assets/images/
    - assets/config/production.json
```

### **App Store Deployment:**
```bash
# iOS App Store
flutter build ios --release
# Follow iOS deployment guide

# Google Play Store
flutter build appbundle --release
# Follow Android deployment guide
```

## 🎯 **Step 9: Go-Live Checklist**

### **Final Verification:**
- [ ] Database connection working
- [ ] Stripe integration tested
- [ ] Commission calculations verified
- [ ] Payout system functional
- [ ] Fraud detection active
- [ ] Monitoring alerts working
- [ ] SSL certificates valid
- [ ] Backup system operational
- [ ] Performance benchmarks met
- [ ] Security audit completed

### **Launch Sequence:**
1. **Deploy to staging** - Test with real data
2. **Load testing** - Verify performance under load
3. **Security scan** - Final security verification
4. **Backup verification** - Ensure backups work
5. **Go live** - Switch DNS to production
6. **Monitor closely** - Watch for 24 hours
7. **Partner onboarding** - Begin partner recruitment

## 📞 **Support & Maintenance**

### **Monitoring Dashboards:**
- **System Health**: https://admin.naturesplace.com/health
- **Commission Analytics**: https://admin.naturesplace.com/commissions
- **Partner Performance**: https://admin.naturesplace.com/partners
- **Fraud Detection**: https://admin.naturesplace.com/fraud

### **Emergency Contacts:**
- **Database Issues**: DBA team
- **Payment Issues**: Stripe support
- **Security Issues**: Security team
- **System Outages**: DevOps team

Your Nature's Place commission system is now ready for production deployment! 🚀
