import '../providers/app_state.dart';
import '../data/world_plant_encyclopedia.dart';

/// Plant identification and visual enhancement features
class PlantIdentificationService {
  
  /// Identify plants based on visual characteristics
  static List<Plant> identifyByCharacteristics({
    String? leafShape,
    String? flowerColor,
    String? plantSize,
    String? habitat,
    String? season,
    String? region,
  }) {
    List<Plant> candidates = WorldPlantEncyclopedia.getAllPlants();
    
    // Filter by habitat
    if (habitat != null && habitat.isNotEmpty) {
      candidates = candidates.where((plant) =>
          plant.habitat.toLowerCase().contains(habitat.toLowerCase())).toList();
    }
    
    // Filter by region
    if (region != null && region.isNotEmpty) {
      candidates = candidates.where((plant) =>
          plant.origin.toLowerCase().contains(region.toLowerCase())).toList();
    }
    
    // Filter by season (harvest season)
    if (season != null && season.isNotEmpty) {
      candidates = candidates.where((plant) =>
          plant.harvestingSeason.toLowerCase().contains(season.toLowerCase())).toList();
    }
    
    // Additional filtering based on description keywords
    if (leafShape != null && leafShape.isNotEmpty) {
      candidates = candidates.where((plant) =>
          plant.description.toLowerCase().contains(leafShape.toLowerCase())).toList();
    }
    
    if (flowerColor != null && flowerColor.isNotEmpty) {
      candidates = candidates.where((plant) =>
          plant.description.toLowerCase().contains(flowerColor.toLowerCase()) ||
          plant.name.toLowerCase().contains(flowerColor.toLowerCase())).toList();
    }
    
    return candidates;
  }
  
  /// Get plant identification quiz questions
  static List<PlantQuizQuestion> getIdentificationQuiz() {
    return [
      PlantQuizQuestion(
        question: "Which plant is known as the 'King of Medicinal Mushrooms'?",
        options: ["Reishi", "Chaga", "Shiitake", "Cordyceps"],
        correctAnswer: "Chaga",
        plantId: "scan_001",
        explanation: "Chaga is called the 'King of Medicinal Mushrooms' and 'Diamond of the Forest'",
      ),
      PlantQuizQuestion(
        question: "What is the most expensive spice by weight?",
        options: ["Vanilla", "Cardamom", "Saffron", "Black Pepper"],
        correctAnswer: "Saffron",
        plantId: "me_001",
        explanation: "Saffron is known as 'Red Gold' and is the world's most expensive spice by weight",
      ),
      PlantQuizQuestion(
        question: "Which plant is called the 'Miracle Tree'?",
        options: ["Neem", "Moringa", "Baobab", "Frankincense"],
        correctAnswer: "Moringa",
        plantId: "car_002",
        explanation: "Moringa is called the 'Miracle Tree' and 'Tree of Life' for its complete nutrition",
      ),
      PlantQuizQuestion(
        question: "What plant contains the highest concentration of Vitamin C?",
        options: ["Orange", "Acerola", "Camu Camu", "Kiwi"],
        correctAnswer: "Camu Camu",
        plantId: "amz_002",
        explanation: "Camu Camu contains up to 60 times more vitamin C than oranges",
      ),
      PlantQuizQuestion(
        question: "Which adaptogen is known as 'Indian Winter Cherry'?",
        options: ["Ashwagandha", "Brahmi", "Turmeric", "Holy Basil"],
        correctAnswer: "Ashwagandha",
        plantId: "ayur_001",
        explanation: "Ashwagandha is known as 'Indian Winter Cherry' and is a powerful adaptogen",
      ),
    ];
  }
  
  /// Get plant learning modules
  static List<PlantLearningModule> getLearningModules() {
    return [
      PlantLearningModule(
        title: "Introduction to Adaptogens",
        description: "Learn about plants that help your body adapt to stress",
        plants: ["ayur_001", "ayur_003", "tcm_001", "afr_002", "sea_002"],
        lessons: [
          "What are adaptogens?",
          "How do adaptogens work?",
          "Top 5 adaptogenic plants",
          "Safe usage guidelines",
          "Combining adaptogens",
        ],
      ),
      PlantLearningModule(
        title: "Medicinal Mushrooms",
        description: "Discover the healing power of fungi",
        plants: ["tcm_002", "scan_001", "jap_002"],
        lessons: [
          "Types of medicinal mushrooms",
          "Beta-glucans and immunity",
          "Cultivation and harvesting",
          "Preparation methods",
          "Safety considerations",
        ],
      ),
      PlantLearningModule(
        title: "Endangered Healing Plants",
        description: "Learn about rare and threatened medicinal plants",
        plants: ["nam_001", "me_002", "sea_002"],
        lessons: [
          "Conservation challenges",
          "Sustainable harvesting",
          "Cultivation alternatives",
          "Supporting conservation",
          "Ethical sourcing",
        ],
      ),
      PlantLearningModule(
        title: "Traditional Medicine Systems",
        description: "Explore healing traditions from around the world",
        plants: ["ayur_001", "tcm_001", "eur_001", "amz_001"],
        lessons: [
          "Ayurvedic principles",
          "Traditional Chinese Medicine",
          "European herbalism",
          "Indigenous knowledge",
          "Modern integration",
        ],
      ),
    ];
  }
  
  /// Get plant safety information
  static PlantSafetyInfo getPlantSafety(String plantId) {
    final plant = WorldPlantEncyclopedia.getAllPlants()
        .firstWhere((p) => p.id == plantId);
    
    return PlantSafetyInfo(
      plantName: plant.name,
      safetyLevel: _calculateSafetyLevel(plant),
      precautions: plant.precautions,
      contraindications: plant.contraindications,
      interactions: plant.interactions,
      dosage: plant.dosage,
      pregnancySafe: !plant.contraindications.any((c) => 
          c.toLowerCase().contains('pregnancy')),
      childrenSafe: _isChildrenSafe(plant),
      warnings: _generateWarnings(plant),
    );
  }
  
  static SafetyLevel _calculateSafetyLevel(Plant plant) {
    int riskScore = 0;
    
    // Check contraindications
    riskScore += plant.contraindications.length * 2;
    
    // Check interactions
    riskScore += plant.interactions.length;
    
    // Check for specific high-risk terms
    final highRiskTerms = ['toxic', 'poison', 'dangerous', 'avoid'];
    for (final term in highRiskTerms) {
      if (plant.precautions.any((p) => p.toLowerCase().contains(term))) {
        riskScore += 5;
      }
    }
    
    if (riskScore <= 3) return SafetyLevel.high;
    if (riskScore <= 7) return SafetyLevel.medium;
    return SafetyLevel.low;
  }
  
  static bool _isChildrenSafe(Plant plant) {
    final unsafeTerms = ['children', 'pediatric', 'infant', 'baby'];
    return !plant.contraindications.any((c) =>
        unsafeTerms.any((term) => c.toLowerCase().contains(term)));
  }
  
  static List<String> _generateWarnings(Plant plant) {
    List<String> warnings = [];
    
    if (plant.isEndangered == true) {
      warnings.add("⚠️ This plant is endangered - consider sustainable alternatives");
    }
    
    if (plant.interactions.isNotEmpty) {
      warnings.add("💊 May interact with medications - consult healthcare provider");
    }
    
    if (plant.contraindications.any((c) => c.toLowerCase().contains('pregnancy'))) {
      warnings.add("🤰 Not safe during pregnancy");
    }
    
    if (plant.precautions.any((p) => p.toLowerCase().contains('toxic'))) {
      warnings.add("☠️ Potentially toxic - use with extreme caution");
    }
    
    return warnings;
  }
}

/// Plant quiz question model
class PlantQuizQuestion {
  final String question;
  final List<String> options;
  final String correctAnswer;
  final String plantId;
  final String explanation;
  
  PlantQuizQuestion({
    required this.question,
    required this.options,
    required this.correctAnswer,
    required this.plantId,
    required this.explanation,
  });
}

/// Plant learning module model
class PlantLearningModule {
  final String title;
  final String description;
  final List<String> plants;
  final List<String> lessons;
  
  PlantLearningModule({
    required this.title,
    required this.description,
    required this.plants,
    required this.lessons,
  });
}

/// Plant safety information model
class PlantSafetyInfo {
  final String plantName;
  final SafetyLevel safetyLevel;
  final List<String> precautions;
  final List<String> contraindications;
  final List<String> interactions;
  final String dosage;
  final bool pregnancySafe;
  final bool childrenSafe;
  final List<String> warnings;
  
  PlantSafetyInfo({
    required this.plantName,
    required this.safetyLevel,
    required this.precautions,
    required this.contraindications,
    required this.interactions,
    required this.dosage,
    required this.pregnancySafe,
    required this.childrenSafe,
    required this.warnings,
  });
}

/// Safety level enumeration
enum SafetyLevel {
  high,
  medium,
  low,
}
