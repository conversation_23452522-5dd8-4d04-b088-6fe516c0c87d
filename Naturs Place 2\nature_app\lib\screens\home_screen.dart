import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import '../providers/app_state.dart';
import '../services/auth_service.dart';
import '../models/user_models.dart';
import '../widgets/subscription_card.dart';
import '../widgets/gradient_container.dart';
import '../widgets/product_card.dart';
import '../widgets/daily_plant_notification.dart';
import '../data/product_dataset.dart';
import 'plant_scanner_screen.dart';
import 'auth/login_screen.dart';
import 'profile_screen.dart';
import 'settings_screen.dart';
import 'vendor_dashboard_screen.dart';
import 'partner_dashboard_screen.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> with SingleTickerProviderStateMixin {
  TabController? _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // Return loading indicator if TabController is not initialized yet
    if (_tabController == null) {
      return const Scaffold(
        body: Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    return SafeArea(
      child: Scaffold(
        appBar: AppBar(
          backgroundColor: Colors.white,
          foregroundColor: Colors.black,
          title: Row(
            children: [
              const FaIcon(FontAwesomeIcons.leaf, color: Color(0xFF22c55e)),
              const SizedBox(width: 8),
              Text(
                "Nature's Place",
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          actions: [
            IconButton(
              icon: const Icon(Icons.notifications_outlined),
              onPressed: () {},
            ),
            Consumer<AuthService>(
              builder: (context, authService, child) {
                if (authService.isLoggedIn) {
                  return PopupMenuButton<String>(
                    child: CircleAvatar(
                      radius: 16,
                      backgroundColor: const Color(0xFF22c55e),
                      child: Text(
                        authService.currentUser!.firstName.substring(0, 1).toUpperCase(),
                        style: const TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    itemBuilder: (context) => _buildPopupMenuItems(authService),
                    onSelected: (value) {
                      switch (value) {
                        case 'profile':
                          Navigator.of(context).push(
                            MaterialPageRoute(
                              builder: (context) => const ProfileScreen(),
                            ),
                          );
                          break;
                        case 'settings':
                          Navigator.of(context).push(
                            MaterialPageRoute(
                              builder: (context) => const SettingsScreen(),
                            ),
                          );
                          break;
                        case 'vendor_dashboard':
                          Navigator.of(context).push(
                            MaterialPageRoute(
                              builder: (context) => const VendorDashboardScreen(),
                            ),
                          );
                          break;
                        case 'partner_dashboard':
                          Navigator.of(context).push(
                            MaterialPageRoute(
                              builder: (context) => const PartnerDashboardScreen(),
                            ),
                          );
                          break;
                        case 'logout':
                          authService.signOut();
                          break;
                      }
                    },
                  );
                } else {
                  return IconButton(
                    icon: const Icon(Icons.person_outlined),
                    onPressed: () {
                      Navigator.of(context).push(
                        MaterialPageRoute(
                          builder: (context) => const LoginScreen(),
                        ),
                      );
                    },
                  );
                }
              },
            ),
            const SizedBox(width: 8),
          ],
          bottom: TabBar(
            controller: _tabController!,
            tabs: const [
              Tab(icon: Icon(Icons.home), text: 'Overview'),
              Tab(icon: Icon(Icons.camera_alt), text: 'Scanner'),
              Tab(icon: Icon(Icons.card_membership), text: 'Plans'),
            ],
          ),
        ),
        body: TabBarView(
          controller: _tabController!,
          children: [
            _buildOverviewTab(),
            _buildScannerTab(),
            _buildPlansTab(),
          ],
        ),
      ),
    );
  }

  Widget _buildOverviewTab() {
    return SingleChildScrollView(
      child: Column(
        children: [
          // Daily Plant Notification
          const DailyPlantNotification(),

          // Hero Section
          GradientContainer(
            child: Padding(
              padding: const EdgeInsets.all(24.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    'Discover Nature\'s\nHealing Power',
                    style: Theme.of(context).textTheme.headlineLarge?.copyWith(
                      color: Colors.white,
                      height: 1.2,
                    ),
                    overflow: TextOverflow.ellipsis,
                    maxLines: 3,
                    textAlign: TextAlign.start,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Explore our marketplace of natural remedies, learn from our plant encyclopedia, and get personalized advice from our AI assistant.',
                    style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                      color: Colors.white.withValues(alpha: 0.9),
                    ),
                    overflow: TextOverflow.ellipsis,
                    maxLines: 4,
                    textAlign: TextAlign.start,
                  ),
                  const SizedBox(height: 24),
                  Row(
                    children: [
                      Expanded(
                        child: ElevatedButton(
                          onPressed: () {
                            Provider.of<AppState>(context, listen: false).setCurrentIndex(1);
                          },
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.white,
                            foregroundColor: const Color(0xFF22c55e),
                          ),
                          child: const Text('Browse Marketplace'),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: OutlinedButton(
                          onPressed: () {
                            Provider.of<AppState>(context, listen: false).setCurrentIndex(3);
                          },
                          style: OutlinedButton.styleFrom(
                            foregroundColor: Colors.white,
                            side: const BorderSide(color: Colors.white),
                          ),
                          child: const Text('Try AI Assistant'),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),

          // Featured Products Section
          Container(
            margin: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  'Featured Products',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'Discover the most popular natural remedies',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Colors.grey[600],
                  ),
                ),
                const SizedBox(height: 16),
                SizedBox(
                  height: 280,
                  child: ListView.builder(
                    scrollDirection: Axis.horizontal,
                    itemCount: ProductDataset.getFeaturedProducts().length,
                    itemBuilder: (context, index) {
                      final product = ProductDataset.getFeaturedProducts()[index];
                      return Container(
                        width: 200,
                        margin: const EdgeInsets.only(right: 16),
                        child: ProductCard(
                          product: product,
                          onTap: () {
                            // Navigate to marketplace and show this product
                            Provider.of<AppState>(context, listen: false).setCurrentIndex(1);
                          },
                        ),
                      );
                    },
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildScannerTab() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: const Color(0xFF22c55e).withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const FaIcon(
                      FontAwesomeIcons.seedling,
                      color: Color(0xFF22c55e),
                      size: 24,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Plant Scanner',
                        style: Theme.of(context).textTheme.titleMedium,
                      ),
                      Text(
                        'Identify plants and get care tips',
                        style: Theme.of(context).textTheme.bodySmall,
                      ),
                    ],
                  ),
                ],
              ),
              const SizedBox(height: 16),
              Container(
                height: 300,
                width: double.infinity,
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const FaIcon(
                      FontAwesomeIcons.camera,
                      color: Colors.grey,
                      size: 64,
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'Point your camera at a plant',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Get instant identification and care tips',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 16),
              Consumer<AppState>(
                builder: (context, appState, child) {
                  return Column(
                    children: [
                      // Scan status indicator
                      if (!appState.isPremium) ...[
                        Container(
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: appState.canScanThisMonth ? Colors.blue[50] : Colors.red[50],
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(
                              color: appState.canScanThisMonth ? Colors.blue[200]! : Colors.red[200]!,
                            ),
                          ),
                          child: Row(
                            children: [
                              Icon(
                                appState.canScanThisMonth ? Icons.camera_alt : Icons.lock,
                                color: appState.canScanThisMonth ? Colors.blue : Colors.red,
                                size: 20,
                              ),
                              const SizedBox(width: 8),
                              Expanded(
                                child: Text(
                                  appState.canScanThisMonth
                                    ? '${appState.scansRemainingThisMonth} free scans remaining this month'
                                    : 'Monthly scan limit reached. Upgrade for unlimited scans.',
                                  style: TextStyle(
                                    color: appState.canScanThisMonth ? Colors.blue[700] : Colors.red[700],
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ),
                              if (!appState.canScanThisMonth)
                                TextButton(
                                  onPressed: () {
                                    _tabController?.animateTo(2); // Switch to plans tab
                                  },
                                  child: const Text('Upgrade'),
                                ),
                            ],
                          ),
                        ),
                        const SizedBox(height: 16),
                      ],

                      // Scan button
                      SizedBox(
                        width: double.infinity,
                        child: ElevatedButton.icon(
                          onPressed: appState.isPremium || appState.canScanThisMonth ? () {
                            Navigator.of(context).push(
                              MaterialPageRoute(
                                builder: (context) => const PlantScannerScreen(),
                              ),
                            );
                          } : null,
                          icon: const FaIcon(FontAwesomeIcons.camera, size: 16),
                          label: Flexible(
                            child: Text(
                              appState.isPremium
                                ? 'Scan Plant (Unlimited)'
                                : appState.canScanThisMonth
                                  ? 'Scan Plant (${appState.scansRemainingThisMonth} left)'
                                  : 'Upgrade to Scan',
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: appState.isPremium || appState.canScanThisMonth
                              ? const Color(0xFF22c55e)
                              : Colors.grey,
                          ),
                        ),
                      ),
                    ],
                  );
                },
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPlansTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Choose Your Plan',
            style: Theme.of(context).textTheme.headlineMedium,
          ),
          const SizedBox(height: 8),
          Text(
            'Unlock more features with our premium subscriptions',
            style: Theme.of(context).textTheme.bodyMedium,
          ),
          const SizedBox(height: 24),
          const SubscriptionCard(
            tier: SubscriptionTier.free,
            title: 'Free',
            price: '\$0',
            period: '/month',
            description: 'Perfect for casual plant enthusiasts',
            features: [
              '5 free plant scans per month',
              'Basic plant identification (70% accuracy)',
              'Limited access to plant encyclopedia',
              'Basic AI assistant (3 questions/month)',
              'Community access',
            ],
            unavailableFeatures: [
              'Unlimited scanning',
              'Disease detection',
              'Advanced features',
            ],
            isPopular: false,
          ),
          const SizedBox(height: 16),
          const SubscriptionCard(
            tier: SubscriptionTier.premium,
            title: 'Premium',
            price: '\$9.99',
            period: '/month',
            description: 'Ideal for plant lovers and health enthusiasts',
            features: [
              'Unlimited plant scans',
              'Advanced plant identification',
              'Full plant encyclopedia access',
              'Unlimited AI assistant',
              'Disease detection & treatment',
              'Personalized health recommendations',
              'Offline plant database',
            ],
            unavailableFeatures: [
              'Professional features',
            ],
            isPopular: true,
          ),
          const SizedBox(height: 16),
          const SubscriptionCard(
            tier: SubscriptionTier.pro,
            title: 'Pro',
            price: '\$19.99',
            period: '/month',
            description: 'For health practitioners and serious herbalists',
            features: [
              'Everything in Premium',
              'Professional plant database',
              'Clinical research access',
              'Dosage & preparation guides',
              'Drug interaction warnings',
              'Priority customer support',
            ],
            unavailableFeatures: [],
            isPopular: false,
          ),
        ],
      ),
    );
  }

  /// Build role-specific popup menu items
  List<PopupMenuEntry<String>> _buildPopupMenuItems(AuthService authService) {
    final currentUser = authService.currentUser;

    List<PopupMenuEntry<String>> items = [
      PopupMenuItem<String>(
        enabled: false,
        child: Text('Hello, ${currentUser!.firstName}'),
      ),
      const PopupMenuDivider(),
      const PopupMenuItem<String>(
        value: 'profile',
        child: Row(
          children: [
            Icon(Icons.person),
            SizedBox(width: 8),
            Text('Profile'),
          ],
        ),
      ),
      const PopupMenuItem<String>(
        value: 'settings',
        child: Row(
          children: [
            Icon(Icons.settings),
            SizedBox(width: 8),
            Text('Settings'),
          ],
        ),
      ),
    ];

    // Add role-specific dashboard options
    if (currentUser.role == UserRole.vendor) {
      items.add(
        const PopupMenuItem<String>(
          value: 'vendor_dashboard',
          child: Row(
            children: [
              Icon(Icons.dashboard),
              SizedBox(width: 8),
              Text('Vendor Dashboard'),
            ],
          ),
        ),
      );
    } else if (currentUser.role == UserRole.partner) {
      items.add(
        const PopupMenuItem<String>(
          value: 'partner_dashboard',
          child: Row(
            children: [
              Icon(Icons.dashboard),
              SizedBox(width: 8),
              Text('Partner Dashboard'),
            ],
          ),
        ),
      );
    }

    // Add logout option
    items.add(
      const PopupMenuItem<String>(
        value: 'logout',
        child: Row(
          children: [
            Icon(Icons.logout),
            SizedBox(width: 8),
            Text('Sign Out'),
          ],
        ),
      ),
    );

    return items;
  }
}
