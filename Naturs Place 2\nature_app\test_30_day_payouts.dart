import 'package:flutter/foundation.dart';
import 'lib/services/commission_service.dart';
import 'lib/services/payout_scheduler_service.dart';
import 'lib/models/commission_models.dart';
import 'lib/models/vendor_models.dart';

/// Test 30-day partner payout system
/// Verify: Partners get paid every 30 days automatically
void main() async {
  print('📅 Testing 30-Day Partner Payout System\n');

  try {
    // Initialize services
    print('🚀 Initializing services...');
    final commissionService = CommissionService();
    final payoutScheduler = PayoutSchedulerService();
    
    await commissionService.initialize();
    await payoutScheduler.initialize();
    print('✅ Services initialized!\n');

    // Test partner ID
    final partnerId = 'partner_wellness_guru';
    
    // Step 1: Create payout schedule for new partner
    print('📋 Step 1: Creating 30-day payout schedule...');
    await payoutScheduler.createPartnerPayoutSchedule(partnerId);
    
    final schedule = await payoutScheduler.getPartnerPayoutSchedule(partnerId);
    if (schedule != null) {
      print('✅ Payout schedule created:');
      print('   Partner ID: $partnerId');
      print('   Schedule Type: ${schedule['schedule_type']}');
      print('   Cycle Start: ${schedule['cycle_start_date']}');
      print('   Cycle End: ${schedule['cycle_end_date']}');
      print('   Next Payout: ${schedule['next_payout_date']}\n');
    }

    // Step 2: Simulate partner earning commissions over 30 days
    print('📈 Step 2: Simulating partner commissions over 30 days...');
    
    final transactions = [
      // Day 1: $100 sale → Partner earns $5
      _createTestTransaction('txn_day_1', partnerId, 100.0, DateTime.now().subtract(const Duration(days: 29))),
      // Day 10: $200 sale → Partner earns $10
      _createTestTransaction('txn_day_10', partnerId, 200.0, DateTime.now().subtract(const Duration(days: 20))),
      // Day 20: $150 sale → Partner earns $7.50
      _createTestTransaction('txn_day_20', partnerId, 150.0, DateTime.now().subtract(const Duration(days: 10))),
      // Day 30: $300 sale → Partner earns $15
      _createTestTransaction('txn_day_30', partnerId, 300.0, DateTime.now()),
    ];

    double totalExpectedEarnings = 0.0;
    for (final transaction in transactions) {
      await commissionService.processTransaction(transaction);
      final partnerCommission = transaction.total * 0.05; // 5% commission
      totalExpectedEarnings += partnerCommission;
      print('   ${transaction.id}: \$${transaction.total} sale → Partner earns \$${partnerCommission.toStringAsFixed(2)}');
    }
    
    print('   Total expected partner earnings: \$${totalExpectedEarnings.toStringAsFixed(2)}\n');

    // Step 3: Check pending commissions
    print('💰 Step 3: Checking pending commissions...');
    final pendingAmount = await payoutScheduler.calculatePendingCommissions(partnerId);
    print('   Pending commission amount: \$${pendingAmount.toStringAsFixed(2)}');
    
    final expectedMatch = (pendingAmount - totalExpectedEarnings).abs() < 0.01;
    print('   Expected vs Actual: ${expectedMatch ? "✅ MATCH" : "❌ MISMATCH"}\n');

    // Step 4: Simulate 30 days passing and process payout
    print('⏰ Step 4: Processing 30-day payout...');
    
    if (pendingAmount > 0) {
      final payoutId = await payoutScheduler.processPartnerPayout(partnerId);
      
      if (payoutId != null) {
        print('✅ Payout processed successfully!');
        print('   Payout ID: $payoutId');
        print('   Amount: \$${pendingAmount.toStringAsFixed(2)}');
        
        // Calculate fees (Stripe: 2.9% + $0.30)
        final fees = (pendingAmount * 0.029) + 0.30;
        final netAmount = pendingAmount - fees;
        print('   Fees: \$${fees.toStringAsFixed(2)}');
        print('   Net Amount: \$${netAmount.toStringAsFixed(2)}\n');
      } else {
        print('❌ Payout processing failed\n');
      }
    } else {
      print('⚠️ No pending commissions to pay out\n');
    }

    // Step 5: Verify payout history
    print('📊 Step 5: Checking payout history...');
    final payoutHistory = await payoutScheduler.getPartnerPayoutHistory(partnerId);
    
    if (payoutHistory.isNotEmpty) {
      print('✅ Payout history found:');
      for (final payout in payoutHistory) {
        print('   Payout: ${payout['id']}');
        print('   Amount: \$${payout['total_amount']}');
        print('   Net: \$${payout['net_amount']}');
        print('   Status: ${payout['status']}');
        print('   Date: ${payout['created_at']}\n');
      }
    } else {
      print('⚠️ No payout history found\n');
    }

    // Step 6: Check updated payout schedule
    print('🔄 Step 6: Checking updated payout schedule...');
    final updatedSchedule = await payoutScheduler.getPartnerPayoutSchedule(partnerId);
    
    if (updatedSchedule != null) {
      print('✅ Payout schedule updated for next cycle:');
      print('   Next Payout Date: ${updatedSchedule['next_payout_date']}');
      print('   Cycle Start: ${updatedSchedule['cycle_start_date']}');
      print('   Cycle End: ${updatedSchedule['cycle_end_date']}\n');
    }

    // Step 7: Test automated payout processing
    print('🤖 Step 7: Testing automated payout processing...');
    await payoutScheduler.processDuePayouts();
    print('✅ Automated payout processing completed\n');

    // Summary
    print('📋 30-Day Payout System Summary:');
    print('   ✅ Partner payout schedule created (30-day cycles)');
    print('   ✅ Commissions accumulated over 30 days');
    print('   ✅ Automatic payout processing on cycle completion');
    print('   ✅ Stripe integration with fee calculation');
    print('   ✅ Payout history tracking');
    print('   ✅ Next cycle scheduling');
    
    final stats = payoutScheduler.getPayoutStatistics();
    print('\n💡 Payout System Configuration:');
    print('   Frequency: ${stats['payout_frequency']}');
    print('   Payment Method: ${stats['payment_method']}');
    print('   Fee Structure: ${stats['fee_structure']}');
    print('   Processing: ${stats['processing_day']}');

    print('\n🎉 30-Day Partner Payout System is working correctly!');

  } catch (e, stackTrace) {
    print('❌ Test failed with error: $e');
    print('📋 Stack trace: $stackTrace');
    print('\n🔧 Troubleshooting:');
    print('   • Check PostgreSQL connection');
    print('   • Verify payout scheduler service');
    print('   • Ensure commission calculations are correct');
  }
}

/// Helper function to create test transaction
Transaction _createTestTransaction(String id, String affiliateId, double amount, DateTime date) {
  return Transaction(
    id: id,
    orderId: 'order_${id}',
    customerId: 'customer_referred_${id}',
    vendorId: 'vendor_healing_herbs',
    affiliateId: affiliateId,
    productId: 'product_wellness_${id}',
    productName: 'Wellness Product - ${amount.toStringAsFixed(0)}',
    category: ProductCategory.vitamins,
    productPrice: amount,
    quantity: 1,
    subtotal: amount,
    tax: 0.0,
    shipping: 0.0,
    total: amount,
    status: TransactionStatus.completed,
    createdAt: date,
    completedAt: date,
    metadata: {"referral_source": "partner_link", "payout_cycle": "30_days"},
  );
}
