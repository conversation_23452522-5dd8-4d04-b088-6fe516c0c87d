import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:postgres/postgres.dart';
import 'database_config_service.dart';

/// Database connection manager with connection pooling and retry logic
class DatabaseConnectionManager {
  static final DatabaseConnectionManager _instance = DatabaseConnectionManager._internal();
  factory DatabaseConnectionManager() => _instance;
  DatabaseConnectionManager._internal();

  final DatabaseConfigService _configService = DatabaseConfigService();
  final List<Connection> _connectionPool = [];
  final List<Connection> _availableConnections = [];
  final List<Connection> _busyConnections = [];
  
  Timer? _healthCheckTimer;
  bool _isInitialized = false;
  int _maxPoolSize = 5;
  int _currentRetryAttempts = 0;
  int _maxRetryAttempts = 3;

  /// Initialize the connection manager
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      await _configService.initialize();
      final performanceSettings = _configService.getPerformanceSettings();
      _maxPoolSize = performanceSettings['connection_pool_size'] as int;
      _maxRetryAttempts = _configService.getSyncSettings()['max_retry_attempts'] as int;

      await _createConnectionPool();
      _startHealthCheck();
      
      _isInitialized = true;
      debugPrint('✅ Database Connection Manager initialized with pool size: $_maxPoolSize');
    } catch (e) {
      debugPrint('❌ Failed to initialize Database Connection Manager: $e');
      rethrow;
    }
  }

  /// Create initial connection pool
  Future<void> _createConnectionPool() async {
    final config = _configService.getPostgreSQLConfig();
    
    for (int i = 0; i < _maxPoolSize; i++) {
      try {
        final connection = await _createConnection(config);
        _connectionPool.add(connection);
        _availableConnections.add(connection);
        debugPrint('✅ Created connection ${i + 1}/$_maxPoolSize');
      } catch (e) {
        debugPrint('⚠️ Failed to create connection ${i + 1}: $e');
        // Continue creating other connections
      }
    }

    if (_availableConnections.isEmpty) {
      throw Exception('Failed to create any database connections');
    }
  }

  /// Create a single database connection
  Future<Connection> _createConnection(Map<String, dynamic> config) async {
    return await Connection.open(
      Endpoint(
        host: config['host'],
        port: config['port'],
        database: config['database'],
        username: config['username'],
        password: config['password'],
      ),
      settings: ConnectionSettings(
        sslMode: _getSslMode(config['ssl_mode']),
        connectTimeout: Duration(seconds: config['connect_timeout']),
        queryTimeout: Duration(seconds: config['query_timeout']),
      ),
    );
  }

  /// Get SSL mode from string
  SslMode _getSslMode(String mode) {
    switch (mode.toLowerCase()) {
      case 'disable':
        return SslMode.disable;
      case 'require':
        return SslMode.require;
      case 'prefer':
      default:
        return SslMode.prefer;
    }
  }

  /// Get an available connection from the pool
  Future<Connection?> getConnection() async {
    if (!_isInitialized) {
      debugPrint('⚠️ Connection manager not initialized');
      return null;
    }

    // Try to get an available connection
    if (_availableConnections.isNotEmpty) {
      final connection = _availableConnections.removeAt(0);
      _busyConnections.add(connection);
      return connection;
    }

    // If no available connections, try to create a new one if under limit
    if (_connectionPool.length < _maxPoolSize) {
      try {
        final config = _configService.getPostgreSQLConfig();
        final connection = await _createConnection(config);
        _connectionPool.add(connection);
        _busyConnections.add(connection);
        debugPrint('✅ Created additional connection (${_connectionPool.length}/$_maxPoolSize)');
        return connection;
      } catch (e) {
        debugPrint('❌ Failed to create additional connection: $e');
      }
    }

    // Wait for a connection to become available
    debugPrint('⏳ Waiting for available connection...');
    return await _waitForAvailableConnection();
  }

  /// Wait for a connection to become available
  Future<Connection?> _waitForAvailableConnection() async {
    const maxWaitTime = Duration(seconds: 30);
    const checkInterval = Duration(milliseconds: 100);
    final startTime = DateTime.now();

    while (DateTime.now().difference(startTime) < maxWaitTime) {
      if (_availableConnections.isNotEmpty) {
        final connection = _availableConnections.removeAt(0);
        _busyConnections.add(connection);
        return connection;
      }
      await Future.delayed(checkInterval);
    }

    debugPrint('❌ Timeout waiting for available connection');
    return null;
  }

  /// Return a connection to the pool
  void returnConnection(Connection connection) {
    if (_busyConnections.remove(connection)) {
      _availableConnections.add(connection);
      debugPrint('🔄 Connection returned to pool (${_availableConnections.length} available)');
    }
  }

  /// Execute a query with automatic connection management
  Future<T?> executeWithConnection<T>(
    Future<T> Function(Connection connection) operation,
  ) async {
    Connection? connection;
    
    try {
      connection = await getConnection();
      if (connection == null) {
        debugPrint('❌ No connection available for operation');
        return null;
      }

      final result = await operation(connection);
      return result;
    } catch (e) {
      debugPrint('❌ Error executing operation: $e');
      
      // Check if it's a connection error and retry
      if (_isConnectionError(e) && _currentRetryAttempts < _maxRetryAttempts) {
        _currentRetryAttempts++;
        debugPrint('🔄 Retrying operation (attempt $_currentRetryAttempts/$_maxRetryAttempts)');
        
        // Remove the failed connection from pool
        if (connection != null) {
          await _removeFailedConnection(connection);
        }
        
        // Retry the operation
        return await executeWithConnection(operation);
      }
      
      rethrow;
    } finally {
      if (connection != null) {
        returnConnection(connection);
      }
      _currentRetryAttempts = 0; // Reset retry counter on success
    }
  }

  /// Check if error is a connection-related error
  bool _isConnectionError(dynamic error) {
    final errorString = error.toString().toLowerCase();
    return errorString.contains('connection') ||
           errorString.contains('timeout') ||
           errorString.contains('network') ||
           errorString.contains('socket');
  }

  /// Remove a failed connection from the pool
  Future<void> _removeFailedConnection(Connection connection) async {
    try {
      await connection.close();
    } catch (e) {
      debugPrint('⚠️ Error closing failed connection: $e');
    }

    _connectionPool.remove(connection);
    _busyConnections.remove(connection);
    _availableConnections.remove(connection);
    
    debugPrint('🗑️ Removed failed connection from pool');

    // Try to create a replacement connection
    try {
      final config = _configService.getPostgreSQLConfig();
      final newConnection = await _createConnection(config);
      _connectionPool.add(newConnection);
      _availableConnections.add(newConnection);
      debugPrint('✅ Created replacement connection');
    } catch (e) {
      debugPrint('❌ Failed to create replacement connection: $e');
    }
  }

  /// Start health check timer
  void _startHealthCheck() {
    _healthCheckTimer = Timer.periodic(const Duration(minutes: 5), (timer) {
      _performHealthCheck();
    });
  }

  /// Perform health check on all connections
  Future<void> _performHealthCheck() async {
    debugPrint('🔍 Performing connection health check...');
    
    final connectionsToCheck = List<Connection>.from(_availableConnections);
    
    for (final connection in connectionsToCheck) {
      try {
        // Simple query to test connection
        await connection.execute('SELECT 1');
      } catch (e) {
        debugPrint('❌ Connection failed health check: $e');
        await _removeFailedConnection(connection);
      }
    }
    
    debugPrint('✅ Health check completed (${_availableConnections.length} healthy connections)');
  }

  /// Get connection pool statistics
  Map<String, dynamic> getPoolStatistics() {
    return {
      'total_connections': _connectionPool.length,
      'available_connections': _availableConnections.length,
      'busy_connections': _busyConnections.length,
      'max_pool_size': _maxPoolSize,
      'pool_utilization': _connectionPool.isEmpty ? 0.0 : (_busyConnections.length / _connectionPool.length),
      'is_healthy': _availableConnections.isNotEmpty,
    };
  }

  /// Test connection to database
  Future<bool> testConnection() async {
    try {
      final result = await executeWithConnection<bool>((connection) async {
        await connection.execute('SELECT 1');
        return true;
      });
      return result ?? false;
    } catch (e) {
      debugPrint('❌ Connection test failed: $e');
      return false;
    }
  }

  /// Close all connections and cleanup
  Future<void> close() async {
    _healthCheckTimer?.cancel();
    
    for (final connection in _connectionPool) {
      try {
        await connection.close();
      } catch (e) {
        debugPrint('⚠️ Error closing connection: $e');
      }
    }
    
    _connectionPool.clear();
    _availableConnections.clear();
    _busyConnections.clear();
    _isInitialized = false;
    
    debugPrint('🔌 All database connections closed');
  }
}
