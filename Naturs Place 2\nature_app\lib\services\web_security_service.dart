import 'dart:async';
import 'dart:convert';
import 'dart:math';
import 'package:crypto/crypto.dart';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
// Conditional import for web-only functionality
import 'web_stub.dart' if (dart.library.html) 'dart:html' as html;

/// Enhanced web security service
class WebSecurityService {
  static final WebSecurityService _instance = WebSecurityService._internal();
  factory WebSecurityService() => _instance;
  WebSecurityService._internal();

  static bool _isInitialized = false;
  static Timer? _securityTimer;
  static final List<String> _suspiciousActivities = [];
  static final Map<String, int> _rateLimitTracker = {};

  /// Initialize web security
  static Future<void> initialize() async {
    if (!kIsWeb || _isInitialized) return;

    try {
      // Initialize security headers
      _setSecurityHeaders();
      
      // Setup CSRF protection
      await _setupCSRFProtection();
      
      // Initialize rate limiting
      _initializeRateLimiting();
      
      // Setup input validation
      _setupInputValidation();
      
      // Start security monitoring
      _startSecurityMonitoring();
      
      _isInitialized = true;
      debugPrint('🔒 Web security service initialized');
    } catch (e) {
      debugPrint('❌ Error initializing web security: $e');
    }
  }

  /// Set security headers
  static void _setSecurityHeaders() {
    if (!kIsWeb) return;

    try {
      // Add security meta tags if not present
      final head = html.document.head;
      if (head != null) {
        // Content Security Policy
        if (head.querySelector('meta[http-equiv="Content-Security-Policy"]') == null) {
          final cspMeta = html.MetaElement()
            ..httpEquiv = 'Content-Security-Policy'
            ..content = "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self' https:;";
          head.append(cspMeta);
        }

        // X-Frame-Options
        if (head.querySelector('meta[http-equiv="X-Frame-Options"]') == null) {
          final frameMeta = html.MetaElement()
            ..httpEquiv = 'X-Frame-Options'
            ..content = 'DENY';
          head.append(frameMeta);
        }

        // X-Content-Type-Options
        if (head.querySelector('meta[http-equiv="X-Content-Type-Options"]') == null) {
          final contentTypeMeta = html.MetaElement()
            ..httpEquiv = 'X-Content-Type-Options'
            ..content = 'nosniff';
          head.append(contentTypeMeta);
        }
      }

      debugPrint('🛡️ Security headers configured');
    } catch (e) {
      debugPrint('❌ Error setting security headers: $e');
    }
  }

  /// Setup CSRF protection
  static Future<void> _setupCSRFProtection() async {
    if (!kIsWeb) return;

    try {
      final prefs = await SharedPreferences.getInstance();
      
      // Generate CSRF token
      final csrfToken = _generateSecureToken();
      await prefs.setString('csrf_token', csrfToken);
      
      // Store token creation time
      await prefs.setString('csrf_token_created', DateTime.now().toIso8601String());
      
      debugPrint('🔐 CSRF protection enabled');
    } catch (e) {
      debugPrint('❌ Error setting up CSRF protection: $e');
    }
  }

  /// Initialize rate limiting
  static void _initializeRateLimiting() {
    if (!kIsWeb) return;

    try {
      // Clear rate limit tracker periodically
      Timer.periodic(const Duration(minutes: 1), (timer) {
        _rateLimitTracker.clear();
      });

      debugPrint('⏱️ Rate limiting initialized');
    } catch (e) {
      debugPrint('❌ Error initializing rate limiting: $e');
    }
  }

  /// Setup input validation
  static void _setupInputValidation() {
    if (!kIsWeb) return;

    try {
      // Monitor form inputs for suspicious patterns (web only)
      if (kIsWeb) {
        try {
          html.document.addEventListener('input', (event) {
            // Use dynamic typing for cross-platform compatibility
            final target = event.target;
            if (target != null) {
              final value = (target as dynamic).value;
              if (value is String) {
                _validateInput(value);
              }
            }
          });
        } catch (e) {
          debugPrint('Input monitoring not available on this platform');
        }
      }

      debugPrint('✅ Input validation enabled');
    } catch (e) {
      debugPrint('❌ Error setting up input validation: $e');
    }
  }

  /// Start security monitoring
  static void _startSecurityMonitoring() {
    if (!kIsWeb) return;

    _securityTimer = Timer.periodic(const Duration(minutes: 5), (timer) {
      try {
        _performSecurityChecks();
        _analyzeSecurityLogs();
        _cleanupOldLogs();
      } catch (e) {
        debugPrint('❌ Security monitoring error: $e');
      }
    });
  }

  /// Generate secure token using crypto package
  static String _generateSecureToken() {
    final random = Random.secure();
    final bytes = Uint8List.fromList(List<int>.generate(32, (i) => random.nextInt(256)));

    // Use SHA-256 hash for additional security
    final digest = sha256.convert(bytes);
    return base64Url.encode(digest.bytes);
  }

  /// Validate input for security threats
  static bool _validateInput(String input) {
    if (input.isEmpty) return true;

    try {
      // Check for XSS patterns
      final xssPatterns = [
        r'<script[^>]*>.*?</script>',
        r'javascript:',
        r'on\w+\s*=',
        r'<iframe[^>]*>',
        r'<object[^>]*>',
        r'<embed[^>]*>',
      ];

      for (final pattern in xssPatterns) {
        if (RegExp(pattern, caseSensitive: false).hasMatch(input)) {
          _logSuspiciousActivity('XSS attempt detected: $pattern');
          return false;
        }
      }

      // Check for SQL injection patterns
      final sqlPatterns = [
        r"'.*?--",
        r'union.*?select',
        r'drop.*?table',
        r'insert.*?into',
        r'delete.*?from',
      ];

      for (final pattern in sqlPatterns) {
        if (RegExp(pattern, caseSensitive: false).hasMatch(input)) {
          _logSuspiciousActivity('SQL injection attempt detected: $pattern');
          return false;
        }
      }

      return true;
    } catch (e) {
      debugPrint('❌ Error validating input: $e');
      return false;
    }
  }

  /// Check rate limits
  static bool checkRateLimit(String identifier, {int maxRequests = 100}) {
    try {
      final currentCount = _rateLimitTracker[identifier] ?? 0;
      
      if (currentCount >= maxRequests) {
        _logSuspiciousActivity('Rate limit exceeded for: $identifier');
        return false;
      }
      
      _rateLimitTracker[identifier] = currentCount + 1;
      return true;
    } catch (e) {
      debugPrint('❌ Error checking rate limit: $e');
      return false;
    }
  }

  /// Validate CSRF token
  static Future<bool> validateCSRFToken(String token) async {
    if (!kIsWeb) return true;

    try {
      final prefs = await SharedPreferences.getInstance();
      final storedToken = prefs.getString('csrf_token');
      final tokenCreated = prefs.getString('csrf_token_created');
      
      if (storedToken == null || tokenCreated == null) {
        return false;
      }
      
      // Check token expiry (24 hours)
      final createdTime = DateTime.parse(tokenCreated);
      if (DateTime.now().difference(createdTime).inHours > 24) {
        await _setupCSRFProtection(); // Regenerate token
        return false;
      }
      
      return storedToken == token;
    } catch (e) {
      debugPrint('❌ Error validating CSRF token: $e');
      return false;
    }
  }

  /// Get current CSRF token
  static Future<String?> getCSRFToken() async {
    if (!kIsWeb) return null;

    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getString('csrf_token');
    } catch (e) {
      debugPrint('❌ Error getting CSRF token: $e');
      return null;
    }
  }

  /// Sanitize HTML input
  static String sanitizeHTML(String input) {
    if (input.isEmpty) return input;

    try {
      // Remove potentially dangerous HTML tags and attributes
      String sanitized = input
          .replaceAll(RegExp(r'<script[^>]*>.*?</script>', caseSensitive: false), '')
          .replaceAll(RegExp(r'<iframe[^>]*>.*?</iframe>', caseSensitive: false), '')
          .replaceAll(RegExp(r'<object[^>]*>.*?</object>', caseSensitive: false), '')
          .replaceAll(RegExp(r'<embed[^>]*>', caseSensitive: false), '')
          .replaceAll(RegExp(r'javascript:', caseSensitive: false), '')
          .replaceAll(RegExp(r'on\w+\s*=', caseSensitive: false), '');

      return sanitized;
    } catch (e) {
      debugPrint('❌ Error sanitizing HTML: $e');
      return input;
    }
  }

  /// Log suspicious activity
  static void _logSuspiciousActivity(String activity) {
    try {
      final timestamp = DateTime.now().toIso8601String();
      final logEntry = '$timestamp: $activity';
      _suspiciousActivities.add(logEntry);
      
      // Keep only last 100 entries
      if (_suspiciousActivities.length > 100) {
        _suspiciousActivities.removeAt(0);
      }
      
      debugPrint('🚨 Security alert: $activity');
    } catch (e) {
      debugPrint('❌ Error logging suspicious activity: $e');
    }
  }

  /// Perform security checks
  static void _performSecurityChecks() {
    try {
      // Check for suspicious URL patterns
      final currentUrl = html.window.location.href;
      if (_containsSuspiciousPatterns(currentUrl)) {
        _logSuspiciousActivity('Suspicious URL detected: $currentUrl');
      }

      // Check for unusual user agent
      final userAgent = html.window.navigator.userAgent;
      if (_isUnusualUserAgent(userAgent)) {
        _logSuspiciousActivity('Unusual user agent: $userAgent');
      }

    } catch (e) {
      debugPrint('❌ Error performing security checks: $e');
    }
  }

  /// Analyze security logs
  static void _analyzeSecurityLogs() {
    try {
      if (_suspiciousActivities.length > 10) {
        debugPrint('⚠️ High number of suspicious activities detected');
      }
    } catch (e) {
      debugPrint('❌ Error analyzing security logs: $e');
    }
  }

  /// Cleanup old logs
  static void _cleanupOldLogs() {
    try {
      // Remove logs older than 1 hour
      final oneHourAgo = DateTime.now().subtract(const Duration(hours: 1));
      _suspiciousActivities.removeWhere((log) {
        try {
          final timestamp = DateTime.parse(log.split(':')[0]);
          return timestamp.isBefore(oneHourAgo);
        } catch (e) {
          return true; // Remove malformed entries
        }
      });
    } catch (e) {
      debugPrint('❌ Error cleaning up logs: $e');
    }
  }

  /// Check for suspicious URL patterns
  static bool _containsSuspiciousPatterns(String url) {
    final suspiciousPatterns = [
      'javascript:',
      'data:text/html',
      '<script',
      'vbscript:',
    ];

    return suspiciousPatterns.any((pattern) => 
        url.toLowerCase().contains(pattern.toLowerCase()));
  }

  /// Check for unusual user agent
  static bool _isUnusualUserAgent(String userAgent) {
    final suspiciousAgents = [
      'bot',
      'crawler',
      'spider',
      'scraper',
    ];

    return suspiciousAgents.any((agent) => 
        userAgent.toLowerCase().contains(agent.toLowerCase()));
  }

  /// Generate secure hash for crypto payments (future use)
  static String generatePaymentHash(String paymentData) {
    final bytes = utf8.encode(paymentData);
    final digest = sha256.convert(bytes);
    return digest.toString();
  }

  /// Verify crypto payment hash (future use)
  static bool verifyPaymentHash(String paymentData, String expectedHash) {
    final actualHash = generatePaymentHash(paymentData);
    return actualHash == expectedHash;
  }

  /// Generate secure wallet address hash (future use)
  static String generateWalletHash(String walletAddress) {
    final bytes = utf8.encode(walletAddress + DateTime.now().toIso8601String());
    final digest = sha512.convert(bytes);
    return digest.toString();
  }

  /// Get security report
  static Map<String, dynamic> getSecurityReport() {
    return {
      'suspicious_activities_count': _suspiciousActivities.length,
      'rate_limit_violations': _rateLimitTracker.length,
      'last_security_check': DateTime.now().toIso8601String(),
      'security_status': _suspiciousActivities.length < 5 ? 'good' : 'warning',
      'crypto_security_ready': true, // Ready for crypto payment integration
    };
  }

  /// Cleanup resources
  static void dispose() {
    _securityTimer?.cancel();
    _suspiciousActivities.clear();
    _rateLimitTracker.clear();
    _isInitialized = false;
  }
}
