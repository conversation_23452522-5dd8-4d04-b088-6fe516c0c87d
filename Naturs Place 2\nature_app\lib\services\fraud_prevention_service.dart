import 'package:flutter/foundation.dart';
import '../models/commission_models.dart';
import 'postgres_service.dart';

/// Advanced fraud prevention service for commission system
class FraudPreventionService {
  static final FraudPreventionService _instance = FraudPreventionService._internal();
  factory FraudPreventionService() => _instance;
  FraudPreventionService._internal();

  final PostgreSQLService _postgresService = PostgreSQLService();
  
  // Fraud detection thresholds
  static const double _maxConversionRate = 0.50; // 50% max conversion rate
  static const double _minConversionRate = 0.001; // 0.1% min conversion rate
  static const int _maxDailyPurchases = 20; // Max purchases per partner per day
  static const double _maxSinglePurchase = 1000.0; // Max single purchase amount
  // static const int _maxRefundsPerWeek = 5; // Max refunds per partner per week (reserved for future use)

  /// Initialize fraud prevention service
  Future<void> initialize() async {
    await _postgresService.initialize();
    debugPrint('✅ Fraud Prevention Service initialized');
  }

  /// Validate transaction before commission creation
  Future<FraudValidationResult> validateTransaction(Transaction transaction) async {
    final checks = <String, bool>{};
    final warnings = <String>[];
    final errors = <String>[];

    try {
      // Check 1: Self-referral detection
      if (await _isSelfReferral(transaction)) {
        errors.add('Self-referral detected: customer and partner are the same');
        checks['self_referral'] = false;
      } else {
        checks['self_referral'] = true;
      }

      // Check 2: Purchase amount validation
      if (_isValidPurchaseAmount(transaction.total)) {
        checks['purchase_amount'] = true;
      } else {
        errors.add('Invalid purchase amount: \$${transaction.total}');
        checks['purchase_amount'] = false;
      }

      // Check 3: Partner daily purchase limit
      final dailyPurchases = await _getPartnerDailyPurchases(transaction.affiliateId!, transaction.createdAt);
      if (dailyPurchases < _maxDailyPurchases) {
        checks['daily_limit'] = true;
      } else {
        warnings.add('Partner has $dailyPurchases purchases today (limit: $_maxDailyPurchases)');
        checks['daily_limit'] = false;
      }

      // Check 4: Rapid purchase pattern
      if (await _hasRapidPurchasePattern(transaction)) {
        warnings.add('Rapid purchase pattern detected');
        checks['rapid_pattern'] = false;
      } else {
        checks['rapid_pattern'] = true;
      }

      // Check 5: Customer purchase history
      if (await _hasValidCustomerHistory(transaction.customerId)) {
        checks['customer_history'] = true;
      } else {
        warnings.add('New customer with limited history');
        checks['customer_history'] = false;
      }

      // Check 6: Partner conversion rate
      final conversionRate = await _getPartnerConversionRate(transaction.affiliateId!);
      if (conversionRate >= _minConversionRate && conversionRate <= _maxConversionRate) {
        checks['conversion_rate'] = true;
      } else {
        if (conversionRate > _maxConversionRate) {
          errors.add('Suspiciously high conversion rate: ${(conversionRate * 100).toStringAsFixed(1)}%');
        } else {
          warnings.add('Very low conversion rate: ${(conversionRate * 100).toStringAsFixed(1)}%');
        }
        checks['conversion_rate'] = false;
      }

      // Check 7: Duplicate transaction detection
      if (await _isDuplicateTransaction(transaction)) {
        errors.add('Duplicate transaction detected');
        checks['duplicate_check'] = false;
      } else {
        checks['duplicate_check'] = true;
      }

    } catch (e) {
      errors.add('Fraud validation error: $e');
      debugPrint('❌ Fraud validation failed: $e');
    }

    final riskScore = _calculateRiskScore(checks, warnings.length, errors.length);
    final isValid = errors.isEmpty && riskScore < 0.7;

    return FraudValidationResult(
      isValid: isValid,
      riskScore: riskScore,
      checks: checks,
      warnings: warnings,
      errors: errors,
    );
  }

  /// Check if transaction is a self-referral
  Future<bool> _isSelfReferral(Transaction transaction) async {
    if (transaction.affiliateId == null) return false;
    
    // Check if customer ID matches partner ID or related accounts
    if (transaction.customerId == transaction.affiliateId) {
      return true;
    }
    
    // Additional checks could include:
    // - Same email address
    // - Same IP address
    // - Same payment method
    // - Same shipping address
    
    return false;
  }

  /// Validate purchase amount
  bool _isValidPurchaseAmount(double amount) {
    return amount > 0 && amount <= _maxSinglePurchase;
  }

  /// Get partner's daily purchase count
  Future<int> _getPartnerDailyPurchases(String partnerId, DateTime date) async {
    try {
      // final startOfDay = DateTime(date.year, date.month, date.day); // Reserved for future use
      // final endOfDay = startOfDay.add(const Duration(days: 1)); // Reserved for future use
      
      // Query would count transactions for partner on specific day
      // For now, return mock data
      return 3; // Mock: 3 purchases today
    } catch (e) {
      debugPrint('❌ Failed to get daily purchases: $e');
      return 0;
    }
  }

  /// Check for rapid purchase patterns
  Future<bool> _hasRapidPurchasePattern(Transaction transaction) async {
    if (transaction.affiliateId == null) return false;
    
    try {
      // final last10Minutes = transaction.createdAt.subtract(const Duration(minutes: 10)); // Reserved for future use
      
      // Query would check for multiple purchases in short time frame
      // For now, return mock data
      return false; // Mock: no rapid pattern
    } catch (e) {
      debugPrint('❌ Failed to check rapid pattern: $e');
      return false;
    }
  }

  /// Validate customer purchase history
  Future<bool> _hasValidCustomerHistory(String customerId) async {
    try {
      // Query would check customer's purchase history
      // For now, return mock data
      return true; // Mock: valid customer
    } catch (e) {
      debugPrint('❌ Failed to check customer history: $e');
      return true; // Default to valid if check fails
    }
  }

  /// Get partner's conversion rate
  Future<double> _getPartnerConversionRate(String partnerId) async {
    try {
      // Query would calculate conversion rate from database
      // For now, return mock data
      return 0.12; // Mock: 12% conversion rate
    } catch (e) {
      debugPrint('❌ Failed to get conversion rate: $e');
      return 0.05; // Default to 5% if check fails
    }
  }

  /// Check for duplicate transactions
  Future<bool> _isDuplicateTransaction(Transaction transaction) async {
    try {
      // Query would check for duplicate order IDs or similar transactions
      // For now, return mock data
      return false; // Mock: not duplicate
    } catch (e) {
      debugPrint('❌ Failed to check duplicates: $e');
      return false;
    }
  }

  /// Calculate fraud risk score (0.0 = no risk, 1.0 = high risk)
  double _calculateRiskScore(Map<String, bool> checks, int warningCount, int errorCount) {
    double score = 0.0;
    
    // Base score from failed checks
    final failedChecks = checks.values.where((passed) => !passed).length;
    score += failedChecks * 0.15; // Each failed check adds 15%
    
    // Add score for warnings and errors
    score += warningCount * 0.05; // Each warning adds 5%
    score += errorCount * 0.25; // Each error adds 25%
    
    // Cap at 1.0
    return score > 1.0 ? 1.0 : score;
  }

  /// Monitor partner for ongoing fraud patterns
  Future<PartnerFraudReport> generatePartnerFraudReport(String partnerId) async {
    try {
      final last30Days = DateTime.now().subtract(const Duration(days: 30));
      
      return PartnerFraudReport(
        partnerId: partnerId,
        reportDate: DateTime.now(),
        conversionRate: await _getPartnerConversionRate(partnerId),
        totalTransactions: await _getPartnerTransactionCount(partnerId, last30Days),
        refundRate: await _getPartnerRefundRate(partnerId, last30Days),
        averagePurchaseAmount: await _getPartnerAveragePurchase(partnerId, last30Days),
        suspiciousActivityCount: await _getSuspiciousActivityCount(partnerId, last30Days),
        riskLevel: await _calculatePartnerRiskLevel(partnerId),
        recommendations: await _generateRecommendations(partnerId),
      );
    } catch (e) {
      debugPrint('❌ Failed to generate fraud report: $e');
      return PartnerFraudReport.empty(partnerId);
    }
  }

  /// Block partner if fraud is confirmed
  Future<void> blockPartner(String partnerId, String reason) async {
    try {
      // Implementation would:
      // 1. Mark partner as blocked in database
      // 2. Cancel all pending commissions
      // 3. Send notification to admin
      // 4. Log the action for audit
      
      debugPrint('🚫 Partner blocked: $partnerId - $reason');
    } catch (e) {
      debugPrint('❌ Failed to block partner: $e');
    }
  }

  // Helper methods for fraud report
  Future<int> _getPartnerTransactionCount(String partnerId, DateTime since) async {
    return 25; // Mock data
  }

  Future<double> _getPartnerRefundRate(String partnerId, DateTime since) async {
    return 0.08; // Mock: 8% refund rate
  }

  Future<double> _getPartnerAveragePurchase(String partnerId, DateTime since) async {
    return 75.50; // Mock: $75.50 average
  }

  Future<int> _getSuspiciousActivityCount(String partnerId, DateTime since) async {
    return 0; // Mock: no suspicious activity
  }

  Future<String> _calculatePartnerRiskLevel(String partnerId) async {
    final conversionRate = await _getPartnerConversionRate(partnerId);
    final refundRate = await _getPartnerRefundRate(partnerId, DateTime.now().subtract(const Duration(days: 30)));
    
    if (conversionRate > 0.40 || refundRate > 0.20) return 'HIGH';
    if (conversionRate > 0.25 || refundRate > 0.15) return 'MEDIUM';
    return 'LOW';
  }

  Future<List<String>> _generateRecommendations(String partnerId) async {
    final recommendations = <String>[];
    final conversionRate = await _getPartnerConversionRate(partnerId);
    
    if (conversionRate > 0.30) {
      recommendations.add('Monitor for potential fraud - unusually high conversion rate');
    }
    if (conversionRate < 0.02) {
      recommendations.add('Provide conversion optimization training');
    }
    
    return recommendations;
  }
}

/// Result of fraud validation
class FraudValidationResult {
  final bool isValid;
  final double riskScore;
  final Map<String, bool> checks;
  final List<String> warnings;
  final List<String> errors;

  FraudValidationResult({
    required this.isValid,
    required this.riskScore,
    required this.checks,
    required this.warnings,
    required this.errors,
  });
}

/// Partner fraud analysis report
class PartnerFraudReport {
  final String partnerId;
  final DateTime reportDate;
  final double conversionRate;
  final int totalTransactions;
  final double refundRate;
  final double averagePurchaseAmount;
  final int suspiciousActivityCount;
  final String riskLevel;
  final List<String> recommendations;

  PartnerFraudReport({
    required this.partnerId,
    required this.reportDate,
    required this.conversionRate,
    required this.totalTransactions,
    required this.refundRate,
    required this.averagePurchaseAmount,
    required this.suspiciousActivityCount,
    required this.riskLevel,
    required this.recommendations,
  });

  factory PartnerFraudReport.empty(String partnerId) {
    return PartnerFraudReport(
      partnerId: partnerId,
      reportDate: DateTime.now(),
      conversionRate: 0.0,
      totalTransactions: 0,
      refundRate: 0.0,
      averagePurchaseAmount: 0.0,
      suspiciousActivityCount: 0,
      riskLevel: 'UNKNOWN',
      recommendations: ['Unable to generate report'],
    );
  }
}
