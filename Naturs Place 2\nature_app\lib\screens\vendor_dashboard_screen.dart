import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import '../models/vendor_models.dart';
import '../models/commission_models.dart';
import '../services/vendor_management_service.dart';
import '../services/commission_service.dart';
import 'marketing_opportunities_screen.dart';

/// Vendor dashboard for managing partnerships and tracking performance
class VendorDashboardScreen extends StatefulWidget {
  const VendorDashboardScreen({super.key});

  @override
  State<VendorDashboardScreen> createState() => _VendorDashboardScreenState();
}

class _VendorDashboardScreenState extends State<VendorDashboardScreen> with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final VendorManagementService _vendorService = VendorManagementService();
  final CommissionService _commissionService = CommissionService();

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 5, vsync: this);
    _initializeServices();
  }

  Future<void> _initializeServices() async {
    await _vendorService.initialize();
    await _commissionService.initialize();
    setState(() {});
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Row(
          children: [
            FaIcon(FontAwesomeIcons.handshake, size: 20),
            SizedBox(width: 8),
            Text('Partner Dashboard'),
          ],
        ),
        backgroundColor: const Color(0xFF22c55e),
        foregroundColor: Colors.white,
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          tabs: const [
            Tab(text: 'Overview'),
            Tab(text: 'Vendors'),
            Tab(text: 'Affiliates'),
            Tab(text: 'Marketing'),
            Tab(text: 'Analytics'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildOverviewTab(),
          _buildVendorsTab(),
          _buildAffiliatesTab(),
          _buildMarketingTab(),
          _buildAnalyticsTab(),
        ],
      ),
    );
  }

  Widget _buildOverviewTab() {
    final analytics = _commissionService.getCommissionAnalytics();
    
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Revenue Overview Cards
          Row(
            children: [
              Expanded(
                child: _buildMetricCard(
                  'Total Revenue',
                  '\$${analytics['totalCommissions']?.toStringAsFixed(2) ?? '0.00'}',
                  Icons.attach_money,
                  Colors.green,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildMetricCard(
                  'Pending Payouts',
                  '\$${analytics['pendingCommissions']?.toStringAsFixed(2) ?? '0.00'}',
                  Icons.pending_actions,
                  Colors.orange,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          
          Row(
            children: [
              Expanded(
                child: _buildMetricCard(
                  'Active Vendors',
                  '${_vendorService.verifiedVendors.length}',
                  Icons.store,
                  Colors.blue,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildMetricCard(
                  'Active Affiliates',
                  '${_vendorService.activeAffiliates.length}',
                  Icons.people,
                  Colors.purple,
                ),
              ),
            ],
          ),
          const SizedBox(height: 24),

          // Recent Transactions
          _buildSectionHeader('Recent Transactions'),
          const SizedBox(height: 16),
          _buildRecentTransactions(),
          
          const SizedBox(height: 24),

          // Top Performers
          _buildSectionHeader('Top Performers'),
          const SizedBox(height: 16),
          _buildTopPerformers(),
        ],
      ),
    );
  }

  Widget _buildVendorsTab() {
    final vendors = _vendorService.vendors;
    
    return Column(
      children: [
        // Vendor Stats
        Container(
          padding: const EdgeInsets.all(16),
          color: Colors.grey[50],
          child: Row(
            children: [
              Expanded(
                child: _buildStatItem('Total Vendors', '${vendors.length}'),
              ),
              Expanded(
                child: _buildStatItem('Verified', '${_vendorService.verifiedVendors.length}'),
              ),
              Expanded(
                child: _buildStatItem('Featured', '${_vendorService.featuredVendors.length}'),
              ),
            ],
          ),
        ),
        
        // Vendor List
        Expanded(
          child: ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: vendors.length,
            itemBuilder: (context, index) {
              final vendor = vendors[index];
              return _buildVendorCard(vendor);
            },
          ),
        ),
      ],
    );
  }

  Widget _buildAffiliatesTab() {
    final affiliates = _vendorService.affiliates;
    
    return Column(
      children: [
        // Affiliate Stats
        Container(
          padding: const EdgeInsets.all(16),
          color: Colors.grey[50],
          child: Row(
            children: [
              Expanded(
                child: _buildStatItem('Total Affiliates', '${affiliates.length}'),
              ),
              Expanded(
                child: _buildStatItem('Active', '${_vendorService.activeAffiliates.length}'),
              ),
              Expanded(
                child: _buildStatItem('Top Tier', '${affiliates.where((a) => a.tier == CommissionTier.tier4).length}'),
              ),
            ],
          ),
        ),
        
        // Affiliate List
        Expanded(
          child: ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: affiliates.length,
            itemBuilder: (context, index) {
              final affiliate = affiliates[index];
              return _buildAffiliateCard(affiliate);
            },
          ),
        ),
      ],
    );
  }

  Widget _buildMarketingTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Marketing Opportunities Header
          Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [const Color(0xFF22c55e), Colors.green[400]!],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(16),
            ),
            child: Row(
              children: [
                const FaIcon(FontAwesomeIcons.bullhorn, color: Colors.white, size: 32),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Marketing Opportunities',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'Boost your product visibility and sales',
                        style: TextStyle(
                          color: Colors.white.withValues(alpha: 0.9),
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                ),
                ElevatedButton(
                  onPressed: () {
                    Navigator.of(context).push(
                      MaterialPageRoute(
                        builder: (context) => const MarketingOpportunitiesScreen(),
                      ),
                    );
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.white,
                    foregroundColor: const Color(0xFF22c55e),
                    padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
                  ),
                  child: const Text(
                    'Explore All',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: 24),

          // Quick Marketing Actions
          Text(
            'Quick Marketing Actions',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),

          Row(
            children: [
              Expanded(
                child: _buildQuickActionCard(
                  'Featured Placement',
                  'Get your products featured',
                  Icons.star,
                  Colors.amber,
                  () => _navigateToMarketing('featured'),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildQuickActionCard(
                  'Email Campaign',
                  'Reach targeted customers',
                  Icons.email,
                  Colors.blue,
                  () => _navigateToMarketing('email'),
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          Row(
            children: [
              Expanded(
                child: _buildQuickActionCard(
                  'Social Media Boost',
                  'Amplify social presence',
                  Icons.share,
                  Colors.purple,
                  () => _navigateToMarketing('social'),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildQuickActionCard(
                  'SEO Optimization',
                  'Improve search ranking',
                  Icons.search,
                  Colors.green,
                  () => _navigateToMarketing('seo'),
                ),
              ),
            ],
          ),

          const SizedBox(height: 24),

          // Marketing Performance
          Text(
            'Marketing Performance',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),

          Row(
            children: [
              Expanded(
                child: _buildMetricCard(
                  'Campaign ROI',
                  '340%',
                  Icons.trending_up,
                  Colors.green,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildMetricCard(
                  'Click Rate',
                  '12.5%',
                  Icons.mouse,
                  Colors.blue,
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          Row(
            children: [
              Expanded(
                child: _buildMetricCard(
                  'Conversions',
                  '8.2%',
                  Icons.shopping_cart,
                  Colors.orange,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildMetricCard(
                  'Ad Spend',
                  '\$2,450',
                  Icons.attach_money,
                  Colors.purple,
                ),
              ),
            ],
          ),

          const SizedBox(height: 24),

          // Active Campaigns
          Text(
            'Active Campaigns',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),

          Card(
            child: Column(
              children: [
                _buildCampaignListItem(
                  'Featured Product Placement',
                  'Ashwagandha Vitamins & Minerals',
                  'Active',
                  '2,450 views',
                  Colors.green,
                ),
                const Divider(height: 1),
                _buildCampaignListItem(
                  'Email Marketing Campaign',
                  'Spring Wellness Collection',
                  'Active',
                  '1,890 clicks',
                  Colors.blue,
                ),
                const Divider(height: 1),
                _buildCampaignListItem(
                  'Social Media Boost',
                  'Organic Turmeric Capsules',
                  'Scheduled',
                  'Starts tomorrow',
                  Colors.orange,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAnalyticsTab() {
    final analytics = _commissionService.getCommissionAnalytics();
    
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSectionHeader('Commission Analytics'),
          const SizedBox(height: 16),
          
          // Commission Breakdown
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Commission Breakdown',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),
                  _buildAnalyticsRow('Total Commissions', '\$${analytics['totalCommissions']?.toStringAsFixed(2) ?? '0.00'}'),
                  _buildAnalyticsRow('Paid Commissions', '\$${analytics['paidCommissions']?.toStringAsFixed(2) ?? '0.00'}'),
                  _buildAnalyticsRow('Pending Commissions', '\$${analytics['pendingCommissions']?.toStringAsFixed(2) ?? '0.00'}'),
                  const Divider(),
                  _buildAnalyticsRow('Total Transactions', '${analytics['totalTransactions'] ?? 0}'),
                  _buildAnalyticsRow('Completed Transactions', '${analytics['completedTransactions'] ?? 0}'),
                  _buildAnalyticsRow('Total Payouts', '${analytics['totalPayouts'] ?? 0}'),
                  _buildAnalyticsRow('Total Payout Amount', '\$${analytics['totalPayoutAmount']?.toStringAsFixed(2) ?? '0.00'}'),
                ],
              ),
            ),
          ),
          
          const SizedBox(height: 24),
          
          // Category Performance
          _buildSectionHeader('Category Performance'),
          const SizedBox(height: 16),
          _buildCategoryPerformance(),
        ],
      ),
    );
  }

  Widget _buildMetricCard(String title, String value, IconData icon, Color color) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(icon, color: color, size: 24),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    title,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Colors.grey[600],
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              value,
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Text(
      title,
      style: Theme.of(context).textTheme.titleLarge?.copyWith(
        fontWeight: FontWeight.bold,
      ),
    );
  }

  Widget _buildStatItem(String label, String value) {
    return Column(
      children: [
        Text(
          value,
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.bold,
            color: const Color(0xFF22c55e),
          ),
        ),
        const SizedBox(height: 4),
        Text(
          label,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: Colors.grey[600],
          ),
        ),
      ],
    );
  }

  Widget _buildRecentTransactions() {
    final recentTransactions = _commissionService.transactions.take(5).toList();
    
    return Card(
      child: Column(
        children: recentTransactions.map((transaction) {
          return ListTile(
            leading: CircleAvatar(
              backgroundColor: _getStatusColor(transaction.status).withValues(alpha: 0.1),
              child: Icon(
                _getStatusIcon(transaction.status),
                color: _getStatusColor(transaction.status),
                size: 20,
              ),
            ),
            title: Text(transaction.productName),
            subtitle: Text('Order: ${transaction.orderId}'),
            trailing: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Text(
                  '\$${transaction.total.toStringAsFixed(2)}',
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
                Text(
                  _formatDate(transaction.createdAt),
                  style: Theme.of(context).textTheme.bodySmall,
                ),
              ],
            ),
          );
        }).toList(),
      ),
    );
  }

  Widget _buildTopPerformers() {
    final topVendors = _vendorService.getTopVendors(limit: 3);
    final topAffiliates = _vendorService.getTopAffiliates(limit: 3);
    
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Expanded(
          child: Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Top Vendors',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 12),
                  ...topVendors.map((vendor) => Padding(
                    padding: const EdgeInsets.only(bottom: 8),
                    child: Row(
                      children: [
                        Expanded(child: Text(vendor.businessName)),
                        Text(
                          '\$${vendor.totalSales.toStringAsFixed(0)}',
                          style: const TextStyle(fontWeight: FontWeight.bold),
                        ),
                      ],
                    ),
                  )),
                ],
              ),
            ),
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Top Affiliates',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 12),
                  ...topAffiliates.map((affiliate) => Padding(
                    padding: const EdgeInsets.only(bottom: 8),
                    child: Row(
                      children: [
                        Expanded(child: Text(affiliate.name)),
                        Text(
                          '\$${affiliate.totalEarnings.toStringAsFixed(0)}',
                          style: const TextStyle(fontWeight: FontWeight.bold),
                        ),
                      ],
                    ),
                  )),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildVendorCard(Vendor vendor) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: _getVerificationColor(vendor.status).withValues(alpha: 0.1),
          child: Icon(
            _getVerificationIcon(vendor.status),
            color: _getVerificationColor(vendor.status),
          ),
        ),
        title: Text(vendor.businessName),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('${vendor.name} • ${vendor.tier.name.toUpperCase()}'),
            Text('Sales: \$${vendor.totalSales.toStringAsFixed(0)} • Orders: ${vendor.totalOrders}'),
          ],
        ),
        trailing: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            if (vendor.isFeatured)
              const Icon(Icons.star, color: Colors.amber, size: 16),
            Text(
              '${(vendor.averageRating * 10).round() / 10}★',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ],
        ),
        isThreeLine: true,
        onTap: () => _showVendorDetails(vendor),
      ),
    );
  }

  Widget _buildAffiliateCard(AffiliatePartner affiliate) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: _getTierColor(affiliate.tier).withValues(alpha: 0.1),
          child: Text(
            affiliate.tier.name.substring(4).toUpperCase(),
            style: TextStyle(
              color: _getTierColor(affiliate.tier),
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        title: Text(affiliate.name),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Code: ${affiliate.trackingCode}'),
            Text('Earnings: \$${affiliate.totalEarnings.toStringAsFixed(0)} • Conv: ${(affiliate.conversionRate * 100).toStringAsFixed(1)}%'),
          ],
        ),
        trailing: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            Text(
              '\$${affiliate.pendingPayout.toStringAsFixed(0)}',
              style: const TextStyle(
                fontWeight: FontWeight.bold,
                color: Colors.green,
              ),
            ),
            const Text('Pending', style: TextStyle(fontSize: 12)),
          ],
        ),
        isThreeLine: true,
        onTap: () => _showAffiliateDetails(affiliate),
      ),
    );
  }

  Widget _buildAnalyticsRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label),
          Text(
            value,
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
        ],
      ),
    );
  }

  Widget _buildCategoryPerformance() {
    final categories = ProductCategory.values;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Sales by Category',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            ...categories.map((category) {
              final categoryTransactions = _commissionService.transactions
                  .where((t) => t.category == category)
                  .toList();
              final totalSales = categoryTransactions.fold<double>(
                0.0, (sum, t) => sum + t.total);

              return Padding(
                padding: const EdgeInsets.only(bottom: 12),
                child: Row(
                  children: [
                    Expanded(
                      flex: 2,
                      child: Text(_getCategoryName(category)),
                    ),
                    Expanded(
                      flex: 1,
                      child: Text('${categoryTransactions.length} orders'),
                    ),
                    Expanded(
                      flex: 1,
                      child: Text(
                        '\$${totalSales.toStringAsFixed(0)}',
                        style: const TextStyle(fontWeight: FontWeight.bold),
                        textAlign: TextAlign.right,
                      ),
                    ),
                  ],
                ),
              );
            }),
          ],
        ),
      ),
    );
  }

  // Helper methods
  Color _getStatusColor(TransactionStatus status) {
    switch (status) {
      case TransactionStatus.completed:
        return Colors.green;
      case TransactionStatus.pending:
        return Colors.orange;
      case TransactionStatus.cancelled:
      case TransactionStatus.refunded:
        return Colors.red;
      case TransactionStatus.disputed:
        return Colors.purple;
    }
  }

  IconData _getStatusIcon(TransactionStatus status) {
    switch (status) {
      case TransactionStatus.completed:
        return Icons.check_circle;
      case TransactionStatus.pending:
        return Icons.pending;
      case TransactionStatus.cancelled:
        return Icons.cancel;
      case TransactionStatus.refunded:
        return Icons.undo;
      case TransactionStatus.disputed:
        return Icons.warning;
    }
  }

  Color _getVerificationColor(VerificationStatus status) {
    switch (status) {
      case VerificationStatus.verified:
        return Colors.green;
      case VerificationStatus.pending:
        return Colors.orange;
      case VerificationStatus.rejected:
      case VerificationStatus.suspended:
        return Colors.red;
    }
  }

  IconData _getVerificationIcon(VerificationStatus status) {
    switch (status) {
      case VerificationStatus.verified:
        return Icons.verified;
      case VerificationStatus.pending:
        return Icons.pending;
      case VerificationStatus.rejected:
        return Icons.cancel;
      case VerificationStatus.suspended:
        return Icons.block;
    }
  }

  Color _getTierColor(CommissionTier tier) {
    switch (tier) {
      case CommissionTier.tier1:
        return Colors.grey;
      case CommissionTier.tier2:
        return Colors.blue;
      case CommissionTier.tier3:
        return Colors.purple;
      case CommissionTier.tier4:
        return Colors.amber;
    }
  }

  String _getCategoryName(ProductCategory category) {
    switch (category) {
      case ProductCategory.supplements:
        return 'Supplements';
      case ProductCategory.essentialOils:
        return 'Essential Oils';
      case ProductCategory.herbalTeas:
        return 'Herbal Teas';
      case ProductCategory.skincare:
        return 'Skincare';
      case ProductCategory.books:
        return 'Books';
      case ProductCategory.equipment:
        return 'Equipment';
      case ProductCategory.consultations:
        return 'Consultations';
    }
  }

  String _formatDate(DateTime date) {
    return '${date.month}/${date.day}/${date.year}';
  }

  void _showVendorDetails(Vendor vendor) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(vendor.businessName),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Contact: ${vendor.name}'),
            Text('Email: ${vendor.email}'),
            Text('Status: ${vendor.status.name}'),
            Text('Tier: ${vendor.tier.name}'),
            Text('Total Sales: \$${vendor.totalSales.toStringAsFixed(2)}'),
            Text('Commission Paid: \$${vendor.totalCommissionPaid.toStringAsFixed(2)}'),
            Text('Rating: ${vendor.averageRating}/5.0'),
            if (vendor.qualifiesForTierUpgrade())
              const Text(
                'Qualifies for tier upgrade!',
                style: TextStyle(color: Colors.green, fontWeight: FontWeight.bold),
              ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _showAffiliateDetails(AffiliatePartner affiliate) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(affiliate.name),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Email: ${affiliate.email}'),
            Text('Website: ${affiliate.website}'),
            Text('Tracking Code: ${affiliate.trackingCode}'),
            Text('Tier: ${affiliate.tier.name}'),
            Text('Total Earnings: \$${affiliate.totalEarnings.toStringAsFixed(2)}'),
            Text('Conversion Rate: ${(affiliate.conversionRate * 100).toStringAsFixed(2)}%'),
            Text('Pending Payout: \$${affiliate.pendingPayout.toStringAsFixed(2)}'),
            if (affiliate.qualifiesForTierUpgrade())
              const Text(
                'Qualifies for tier upgrade!',
                style: TextStyle(color: Colors.green, fontWeight: FontWeight.bold),
              ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  Widget _buildQuickActionCard(String title, String subtitle, IconData icon, Color color, VoidCallback onTap) {
    return Card(
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(icon, color: color, size: 24),
              ),
              const SizedBox(height: 12),
              Text(
                title,
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 14,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                subtitle,
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 12,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCampaignListItem(String campaign, String product, String status, String metric, Color color) {
    return ListTile(
      leading: CircleAvatar(
        backgroundColor: color.withValues(alpha: 0.1),
        child: Icon(Icons.campaign, color: color, size: 20),
      ),
      title: Text(
        campaign,
        style: const TextStyle(fontWeight: FontWeight.w600),
      ),
      subtitle: Text(product),
      trailing: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              status,
              style: TextStyle(
                color: color,
                fontSize: 10,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          const SizedBox(height: 4),
          Text(
            metric,
            style: TextStyle(
              color: Colors.grey[600],
              fontSize: 11,
            ),
          ),
        ],
      ),
    );
  }

  void _navigateToMarketing(String campaignType) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const MarketingOpportunitiesScreen(),
      ),
    );
  }
}