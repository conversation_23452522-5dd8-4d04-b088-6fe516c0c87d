class CommissionController {
  static Map<String, dynamic> calculateCommissions(double saleAmount, String vendorTier) {
    if (saleAmount <= 0 || !['Basic', 'Premium'].contains(vendorTier)) {
      throw ArgumentError('Invalid input');
    }

    final platformFee = saleAmount * (vendorTier == 'Basic' ? 0.05 : 0.03);
    final affiliateCommission = saleAmount * 0.02;

    return {
      'platformFee': platformFee,
      'affiliateCommission': affiliateCommission,
      'vendorPayout': saleAmount - platformFee - affiliateCommission,
    };
  }
}
