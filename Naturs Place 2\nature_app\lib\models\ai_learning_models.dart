import '../providers/app_state.dart';

/// Enhanced query analysis result with learning capabilities
class EnhancedQueryAnalysis {
  final String originalQuery;
  final List<DetectedCondition> detectedConditions;
  final List<DetectedSymptom> detectedSymptoms;
  final List<IdentifiedPlant> identifiedPlants;
  final UrgencyLevel urgencyLevel;
  final ContextAnalysis contextAnalysis;
  final Map<String, double> confidenceScores;
  final List<String> suggestedFollowUps;
  final List<String> learningOpportunities;

  EnhancedQueryAnalysis({
    required this.originalQuery,
    required this.detectedConditions,
    required this.detectedSymptoms,
    required this.identifiedPlants,
    required this.urgencyLevel,
    required this.contextAnalysis,
    required this.confidenceScores,
    required this.suggestedFollowUps,
    required this.learningOpportunities,
  });

  Map<String, dynamic> toJson() {
    return {
      'originalQuery': originalQuery,
      'detectedConditions': detectedConditions.map((c) => c.toJson()).toList(),
      'detectedSymptoms': detectedSymptoms.map((s) => s.toJson()).toList(),
      'identifiedPlants': identifiedPlants.map((p) => p.toJson()).toList(),
      'urgencyLevel': urgencyLevel.toString(),
      'contextAnalysis': contextAnalysis.toJson(),
      'confidenceScores': confidenceScores,
      'suggestedFollowUps': suggestedFollowUps,
      'learningOpportunities': learningOpportunities,
    };
  }
}

/// Detected medical condition with confidence scoring
class DetectedCondition {
  final String name;
  final double confidence;
  final List<String> matchedKeywords;
  final String source;

  DetectedCondition({
    required this.name,
    required this.confidence,
    required this.matchedKeywords,
    required this.source,
  });

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'confidence': confidence,
      'matchedKeywords': matchedKeywords,
      'source': source,
    };
  }

  factory DetectedCondition.fromJson(Map<String, dynamic> json) {
    return DetectedCondition(
      name: json['name'],
      confidence: json['confidence'],
      matchedKeywords: List<String>.from(json['matchedKeywords']),
      source: json['source'],
    );
  }
}

/// Detected symptom with severity and duration analysis
class DetectedSymptom {
  final String name;
  final double confidence;
  final List<String> matchedKeywords;
  final String severity;
  final String duration;
  final String source;

  DetectedSymptom({
    required this.name,
    required this.confidence,
    required this.matchedKeywords,
    required this.severity,
    required this.duration,
    required this.source,
  });

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'confidence': confidence,
      'matchedKeywords': matchedKeywords,
      'severity': severity,
      'duration': duration,
      'source': source,
    };
  }

  factory DetectedSymptom.fromJson(Map<String, dynamic> json) {
    return DetectedSymptom(
      name: json['name'],
      confidence: json['confidence'],
      matchedKeywords: List<String>.from(json['matchedKeywords']),
      severity: json['severity'],
      duration: json['duration'],
      source: json['source'],
    );
  }
}

/// Identified plant with relevance scoring
class IdentifiedPlant {
  final Plant plant;
  final double confidence;
  final List<String> matchedTerms;
  final double relevanceScore;

  IdentifiedPlant({
    required this.plant,
    required this.confidence,
    required this.matchedTerms,
    required this.relevanceScore,
  });

  Map<String, dynamic> toJson() {
    return {
      'plant': plant.toJson(),
      'confidence': confidence,
      'matchedTerms': matchedTerms,
      'relevanceScore': relevanceScore,
    };
  }
}

/// Context analysis for determining response approach
class ContextAnalysis {
  QueryIntent intent = QueryIntent.general;
  ResponseDepth responseDepth = ResponseDepth.basic;
  ExpertiseLevel userExpertiseLevel = ExpertiseLevel.beginner;
  bool requiresDisclaimer = true;
  List<String> contextClues = [];

  ContextAnalysis();

  Map<String, dynamic> toJson() {
    return {
      'intent': intent.toString(),
      'responseDepth': responseDepth.toString(),
      'userExpertiseLevel': userExpertiseLevel.toString(),
      'requiresDisclaimer': requiresDisclaimer,
      'contextClues': contextClues,
    };
  }
}

/// Learning pattern for medical conditions
class ConditionPattern {
  final String conditionName;
  final List<String> keywords;
  final List<String> contextPatterns;
  final Map<String, double> keywordWeights;
  final int occurrenceCount;
  final double averageConfidence;
  final DateTime lastUpdated;

  ConditionPattern({
    required this.conditionName,
    required this.keywords,
    required this.contextPatterns,
    required this.keywordWeights,
    required this.occurrenceCount,
    required this.averageConfidence,
    required this.lastUpdated,
  });

  Map<String, dynamic> toJson() {
    return {
      'conditionName': conditionName,
      'keywords': keywords,
      'contextPatterns': contextPatterns,
      'keywordWeights': keywordWeights,
      'occurrenceCount': occurrenceCount,
      'averageConfidence': averageConfidence,
      'lastUpdated': lastUpdated.toIso8601String(),
    };
  }

  factory ConditionPattern.fromJson(Map<String, dynamic> json) {
    return ConditionPattern(
      conditionName: json['conditionName'],
      keywords: List<String>.from(json['keywords']),
      contextPatterns: List<String>.from(json['contextPatterns']),
      keywordWeights: Map<String, double>.from(json['keywordWeights']),
      occurrenceCount: json['occurrenceCount'],
      averageConfidence: json['averageConfidence'],
      lastUpdated: DateTime.parse(json['lastUpdated']),
    );
  }
}

/// Learning pattern for symptoms
class SymptomPattern {
  final String symptomName;
  final List<String> keywords;
  final List<String> severityIndicators;
  final List<String> durationIndicators;
  final Map<String, double> keywordWeights;
  final int occurrenceCount;
  final double averageConfidence;
  final DateTime lastUpdated;

  SymptomPattern({
    required this.symptomName,
    required this.keywords,
    required this.severityIndicators,
    required this.durationIndicators,
    required this.keywordWeights,
    required this.occurrenceCount,
    required this.averageConfidence,
    required this.lastUpdated,
  });

  Map<String, dynamic> toJson() {
    return {
      'symptomName': symptomName,
      'keywords': keywords,
      'severityIndicators': severityIndicators,
      'durationIndicators': durationIndicators,
      'keywordWeights': keywordWeights,
      'occurrenceCount': occurrenceCount,
      'averageConfidence': averageConfidence,
      'lastUpdated': lastUpdated.toIso8601String(),
    };
  }

  factory SymptomPattern.fromJson(Map<String, dynamic> json) {
    return SymptomPattern(
      symptomName: json['symptomName'],
      keywords: List<String>.from(json['keywords']),
      severityIndicators: List<String>.from(json['severityIndicators']),
      durationIndicators: List<String>.from(json['durationIndicators']),
      keywordWeights: Map<String, double>.from(json['keywordWeights']),
      occurrenceCount: json['occurrenceCount'],
      averageConfidence: json['averageConfidence'],
      lastUpdated: DateTime.parse(json['lastUpdated']),
    );
  }
}

/// Learning pattern for plant mentions
class PlantMentionPattern {
  final String plantId;
  final List<String> alternativeNames;
  final List<String> contextKeywords;
  final Map<String, double> nameWeights;
  final int mentionCount;
  final double averageRelevance;
  final DateTime lastUpdated;

  PlantMentionPattern({
    required this.plantId,
    required this.alternativeNames,
    required this.contextKeywords,
    required this.nameWeights,
    required this.mentionCount,
    required this.averageRelevance,
    required this.lastUpdated,
  });

  Map<String, dynamic> toJson() {
    return {
      'plantId': plantId,
      'alternativeNames': alternativeNames,
      'contextKeywords': contextKeywords,
      'nameWeights': nameWeights,
      'mentionCount': mentionCount,
      'averageRelevance': averageRelevance,
      'lastUpdated': lastUpdated.toIso8601String(),
    };
  }

  factory PlantMentionPattern.fromJson(Map<String, dynamic> json) {
    return PlantMentionPattern(
      plantId: json['plantId'],
      alternativeNames: List<String>.from(json['alternativeNames']),
      contextKeywords: List<String>.from(json['contextKeywords']),
      nameWeights: Map<String, double>.from(json['nameWeights']),
      mentionCount: json['mentionCount'],
      averageRelevance: json['averageRelevance'],
      lastUpdated: DateTime.parse(json['lastUpdated']),
    );
  }
}

/// Learning pattern for urgency assessment
class UrgencyPattern {
  final UrgencyLevel level;
  final List<String> keywords;
  final List<String> contextIndicators;
  final Map<String, double> keywordWeights;
  final int occurrenceCount;
  final double accuracy;
  final DateTime lastUpdated;

  UrgencyPattern({
    required this.level,
    required this.keywords,
    required this.contextIndicators,
    required this.keywordWeights,
    required this.occurrenceCount,
    required this.accuracy,
    required this.lastUpdated,
  });

  Map<String, dynamic> toJson() {
    return {
      'level': level.toString(),
      'keywords': keywords,
      'contextIndicators': contextIndicators,
      'keywordWeights': keywordWeights,
      'occurrenceCount': occurrenceCount,
      'accuracy': accuracy,
      'lastUpdated': lastUpdated.toIso8601String(),
    };
  }
}

/// Learning pattern for context analysis
class ContextPattern {
  final QueryIntent intent;
  final List<String> keywords;
  final List<String> phrasePatterns;
  final Map<String, double> patternWeights;
  final int occurrenceCount;
  final double accuracy;
  final DateTime lastUpdated;

  ContextPattern({
    required this.intent,
    required this.keywords,
    required this.phrasePatterns,
    required this.patternWeights,
    required this.occurrenceCount,
    required this.accuracy,
    required this.lastUpdated,
  });

  Map<String, dynamic> toJson() {
    return {
      'intent': intent.toString(),
      'keywords': keywords,
      'phrasePatterns': phrasePatterns,
      'patternWeights': patternWeights,
      'occurrenceCount': occurrenceCount,
      'accuracy': accuracy,
      'lastUpdated': lastUpdated.toIso8601String(),
    };
  }
}

/// User feedback for learning improvement
class UserFeedback {
  final String queryId;
  final String originalQuery;
  final String responseId;
  final FeedbackType feedbackType;
  final int rating;
  final String? comment;
  final Map<String, dynamic> analysisCorrections;
  final DateTime timestamp;

  UserFeedback({
    required this.queryId,
    required this.originalQuery,
    required this.responseId,
    required this.feedbackType,
    required this.rating,
    this.comment,
    required this.analysisCorrections,
    required this.timestamp,
  });

  Map<String, dynamic> toJson() {
    return {
      'queryId': queryId,
      'originalQuery': originalQuery,
      'responseId': responseId,
      'feedbackType': feedbackType.toString(),
      'rating': rating,
      'comment': comment,
      'analysisCorrections': analysisCorrections,
      'timestamp': timestamp.toIso8601String(),
    };
  }
}

/// Query statistics for performance monitoring
class QueryStatistics {
  int totalQueries = 0;
  int successfulAnalyses = 0;
  int emergencyQueries = 0;
  int highUrgencyQueries = 0;
  Map<String, int> conditionCounts = {};
  Map<String, int> symptomCounts = {};
  Map<String, int> plantMentions = {};
  double averageConfidence = 0.0;
  DateTime lastUpdated = DateTime.now();

  QueryStatistics();

  Map<String, dynamic> toJson() {
    return {
      'totalQueries': totalQueries,
      'successfulAnalyses': successfulAnalyses,
      'emergencyQueries': emergencyQueries,
      'highUrgencyQueries': highUrgencyQueries,
      'conditionCounts': conditionCounts,
      'symptomCounts': symptomCounts,
      'plantMentions': plantMentions,
      'averageConfidence': averageConfidence,
      'lastUpdated': lastUpdated.toIso8601String(),
    };
  }

  factory QueryStatistics.fromJson(Map<String, dynamic> json) {
    final stats = QueryStatistics();
    stats.totalQueries = json['totalQueries'] ?? 0;
    stats.successfulAnalyses = json['successfulAnalyses'] ?? 0;
    stats.emergencyQueries = json['emergencyQueries'] ?? 0;
    stats.highUrgencyQueries = json['highUrgencyQueries'] ?? 0;
    stats.conditionCounts = Map<String, int>.from(json['conditionCounts'] ?? {});
    stats.symptomCounts = Map<String, int>.from(json['symptomCounts'] ?? {});
    stats.plantMentions = Map<String, int>.from(json['plantMentions'] ?? {});
    stats.averageConfidence = json['averageConfidence'] ?? 0.0;
    stats.lastUpdated = DateTime.parse(json['lastUpdated'] ?? DateTime.now().toIso8601String());
    return stats;
  }
}

/// Enums for classification
enum UrgencyLevel { normal, high, emergency }
enum QueryIntent { general, informational, treatmentSeeking, safetyConcern }
enum ResponseDepth { basic, moderate, comprehensive }
enum ExpertiseLevel { beginner, intermediate, advanced }
enum FeedbackType { helpful, notHelpful, incorrect, missingInfo, safetyConcern }
