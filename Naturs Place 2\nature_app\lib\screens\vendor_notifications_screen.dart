import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import '../services/affiliate_marketplace_service.dart';

class VendorNotificationsScreen extends StatefulWidget {
  final String vendorId;

  const VendorNotificationsScreen({
    super.key,
    required this.vendorId,
  });

  @override
  State<VendorNotificationsScreen> createState() => _VendorNotificationsScreenState();
}

class _VendorNotificationsScreenState extends State<VendorNotificationsScreen> {
  bool _showUnreadOnly = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Notifications'),
        backgroundColor: const Color(0xFF22c55e),
        foregroundColor: Colors.white,
        actions: [
          PopupMenuButton<String>(
            onSelected: (value) {
              if (value == 'filter') {
                setState(() {
                  _showUnreadOnly = !_showUnreadOnly;
                });
              } else if (value == 'mark_all_read') {
                _markAllAsRead();
              }
            },
            itemBuilder: (context) => [
              PopupMenuItem(
                value: 'filter',
                child: Row(
                  children: [
                    Icon(_showUnreadOnly ? Icons.visibility : Icons.visibility_off),
                    const SizedBox(width: 8),
                    Text(_showUnreadOnly ? 'Show All' : 'Show Unread Only'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'mark_all_read',
                child: Row(
                  children: [
                    Icon(Icons.mark_email_read),
                    SizedBox(width: 8),
                    Text('Mark All as Read'),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
      body: Consumer<PartnerMarketplaceService>(
        builder: (context, service, child) {
          final notifications = service.getVendorNotifications(
            widget.vendorId,
            unreadOnly: _showUnreadOnly,
          );
          final unreadCount = service.getUnreadNotificationCount(widget.vendorId);

          if (notifications.isEmpty) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    _showUnreadOnly ? Icons.mark_email_read : Icons.notifications_none,
                    size: 64,
                    color: Colors.grey[400],
                  ),
                  const SizedBox(height: 16),
                  Text(
                    _showUnreadOnly 
                      ? 'No unread notifications'
                      : 'No notifications yet',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      color: Colors.grey[600],
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    _showUnreadOnly
                      ? 'All caught up! Check back later for new updates.'
                      : 'You\'ll receive notifications here when customers purchase your products.',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Colors.grey[500],
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            );
          }

          return Column(
            children: [
              // Notification Summary
              if (unreadCount > 0)
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(16),
                  color: Colors.blue[50],
                  child: Row(
                    children: [
                      const Icon(Icons.info, color: Colors.blue),
                      const SizedBox(width: 12),
                      Text(
                        'You have $unreadCount unread notification${unreadCount == 1 ? '' : 's'}',
                        style: const TextStyle(
                          color: Colors.blue,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),

              // Notifications List
              Expanded(
                child: ListView.builder(
                  padding: const EdgeInsets.all(16),
                  itemCount: notifications.length,
                  itemBuilder: (context, index) {
                    final notification = notifications[index];
                    return _buildNotificationCard(context, service, notification);
                  },
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildNotificationCard(
    BuildContext context,
    PartnerMarketplaceService service,
    VendorNotification notification,
  ) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      color: notification.isRead ? null : Colors.blue[50],
      child: InkWell(
        onTap: () {
          if (!notification.isRead) {
            service.markNotificationAsRead(notification.id);
          }
          _showNotificationDetails(context, notification);
        },
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header Row
              Row(
                children: [
                  // Notification Icon
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: _getNotificationColor(notification.type).withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: FaIcon(
                      _getNotificationIcon(notification.type),
                      color: _getNotificationColor(notification.type),
                      size: 20,
                    ),
                  ),
                  const SizedBox(width: 12),
                  
                  // Title and Priority
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Expanded(
                              child: Text(
                                notification.title,
                                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                  fontWeight: notification.isRead ? FontWeight.normal : FontWeight.bold,
                                ),
                              ),
                            ),
                            Container(
                              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                              decoration: BoxDecoration(
                                color: _getPriorityColor(notification.priority).withValues(alpha: 0.1),
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: Text(
                                notification.priority.toUpperCase(),
                                style: TextStyle(
                                  color: _getPriorityColor(notification.priority),
                                  fontSize: 10,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                          ],
                        ),
                        Text(
                          _getNotificationTypeLabel(notification.type),
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: _getNotificationColor(notification.type),
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ),
                  
                  // Unread Indicator
                  if (!notification.isRead)
                    Container(
                      width: 8,
                      height: 8,
                      decoration: const BoxDecoration(
                        color: Colors.blue,
                        shape: BoxShape.circle,
                      ),
                    ),
                ],
              ),
              
              const SizedBox(height: 12),
              
              // Message
              Text(
                notification.message,
                style: Theme.of(context).textTheme.bodyMedium,
              ),
              
              // Purchase Details (if applicable)
              if (notification.type == 'purchase' && notification.data != null) ...[
                const SizedBox(height: 12),
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.green.withValues(alpha: 0.05),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.green.withValues(alpha: 0.2)),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            'Purchase Amount',
                            style: Theme.of(context).textTheme.bodySmall,
                          ),
                          Text(
                            '\$${notification.data!['purchaseAmount'].toStringAsFixed(2)}',
                            style: const TextStyle(
                              fontWeight: FontWeight.bold,
                              color: Colors.green,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 4),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            'Your Commission',
                            style: Theme.of(context).textTheme.bodySmall,
                          ),
                          Text(
                            '\$${notification.data!['commissionAmount'].toStringAsFixed(2)}',
                            style: const TextStyle(
                              fontWeight: FontWeight.bold,
                              color: Colors.orange,
                            ),
                          ),
                        ],
                      ),
                      if (notification.data!['orderId'] != null) ...[
                        const SizedBox(height: 4),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              'Order ID',
                              style: Theme.of(context).textTheme.bodySmall,
                            ),
                            Text(
                              notification.data!['orderId'],
                              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                fontFamily: 'monospace',
                              ),
                            ),
                          ],
                        ),
                      ],
                    ],
                  ),
                ),
              ],
              
              const SizedBox(height: 12),
              
              // Timestamp
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    _formatDateTime(notification.createdAt),
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Colors.grey[600],
                    ),
                  ),
                  if (notification.type == 'purchase')
                    TextButton.icon(
                      onPressed: () => _showPurchaseDetails(context, notification),
                      icon: const Icon(Icons.info_outline, size: 16),
                      label: const Text('Details', style: TextStyle(fontSize: 12)),
                      style: TextButton.styleFrom(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      ),
                    ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showNotificationDetails(BuildContext context, VendorNotification notification) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(notification.title),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Type: ${_getNotificationTypeLabel(notification.type)}'),
            Text('Priority: ${notification.priority}'),
            Text('Created: ${_formatDateTime(notification.createdAt)}'),
            const SizedBox(height: 16),
            Text(notification.message),
            if (notification.data != null) ...[
              const SizedBox(height: 16),
              const Text('Additional Data:', style: TextStyle(fontWeight: FontWeight.bold)),
              ...notification.data!.entries.map((entry) => 
                Text('${entry.key}: ${entry.value}')
              ),
            ],
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _showPurchaseDetails(BuildContext context, VendorNotification notification) {
    if (notification.data == null) return;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Purchase Details'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildDetailRow('Product', notification.data!['productName']),
            _buildDetailRow('Customer', notification.data!['customerName']),
            _buildDetailRow('Order ID', notification.data!['orderId']),
            _buildDetailRow('Amount', '\$${notification.data!['purchaseAmount'].toStringAsFixed(2)}'),
            _buildDetailRow('Commission', '\$${notification.data!['commissionAmount'].toStringAsFixed(2)}'),
            _buildDetailRow('Date', _formatDateTime(notification.createdAt)),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
          ),
          Expanded(child: Text(value)),
        ],
      ),
    );
  }

  void _markAllAsRead() {
    final service = Provider.of<PartnerMarketplaceService>(context, listen: false);
    final unreadNotifications = service.getVendorNotifications(widget.vendorId, unreadOnly: true);
    
    for (final notification in unreadNotifications) {
      service.markNotificationAsRead(notification.id);
    }

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('All notifications marked as read'),
        backgroundColor: Colors.green,
      ),
    );
  }

  Color _getNotificationColor(String type) {
    switch (type) {
      case 'purchase':
        return Colors.green;
      case 'commission':
        return Colors.blue;
      case 'system':
        return Colors.orange;
      case 'marketing':
        return Colors.purple;
      default:
        return Colors.grey;
    }
  }

  IconData _getNotificationIcon(String type) {
    switch (type) {
      case 'purchase':
        return FontAwesomeIcons.cartShopping;
      case 'commission':
        return FontAwesomeIcons.dollarSign;
      case 'system':
        return FontAwesomeIcons.gear;
      case 'marketing':
        return FontAwesomeIcons.bullhorn;
      default:
        return FontAwesomeIcons.bell;
    }
  }

  String _getNotificationTypeLabel(String type) {
    switch (type) {
      case 'purchase':
        return 'Purchase Notification';
      case 'commission':
        return 'Commission Update';
      case 'system':
        return 'System Notification';
      case 'marketing':
        return 'Marketing Update';
      default:
        return 'Notification';
    }
  }

  Color _getPriorityColor(String priority) {
    switch (priority) {
      case 'urgent':
        return Colors.red;
      case 'high':
        return Colors.orange;
      case 'medium':
        return Colors.blue;
      case 'low':
        return Colors.green;
      default:
        return Colors.grey;
    }
  }

  String _formatDateTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inDays > 0) {
      return '${difference.inDays} day${difference.inDays == 1 ? '' : 's'} ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours} hour${difference.inHours == 1 ? '' : 's'} ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes} minute${difference.inMinutes == 1 ? '' : 's'} ago';
    } else {
      return 'Just now';
    }
  }
}
