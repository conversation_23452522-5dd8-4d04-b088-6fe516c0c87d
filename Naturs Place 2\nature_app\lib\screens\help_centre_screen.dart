import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';

class HelpCentreScreen extends StatefulWidget {
  const HelpCentreScreen({super.key});

  @override
  State<HelpCentreScreen> createState() => _HelpCentreScreenState();
}

class _HelpCentreScreenState extends State<HelpCentreScreen> with SingleTickerProviderStateMixin {
  TabController? _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
  }

  @override
  void dispose() {
    _tabController?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // Return loading indicator if TabController is not initialized yet
    if (_tabController == null) {
      return const Scaffold(
        body: Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('Help Centre'),
        bottom: TabBar(
          controller: _tabController!,
          isScrollable: true,
          tabs: const [
            Tab(icon: Icon(Icons.help_outline), text: 'FAQ'),
            Tab(icon: Icon(Icons.school), text: 'Getting Started'),
            Tab(icon: Icon(Icons.bug_report), text: 'Troubleshooting'),
            Tab(icon: Icon(Icons.contact_support), text: 'Contact'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController!,
        children: [
          _buildFAQTab(),
          _buildGettingStartedTab(),
          _buildTroubleshootingTab(),
          _buildContactTab(),
        ],
      ),
    );
  }

  Widget _buildFAQTab() {
    final faqs = [
      {
        'question': 'How do I identify a plant using the scanner?',
        'answer': 'Go to the Home screen, tap the Scanner tab, and point your camera at the plant. The AI will identify it and provide care information. Note: Plant scanning requires a Premium or Pro subscription.',
      },
      {
        'question': 'What\'s the difference between subscription tiers?',
        'answer': 'Free: Basic marketplace and encyclopedia access.\nPremium (\$4.99/month): Unlimited AI assistant, plant scanning, full encyclopedia.\nPro (\$9.99/month): Everything in Premium plus disease detection and personalized care plans.',
      },
      {
        'question': 'How do I add plants to my favorites?',
        'answer': 'In the Plant Encyclopedia, tap the heart icon on any plant card to add it to your favorites. You can view all favorites in the Favorites tab.',
      },
      {
        'question': 'Can I use the AI assistant for free?',
        'answer': 'Yes, but with limited queries per day. Premium and Pro subscribers get unlimited access to the AI assistant.',
      },
      {
        'question': 'How do I purchase products from the marketplace?',
        'answer': 'Browse products in the Marketplace, tap on any product to view details, then follow the purchase process. We partner with trusted sellers to ensure quality.',
      },
      {
        'question': 'Is my personal data secure?',
        'answer': 'Yes, we use industry-standard encryption to protect your data. We never share your personal information with third parties without your consent.',
      },
    ];

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: faqs.length,
      itemBuilder: (context, index) {
        final faq = faqs[index];
        return Card(
          margin: const EdgeInsets.only(bottom: 12),
          child: ExpansionTile(
            leading: const FaIcon(
              FontAwesomeIcons.circleQuestion,
              color: Color(0xFF22c55e),
              size: 20,
            ),
            title: Text(
              faq['question']!,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            children: [
              Padding(
                padding: const EdgeInsets.all(16),
                child: Text(
                  faq['answer']!,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    height: 1.5,
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildGettingStartedTab() {
    final steps = [
      {
        'title': '1. Create Your Account',
        'description': 'Sign up to save your favorites, access the AI assistant, and track your plant journey.',
        'icon': FontAwesomeIcons.userPlus,
      },
      {
        'title': '2. Explore the Marketplace',
        'description': 'Browse natural remedies, herbs, and plant-based products from trusted sellers.',
        'icon': FontAwesomeIcons.store,
      },
      {
        'title': '3. Learn About Plants',
        'description': 'Use the Plant Encyclopedia to discover healing properties and care instructions.',
        'icon': FontAwesomeIcons.book,
      },
      {
        'title': '4. Try Plant Scanning',
        'description': 'Upgrade to Premium to identify plants instantly using your camera.',
        'icon': FontAwesomeIcons.camera,
      },
      {
        'title': '5. Ask the AI Assistant',
        'description': 'Get personalized advice about plants, remedies, and natural healing.',
        'icon': FontAwesomeIcons.robot,
      },
      {
        'title': '6. Build Your Collection',
        'description': 'Save favorite plants and products to create your personal natural remedy collection.',
        'icon': FontAwesomeIcons.heart,
      },
    ];

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: steps.length,
      itemBuilder: (context, index) {
        final step = steps[index];
        return Card(
          margin: const EdgeInsets.only(bottom: 16),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: const Color(0xFF22c55e).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: FaIcon(
                    step['icon'] as IconData,
                    color: const Color(0xFF22c55e),
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        step['title']! as String,
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        step['description']! as String,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          height: 1.4,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildTroubleshootingTab() {
    final issues = [
      {
        'problem': 'Plant scanner not working',
        'solution': 'Ensure you have a Premium subscription, good lighting, and camera permissions enabled. Try cleaning your camera lens.',
        'icon': FontAwesomeIcons.camera,
      },
      {
        'problem': 'AI assistant not responding',
        'solution': 'Check your internet connection. Free users have daily limits - consider upgrading for unlimited access.',
        'icon': FontAwesomeIcons.robot,
      },
      {
        'problem': 'Images not loading',
        'solution': 'Check your internet connection. Try clearing the app cache in Settings > Storage.',
        'icon': FontAwesomeIcons.image,
      },
      {
        'problem': 'App crashes or freezes',
        'solution': 'Force close and restart the app. If issues persist, try restarting your device or reinstalling the app.',
        'icon': FontAwesomeIcons.triangleExclamation,
      },
      {
        'problem': 'Can\'t sign in',
        'solution': 'Check your email and password. Use "Forgot Password" if needed. Ensure you have an internet connection.',
        'icon': FontAwesomeIcons.userLock,
      },
      {
        'problem': 'Subscription issues',
        'solution': 'Check your payment method and subscription status in Settings. Contact support if charges appear incorrect.',
        'icon': FontAwesomeIcons.creditCard,
      },
    ];

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: issues.length,
      itemBuilder: (context, index) {
        final issue = issues[index];
        return Card(
          margin: const EdgeInsets.only(bottom: 16),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    FaIcon(
                      issue['icon'] as IconData,
                      color: Colors.orange,
                      size: 20,
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        issue['problem']! as String,
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: Colors.orange[700],
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                Text(
                  issue['solution']! as String,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    height: 1.4,
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildContactTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Get in Touch',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'We\'re here to help! Choose the best way to reach us.',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 24),
          
          _buildContactOption(
            icon: FontAwesomeIcons.envelope,
            title: 'Email Support',
            subtitle: '<EMAIL>',
            description: 'Get help within 24 hours',
            onTap: () => _showEmailDialog(),
          ),
          
          const SizedBox(height: 16),
          
          _buildContactOption(
            icon: FontAwesomeIcons.robot,
            title: 'AI Live Chat',
            subtitle: 'Available 24/7',
            description: 'Instant AI-powered assistance',
            onTap: () => _showAIChatDialog(),
          ),
          
          const SizedBox(height: 32),
          
          Text(
            'Community',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          
          _buildContactOption(
            icon: FontAwesomeIcons.users,
            title: 'Community Forum',
            subtitle: 'Connect with other plant lovers',
            description: 'Share tips and get advice',
            onTap: () => _showForumDialog(),
          ),
          
          const SizedBox(height: 16),
          
          _buildContactOption(
            icon: FontAwesomeIcons.instagram,
            title: 'Follow Us',
            subtitle: '@naturesplace',
            description: 'Latest updates and plant tips',
            onTap: () => _showSocialDialog(),
          ),
        ],
      ),
    );
  }

  Widget _buildContactOption({
    required IconData icon,
    required String title,
    required String subtitle,
    required String description,
    required VoidCallback onTap,
  }) {
    return Card(
      child: ListTile(
        leading: Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: const Color(0xFF22c55e).withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: FaIcon(
            icon,
            color: const Color(0xFF22c55e),
            size: 20,
          ),
        ),
        title: Text(
          title,
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(subtitle),
            const SizedBox(height: 4),
            Text(
              description,
              style: TextStyle(
                color: Colors.grey[600],
                fontSize: 12,
              ),
            ),
          ],
        ),
        trailing: const Icon(Icons.chevron_right),
        onTap: onTap,
      ),
    );
  }

  void _showEmailDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Email Support'),
        content: const Text('You can reach <NAME_EMAIL>\n\nWe typically respond within 24 hours.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              // TODO: Open email app
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Opening email app...')),
              );
            },
            child: const Text('Send Email'),
          ),
        ],
      ),
    );
  }

  void _showAIChatDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            FaIcon(FontAwesomeIcons.robot, color: Color(0xFF22c55e), size: 20),
            SizedBox(width: 8),
            Text('AI Live Chat'),
          ],
        ),
        content: const Text('Connect with our AI assistant for instant help with:\n\n• Plant identification questions\n• Natural remedy advice\n• App navigation support\n• General plant care guidance\n\nAvailable 24/7 with intelligent responses powered by our plant knowledge database.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              // Navigate to AI Assistant screen
              Navigator.of(context).pushNamed('/ai-assistant');
            },
            child: const Text('Start AI Chat'),
          ),
        ],
      ),
    );
  }

  void _showForumDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Community Forum'),
        content: const Text('Join our community forum to connect with other plant enthusiasts, share tips, and get advice.\n\nComing soon!'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _showSocialDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Follow Us'),
        content: const Text('Stay updated with the latest plant tips, product updates, and community highlights.\n\nFollow @naturesplace on Instagram and other social platforms.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Opening social media...')),
              );
            },
            child: const Text('Follow'),
          ),
        ],
      ),
    );
  }
}
