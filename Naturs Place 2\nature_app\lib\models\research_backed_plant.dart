/// Enhanced Plant Model with Comprehensive Research-Backed Information
class ResearchBackedPlant {
  final String id;
  final String name;
  final String scientificName;
  final String category;
  final String description;
  final String imageUrl;
  final List<String> benefits;
  final double rating;
  final int reviewCount;
  final List<String> commonNames;
  final String origin;
  final String habitat;
  final List<String> activeCompounds;
  final List<String> uses;
  final List<String> preparationMethods;
  final List<String> precautions;
  final String dosage;
  final List<String> interactions;
  final String harvestingSeason;
  final String partUsed;
  final String traditionalUse;
  final String modernResearch;
  final String cultivation;
  final String family;
  final String energetics;
  final String taste;
  final List<String> contraindications;
  final String conservationStatus;
  final bool isEndangered;
  
  // Enhanced Research-Backed Fields
  final ResearchData researchData;
  final List<ClinicalStudy> clinicalStudies;
  final List<ScientificPublication> publications;
  final PharmacologyData pharmacology;
  final SafetyProfile safetyProfile;
  final QualityStandards qualityStandards;
  final SustainabilityInfo sustainability;
  final NutritionalProfile? nutritionalProfile; // For fruits and edible plants
  final CultivationGuide cultivationGuide;
  final RegulationInfo regulationInfo;

  ResearchBackedPlant({
    required this.id,
    required this.name,
    required this.scientificName,
    required this.category,
    required this.description,
    required this.imageUrl,
    required this.benefits,
    required this.rating,
    required this.reviewCount,
    required this.commonNames,
    required this.origin,
    required this.habitat,
    required this.activeCompounds,
    required this.uses,
    required this.preparationMethods,
    required this.precautions,
    required this.dosage,
    required this.interactions,
    required this.harvestingSeason,
    required this.partUsed,
    required this.traditionalUse,
    required this.modernResearch,
    required this.cultivation,
    required this.family,
    required this.energetics,
    required this.taste,
    required this.contraindications,
    required this.conservationStatus,
    this.isEndangered = false,
    required this.researchData,
    required this.clinicalStudies,
    required this.publications,
    required this.pharmacology,
    required this.safetyProfile,
    required this.qualityStandards,
    required this.sustainability,
    this.nutritionalProfile,
    required this.cultivationGuide,
    required this.regulationInfo,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'scientificName': scientificName,
      'category': category,
      'description': description,
      'imageUrl': imageUrl,
      'benefits': benefits,
      'rating': rating,
      'reviewCount': reviewCount,
      'commonNames': commonNames,
      'origin': origin,
      'habitat': habitat,
      'activeCompounds': activeCompounds,
      'uses': uses,
      'preparationMethods': preparationMethods,
      'precautions': precautions,
      'dosage': dosage,
      'interactions': interactions,
      'harvestingSeason': harvestingSeason,
      'partUsed': partUsed,
      'traditionalUse': traditionalUse,
      'modernResearch': modernResearch,
      'cultivation': cultivation,
      'family': family,
      'energetics': energetics,
      'taste': taste,
      'contraindications': contraindications,
      'conservationStatus': conservationStatus,
      'isEndangered': isEndangered,
      'researchData': researchData.toJson(),
      'clinicalStudies': clinicalStudies.map((s) => s.toJson()).toList(),
      'publications': publications.map((p) => p.toJson()).toList(),
      'pharmacology': pharmacology.toJson(),
      'safetyProfile': safetyProfile.toJson(),
      'qualityStandards': qualityStandards.toJson(),
      'sustainability': sustainability.toJson(),
      'nutritionalProfile': nutritionalProfile?.toJson(),
      'cultivationGuide': cultivationGuide.toJson(),
      'regulationInfo': regulationInfo.toJson(),
    };
  }

  factory ResearchBackedPlant.fromJson(Map<String, dynamic> json) {
    return ResearchBackedPlant(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      scientificName: json['scientificName'] ?? '',
      category: json['category'] ?? '',
      description: json['description'] ?? '',
      imageUrl: json['imageUrl'] ?? '',
      benefits: List<String>.from(json['benefits'] ?? []),
      rating: (json['rating'] ?? 0.0).toDouble(),
      reviewCount: json['reviewCount'] ?? 0,
      commonNames: List<String>.from(json['commonNames'] ?? []),
      origin: json['origin'] ?? '',
      habitat: json['habitat'] ?? '',
      activeCompounds: List<String>.from(json['activeCompounds'] ?? []),
      uses: List<String>.from(json['uses'] ?? []),
      preparationMethods: List<String>.from(json['preparationMethods'] ?? []),
      precautions: List<String>.from(json['precautions'] ?? []),
      dosage: json['dosage'] ?? '',
      interactions: List<String>.from(json['interactions'] ?? []),
      harvestingSeason: json['harvestingSeason'] ?? '',
      partUsed: json['partUsed'] ?? '',
      traditionalUse: json['traditionalUse'] ?? '',
      modernResearch: json['modernResearch'] ?? '',
      cultivation: json['cultivation'] ?? '',
      family: json['family'] ?? '',
      energetics: json['energetics'] ?? '',
      taste: json['taste'] ?? '',
      contraindications: List<String>.from(json['contraindications'] ?? []),
      conservationStatus: json['conservationStatus'] ?? '',
      isEndangered: json['isEndangered'] ?? false,
      researchData: ResearchData.fromJson(json['researchData'] ?? {}),
      clinicalStudies: (json['clinicalStudies'] as List? ?? [])
          .map((s) => ClinicalStudy.fromJson(s))
          .toList(),
      publications: (json['publications'] as List? ?? [])
          .map((p) => ScientificPublication.fromJson(p))
          .toList(),
      pharmacology: PharmacologyData.fromJson(json['pharmacology'] ?? {}),
      safetyProfile: SafetyProfile.fromJson(json['safetyProfile'] ?? {}),
      qualityStandards: QualityStandards.fromJson(json['qualityStandards'] ?? {}),
      sustainability: SustainabilityInfo.fromJson(json['sustainability'] ?? {}),
      nutritionalProfile: json['nutritionalProfile'] != null
          ? NutritionalProfile.fromJson(json['nutritionalProfile'])
          : null,
      cultivationGuide: CultivationGuide.fromJson(json['cultivationGuide'] ?? {}),
      regulationInfo: RegulationInfo.fromJson(json['regulationInfo'] ?? {}),
    );
  }
}

/// Research Data with Evidence-Based Information
class ResearchData {
  final String evidenceLevel; // A, B, C, D based on research quality
  final int totalStudies;
  final int humanTrials;
  final int animalStudies;
  final int invitroStudies;
  final List<String> primaryMechanisms;
  final List<String> provenBenefits;
  final List<String> potentialBenefits;
  final String lastUpdated;
  final List<String> researchInstitutions;
  final double efficacyScore; // 0-10 based on research evidence

  ResearchData({
    required this.evidenceLevel,
    required this.totalStudies,
    required this.humanTrials,
    required this.animalStudies,
    required this.invitroStudies,
    required this.primaryMechanisms,
    required this.provenBenefits,
    required this.potentialBenefits,
    required this.lastUpdated,
    required this.researchInstitutions,
    required this.efficacyScore,
  });

  Map<String, dynamic> toJson() {
    return {
      'evidenceLevel': evidenceLevel,
      'totalStudies': totalStudies,
      'humanTrials': humanTrials,
      'animalStudies': animalStudies,
      'invitroStudies': invitroStudies,
      'primaryMechanisms': primaryMechanisms,
      'provenBenefits': provenBenefits,
      'potentialBenefits': potentialBenefits,
      'lastUpdated': lastUpdated,
      'researchInstitutions': researchInstitutions,
      'efficacyScore': efficacyScore,
    };
  }

  factory ResearchData.fromJson(Map<String, dynamic> json) {
    return ResearchData(
      evidenceLevel: json['evidenceLevel'] ?? 'D',
      totalStudies: json['totalStudies'] ?? 0,
      humanTrials: json['humanTrials'] ?? 0,
      animalStudies: json['animalStudies'] ?? 0,
      invitroStudies: json['invitroStudies'] ?? 0,
      primaryMechanisms: List<String>.from(json['primaryMechanisms'] ?? []),
      provenBenefits: List<String>.from(json['provenBenefits'] ?? []),
      potentialBenefits: List<String>.from(json['potentialBenefits'] ?? []),
      lastUpdated: json['lastUpdated'] ?? '',
      researchInstitutions: List<String>.from(json['researchInstitutions'] ?? []),
      efficacyScore: (json['efficacyScore'] ?? 0.0).toDouble(),
    );
  }
}

/// Clinical Study Information
class ClinicalStudy {
  final String title;
  final String authors;
  final String journal;
  final String year;
  final String studyType; // RCT, Observational, Meta-analysis, etc.
  final int participantCount;
  final String duration;
  final String dosage;
  final String primaryOutcome;
  final String results;
  final String conclusion;
  final String pubmedId;
  final String doi;
  final double qualityScore; // 0-10 based on study quality

  ClinicalStudy({
    required this.title,
    required this.authors,
    required this.journal,
    required this.year,
    required this.studyType,
    required this.participantCount,
    required this.duration,
    required this.dosage,
    required this.primaryOutcome,
    required this.results,
    required this.conclusion,
    required this.pubmedId,
    required this.doi,
    required this.qualityScore,
  });

  Map<String, dynamic> toJson() {
    return {
      'title': title,
      'authors': authors,
      'journal': journal,
      'year': year,
      'studyType': studyType,
      'participantCount': participantCount,
      'duration': duration,
      'dosage': dosage,
      'primaryOutcome': primaryOutcome,
      'results': results,
      'conclusion': conclusion,
      'pubmedId': pubmedId,
      'doi': doi,
      'qualityScore': qualityScore,
    };
  }

  factory ClinicalStudy.fromJson(Map<String, dynamic> json) {
    return ClinicalStudy(
      title: json['title'] ?? '',
      authors: json['authors'] ?? '',
      journal: json['journal'] ?? '',
      year: json['year'] ?? '',
      studyType: json['studyType'] ?? '',
      participantCount: json['participantCount'] ?? 0,
      duration: json['duration'] ?? '',
      dosage: json['dosage'] ?? '',
      primaryOutcome: json['primaryOutcome'] ?? '',
      results: json['results'] ?? '',
      conclusion: json['conclusion'] ?? '',
      pubmedId: json['pubmedId'] ?? '',
      doi: json['doi'] ?? '',
      qualityScore: (json['qualityScore'] ?? 0.0).toDouble(),
    );
  }
}

/// Scientific Publication Information
class ScientificPublication {
  final String title;
  final String authors;
  final String journal;
  final String year;
  final String abstract;
  final String keywords;
  final String pubmedId;
  final String doi;
  final int citationCount;
  final String impactFactor;

  ScientificPublication({
    required this.title,
    required this.authors,
    required this.journal,
    required this.year,
    required this.abstract,
    required this.keywords,
    required this.pubmedId,
    required this.doi,
    required this.citationCount,
    required this.impactFactor,
  });

  Map<String, dynamic> toJson() {
    return {
      'title': title,
      'authors': authors,
      'journal': journal,
      'year': year,
      'abstract': abstract,
      'keywords': keywords,
      'pubmedId': pubmedId,
      'doi': doi,
      'citationCount': citationCount,
      'impactFactor': impactFactor,
    };
  }

  factory ScientificPublication.fromJson(Map<String, dynamic> json) {
    return ScientificPublication(
      title: json['title'] ?? '',
      authors: json['authors'] ?? '',
      journal: json['journal'] ?? '',
      year: json['year'] ?? '',
      abstract: json['abstract'] ?? '',
      keywords: json['keywords'] ?? '',
      pubmedId: json['pubmedId'] ?? '',
      doi: json['doi'] ?? '',
      citationCount: json['citationCount'] ?? 0,
      impactFactor: json['impactFactor'] ?? '',
    );
  }
}

/// Pharmacology Data
class PharmacologyData {
  final List<String> mechanismsOfAction;
  final String bioavailability;
  final String metabolism;
  final String halfLife;
  final String excretion;
  final List<String> targetReceptors;
  final List<String> enzymeInteractions;
  final String therapeuticWindow;
  final List<String> pharmacokinetics;

  PharmacologyData({
    required this.mechanismsOfAction,
    required this.bioavailability,
    required this.metabolism,
    required this.halfLife,
    required this.excretion,
    required this.targetReceptors,
    required this.enzymeInteractions,
    required this.therapeuticWindow,
    required this.pharmacokinetics,
  });

  Map<String, dynamic> toJson() {
    return {
      'mechanismsOfAction': mechanismsOfAction,
      'bioavailability': bioavailability,
      'metabolism': metabolism,
      'halfLife': halfLife,
      'excretion': excretion,
      'targetReceptors': targetReceptors,
      'enzymeInteractions': enzymeInteractions,
      'therapeuticWindow': therapeuticWindow,
      'pharmacokinetics': pharmacokinetics,
    };
  }

  factory PharmacologyData.fromJson(Map<String, dynamic> json) {
    return PharmacologyData(
      mechanismsOfAction: List<String>.from(json['mechanismsOfAction'] ?? []),
      bioavailability: json['bioavailability'] ?? '',
      metabolism: json['metabolism'] ?? '',
      halfLife: json['halfLife'] ?? '',
      excretion: json['excretion'] ?? '',
      targetReceptors: List<String>.from(json['targetReceptors'] ?? []),
      enzymeInteractions: List<String>.from(json['enzymeInteractions'] ?? []),
      therapeuticWindow: json['therapeuticWindow'] ?? '',
      pharmacokinetics: List<String>.from(json['pharmacokinetics'] ?? []),
    );
  }
}

/// Safety Profile
class SafetyProfile {
  final String safetyRating; // A, B, C, D, X
  final List<String> adverseEffects;
  final List<String> seriousAdverseEvents;
  final List<String> drugInteractions;
  final List<String> populationWarnings;
  final String pregnancyCategory;
  final String lactationSafety;
  final String pediatricSafety;
  final String geriatricConsiderations;
  final List<String> contraindications;
  final String overdoseInformation;

  SafetyProfile({
    required this.safetyRating,
    required this.adverseEffects,
    required this.seriousAdverseEvents,
    required this.drugInteractions,
    required this.populationWarnings,
    required this.pregnancyCategory,
    required this.lactationSafety,
    required this.pediatricSafety,
    required this.geriatricConsiderations,
    required this.contraindications,
    required this.overdoseInformation,
  });

  Map<String, dynamic> toJson() {
    return {
      'safetyRating': safetyRating,
      'adverseEffects': adverseEffects,
      'seriousAdverseEvents': seriousAdverseEvents,
      'drugInteractions': drugInteractions,
      'populationWarnings': populationWarnings,
      'pregnancyCategory': pregnancyCategory,
      'lactationSafety': lactationSafety,
      'pediatricSafety': pediatricSafety,
      'geriatricConsiderations': geriatricConsiderations,
      'contraindications': contraindications,
      'overdoseInformation': overdoseInformation,
    };
  }

  factory SafetyProfile.fromJson(Map<String, dynamic> json) {
    return SafetyProfile(
      safetyRating: json['safetyRating'] ?? 'D',
      adverseEffects: List<String>.from(json['adverseEffects'] ?? []),
      seriousAdverseEvents: List<String>.from(json['seriousAdverseEvents'] ?? []),
      drugInteractions: List<String>.from(json['drugInteractions'] ?? []),
      populationWarnings: List<String>.from(json['populationWarnings'] ?? []),
      pregnancyCategory: json['pregnancyCategory'] ?? '',
      lactationSafety: json['lactationSafety'] ?? '',
      pediatricSafety: json['pediatricSafety'] ?? '',
      geriatricConsiderations: json['geriatricConsiderations'] ?? '',
      contraindications: List<String>.from(json['contraindications'] ?? []),
      overdoseInformation: json['overdoseInformation'] ?? '',
    );
  }
}

/// Quality Standards
class QualityStandards {
  final List<String> certifications;
  final String standardizationMethod;
  final String activeCompoundRange;
  final String testingMethods;
  final String purityStandards;
  final String contaminantLimits;
  final String shelfLife;
  final String storageConditions;
  final List<String> qualityMarkers;

  QualityStandards({
    required this.certifications,
    required this.standardizationMethod,
    required this.activeCompoundRange,
    required this.testingMethods,
    required this.purityStandards,
    required this.contaminantLimits,
    required this.shelfLife,
    required this.storageConditions,
    required this.qualityMarkers,
  });

  Map<String, dynamic> toJson() {
    return {
      'certifications': certifications,
      'standardizationMethod': standardizationMethod,
      'activeCompoundRange': activeCompoundRange,
      'testingMethods': testingMethods,
      'purityStandards': purityStandards,
      'contaminantLimits': contaminantLimits,
      'shelfLife': shelfLife,
      'storageConditions': storageConditions,
      'qualityMarkers': qualityMarkers,
    };
  }

  factory QualityStandards.fromJson(Map<String, dynamic> json) {
    return QualityStandards(
      certifications: List<String>.from(json['certifications'] ?? []),
      standardizationMethod: json['standardizationMethod'] ?? '',
      activeCompoundRange: json['activeCompoundRange'] ?? '',
      testingMethods: json['testingMethods'] ?? '',
      purityStandards: json['purityStandards'] ?? '',
      contaminantLimits: json['contaminantLimits'] ?? '',
      shelfLife: json['shelfLife'] ?? '',
      storageConditions: json['storageConditions'] ?? '',
      qualityMarkers: List<String>.from(json['qualityMarkers'] ?? []),
    );
  }
}

/// Sustainability Information
class SustainabilityInfo {
  final String conservationStatus;
  final List<String> threats;
  final List<String> conservationEfforts;
  final String sustainableHarvesting;
  final String cultivationImpact;
  final String carbonFootprint;
  final List<String> certifications;
  final String fairTrade;
  final String organicStatus;

  SustainabilityInfo({
    required this.conservationStatus,
    required this.threats,
    required this.conservationEfforts,
    required this.sustainableHarvesting,
    required this.cultivationImpact,
    required this.carbonFootprint,
    required this.certifications,
    required this.fairTrade,
    required this.organicStatus,
  });

  Map<String, dynamic> toJson() {
    return {
      'conservationStatus': conservationStatus,
      'threats': threats,
      'conservationEfforts': conservationEfforts,
      'sustainableHarvesting': sustainableHarvesting,
      'cultivationImpact': cultivationImpact,
      'carbonFootprint': carbonFootprint,
      'certifications': certifications,
      'fairTrade': fairTrade,
      'organicStatus': organicStatus,
    };
  }

  factory SustainabilityInfo.fromJson(Map<String, dynamic> json) {
    return SustainabilityInfo(
      conservationStatus: json['conservationStatus'] ?? '',
      threats: List<String>.from(json['threats'] ?? []),
      conservationEfforts: List<String>.from(json['conservationEfforts'] ?? []),
      sustainableHarvesting: json['sustainableHarvesting'] ?? '',
      cultivationImpact: json['cultivationImpact'] ?? '',
      carbonFootprint: json['carbonFootprint'] ?? '',
      certifications: List<String>.from(json['certifications'] ?? []),
      fairTrade: json['fairTrade'] ?? '',
      organicStatus: json['organicStatus'] ?? '',
    );
  }
}

/// Nutritional Profile (for fruits and edible plants)
class NutritionalProfile {
  final Map<String, double> macronutrients; // carbs, protein, fat, fiber
  final Map<String, double> vitamins; // per 100g
  final Map<String, double> minerals; // per 100g
  final Map<String, double> antioxidants; // ORAC values, etc.
  final double calories; // per 100g
  final double glycemicIndex;
  final List<String> phytonutrients;
  final String servingSize;

  NutritionalProfile({
    required this.macronutrients,
    required this.vitamins,
    required this.minerals,
    required this.antioxidants,
    required this.calories,
    required this.glycemicIndex,
    required this.phytonutrients,
    required this.servingSize,
  });

  Map<String, dynamic> toJson() {
    return {
      'macronutrients': macronutrients,
      'vitamins': vitamins,
      'minerals': minerals,
      'antioxidants': antioxidants,
      'calories': calories,
      'glycemicIndex': glycemicIndex,
      'phytonutrients': phytonutrients,
      'servingSize': servingSize,
    };
  }

  factory NutritionalProfile.fromJson(Map<String, dynamic> json) {
    return NutritionalProfile(
      macronutrients: Map<String, double>.from(json['macronutrients'] ?? {}),
      vitamins: Map<String, double>.from(json['vitamins'] ?? {}),
      minerals: Map<String, double>.from(json['minerals'] ?? {}),
      antioxidants: Map<String, double>.from(json['antioxidants'] ?? {}),
      calories: (json['calories'] ?? 0.0).toDouble(),
      glycemicIndex: (json['glycemicIndex'] ?? 0.0).toDouble(),
      phytonutrients: List<String>.from(json['phytonutrients'] ?? []),
      servingSize: json['servingSize'] ?? '',
    );
  }
}

/// Cultivation Guide
class CultivationGuide {
  final String climate;
  final String soilRequirements;
  final String waterNeeds;
  final String sunlightRequirements;
  final String plantingInstructions;
  final String careInstructions;
  final String harvestingGuide;
  final String commonPests;
  final String diseases;
  final String companionPlants;
  final String propagationMethods;
  final String seasonalCare;

  CultivationGuide({
    required this.climate,
    required this.soilRequirements,
    required this.waterNeeds,
    required this.sunlightRequirements,
    required this.plantingInstructions,
    required this.careInstructions,
    required this.harvestingGuide,
    required this.commonPests,
    required this.diseases,
    required this.companionPlants,
    required this.propagationMethods,
    required this.seasonalCare,
  });

  Map<String, dynamic> toJson() {
    return {
      'climate': climate,
      'soilRequirements': soilRequirements,
      'waterNeeds': waterNeeds,
      'sunlightRequirements': sunlightRequirements,
      'plantingInstructions': plantingInstructions,
      'careInstructions': careInstructions,
      'harvestingGuide': harvestingGuide,
      'commonPests': commonPests,
      'diseases': diseases,
      'companionPlants': companionPlants,
      'propagationMethods': propagationMethods,
      'seasonalCare': seasonalCare,
    };
  }

  factory CultivationGuide.fromJson(Map<String, dynamic> json) {
    return CultivationGuide(
      climate: json['climate'] ?? '',
      soilRequirements: json['soilRequirements'] ?? '',
      waterNeeds: json['waterNeeds'] ?? '',
      sunlightRequirements: json['sunlightRequirements'] ?? '',
      plantingInstructions: json['plantingInstructions'] ?? '',
      careInstructions: json['careInstructions'] ?? '',
      harvestingGuide: json['harvestingGuide'] ?? '',
      commonPests: json['commonPests'] ?? '',
      diseases: json['diseases'] ?? '',
      companionPlants: json['companionPlants'] ?? '',
      propagationMethods: json['propagationMethods'] ?? '',
      seasonalCare: json['seasonalCare'] ?? '',
    );
  }
}

/// Regulation Information
class RegulationInfo {
  final String fdaStatus;
  final String emaStatus;
  final String whoStatus;
  final List<String> approvedUses;
  final List<String> restrictedUses;
  final String prescriptionStatus;
  final List<String> regulatoryWarnings;
  final String legalStatus;
  final Map<String, String> countryRegulations;

  RegulationInfo({
    required this.fdaStatus,
    required this.emaStatus,
    required this.whoStatus,
    required this.approvedUses,
    required this.restrictedUses,
    required this.prescriptionStatus,
    required this.regulatoryWarnings,
    required this.legalStatus,
    required this.countryRegulations,
  });

  Map<String, dynamic> toJson() {
    return {
      'fdaStatus': fdaStatus,
      'emaStatus': emaStatus,
      'whoStatus': whoStatus,
      'approvedUses': approvedUses,
      'restrictedUses': restrictedUses,
      'prescriptionStatus': prescriptionStatus,
      'regulatoryWarnings': regulatoryWarnings,
      'legalStatus': legalStatus,
      'countryRegulations': countryRegulations,
    };
  }

  factory RegulationInfo.fromJson(Map<String, dynamic> json) {
    return RegulationInfo(
      fdaStatus: json['fdaStatus'] ?? '',
      emaStatus: json['emaStatus'] ?? '',
      whoStatus: json['whoStatus'] ?? '',
      approvedUses: List<String>.from(json['approvedUses'] ?? []),
      restrictedUses: List<String>.from(json['restrictedUses'] ?? []),
      prescriptionStatus: json['prescriptionStatus'] ?? '',
      regulatoryWarnings: List<String>.from(json['regulatoryWarnings'] ?? []),
      legalStatus: json['legalStatus'] ?? '',
      countryRegulations: Map<String, String>.from(json['countryRegulations'] ?? {}),
    );
  }
}
