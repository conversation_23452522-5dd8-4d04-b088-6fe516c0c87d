import 'package:flutter/foundation.dart';
import 'package:sqflite/sqflite.dart' hide Transaction;
import 'package:path/path.dart';
import '../models/commission_models.dart';
import '../models/vendor_models.dart';

/// Database service for managing local SQLite and PostgreSQL connections
class DatabaseService {
  static final DatabaseService _instance = DatabaseService._internal();
  factory DatabaseService() => _instance;
  DatabaseService._internal();

  Database? _database;
  bool _isInitialized = false;

  /// Initialize the database
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      await _initializeLocalDatabase();
      _isInitialized = true;
      debugPrint('✅ Database Service initialized');
    } catch (e) {
      debugPrint('❌ Database initialization failed: $e');
      rethrow;
    }
  }

  /// Initialize local SQLite database
  Future<void> _initializeLocalDatabase() async {
    final databasesPath = await getDatabasesPath();
    final path = join(databasesPath, 'natures_place.db');

    _database = await openDatabase(
      path,
      version: 1,
      onCreate: _createTables,
      onUpgrade: _upgradeDatabase,
    );
  }

  /// Create database tables
  Future<void> _createTables(Database db, int version) async {
    // Create transactions table
    await db.execute('''
      CREATE TABLE transactions (
        id TEXT PRIMARY KEY,
        order_id TEXT NOT NULL,
        customer_id TEXT NOT NULL,
        vendor_id TEXT NOT NULL,
        affiliate_id TEXT,
        product_id TEXT NOT NULL,
        product_name TEXT NOT NULL,
        category TEXT NOT NULL,
        product_price REAL NOT NULL,
        quantity INTEGER NOT NULL,
        subtotal REAL NOT NULL,
        tax REAL NOT NULL,
        shipping REAL NOT NULL,
        total REAL NOT NULL,
        discount REAL DEFAULT 0.0,
        coupon_code TEXT,
        status TEXT NOT NULL,
        created_at TEXT NOT NULL,
        completed_at TEXT,
        cancelled_at TEXT,
        cancellation_reason TEXT,
        metadata TEXT NOT NULL
      )
    ''');

    // Create commissions table
    await db.execute('''
      CREATE TABLE commissions (
        id TEXT PRIMARY KEY,
        transaction_id TEXT NOT NULL,
        partner_id TEXT NOT NULL,
        partner_type TEXT NOT NULL,
        category TEXT NOT NULL,
        sale_amount REAL NOT NULL,
        commission_rate REAL NOT NULL,
        commission_amount REAL NOT NULL,
        bonus_amount REAL DEFAULT 0.0,
        total_amount REAL NOT NULL,
        app_revenue REAL NOT NULL,
        status TEXT NOT NULL,
        created_at TEXT NOT NULL,
        approved_at TEXT,
        paid_at TEXT,
        payout_id TEXT,
        notes TEXT,
        metadata TEXT NOT NULL,
        FOREIGN KEY (transaction_id) REFERENCES transactions (id)
      )
    ''');

    // Create payouts table
    await db.execute('''
      CREATE TABLE payouts (
        id TEXT PRIMARY KEY,
        partner_id TEXT NOT NULL,
        partner_type TEXT NOT NULL,
        commission_ids TEXT NOT NULL,
        total_amount REAL NOT NULL,
        fees REAL DEFAULT 0.0,
        net_amount REAL NOT NULL,
        status TEXT NOT NULL,
        payment_method TEXT NOT NULL,
        payment_reference TEXT,
        created_at TEXT NOT NULL,
        processed_at TEXT,
        completed_at TEXT,
        failure_reason TEXT,
        payment_details TEXT NOT NULL,
        metadata TEXT NOT NULL
      )
    ''');

    // Create indexes for better performance
    await db.execute('CREATE INDEX idx_transactions_vendor_id ON transactions(vendor_id)');
    await db.execute('CREATE INDEX idx_transactions_affiliate_id ON transactions(affiliate_id)');
    await db.execute('CREATE INDEX idx_transactions_status ON transactions(status)');
    await db.execute('CREATE INDEX idx_commissions_partner_id ON commissions(partner_id)');
    await db.execute('CREATE INDEX idx_commissions_status ON commissions(status)');
    await db.execute('CREATE INDEX idx_payouts_partner_id ON payouts(partner_id)');

    debugPrint('✅ Database tables created successfully');
  }

  /// Upgrade database schema
  Future<void> _upgradeDatabase(Database db, int oldVersion, int newVersion) async {
    debugPrint('🔄 Upgrading database from version $oldVersion to $newVersion');
    // Add migration logic here when needed
  }

  /// Get database instance
  Database get database {
    if (_database == null) {
      throw Exception('Database not initialized. Call initialize() first.');
    }
    return _database!;
  }

  /// Insert a transaction
  Future<void> insertTransaction(Transaction transaction) async {
    await database.insert(
      'transactions',
      _transactionToMap(transaction),
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  /// Insert a commission
  Future<void> insertCommission(Commission commission) async {
    await database.insert(
      'commissions',
      _commissionToMap(commission),
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  /// Insert a payout
  Future<void> insertPayout(Payout payout) async {
    await database.insert(
      'payouts',
      _payoutToMap(payout),
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  /// Get all transactions
  Future<List<Transaction>> getTransactions() async {
    final List<Map<String, dynamic>> maps = await database.query('transactions');
    return List.generate(maps.length, (i) => _mapToTransaction(maps[i]));
  }

  /// Get all commissions
  Future<List<Commission>> getCommissions() async {
    final List<Map<String, dynamic>> maps = await database.query('commissions');
    return List.generate(maps.length, (i) => _mapToCommission(maps[i]));
  }

  /// Get all payouts
  Future<List<Payout>> getPayouts() async {
    final List<Map<String, dynamic>> maps = await database.query('payouts');
    return List.generate(maps.length, (i) => _mapToPayout(maps[i]));
  }

  /// Get commissions by partner
  Future<List<Commission>> getCommissionsByPartner(String partnerId, String partnerType) async {
    final List<Map<String, dynamic>> maps = await database.query(
      'commissions',
      where: 'partner_id = ? AND partner_type = ?',
      whereArgs: [partnerId, partnerType],
    );
    return List.generate(maps.length, (i) => _mapToCommission(maps[i]));
  }

  /// Get pending commissions
  Future<List<Commission>> getPendingCommissions() async {
    final List<Map<String, dynamic>> maps = await database.query(
      'commissions',
      where: 'status = ?',
      whereArgs: ['pending'],
    );
    return List.generate(maps.length, (i) => _mapToCommission(maps[i]));
  }

  /// Update commission status
  Future<void> updateCommissionStatus(String commissionId, CommissionStatus status, {DateTime? paidAt, String? payoutId}) async {
    final Map<String, dynamic> updates = {
      'status': status.name,
    };
    
    if (paidAt != null) {
      updates['paid_at'] = paidAt.toIso8601String();
    }
    
    if (payoutId != null) {
      updates['payout_id'] = payoutId;
    }

    await database.update(
      'commissions',
      updates,
      where: 'id = ?',
      whereArgs: [commissionId],
    );
  }

  /// Convert Transaction to Map
  Map<String, dynamic> _transactionToMap(Transaction transaction) {
    return {
      'id': transaction.id,
      'order_id': transaction.orderId,
      'customer_id': transaction.customerId,
      'vendor_id': transaction.vendorId,
      'affiliate_id': transaction.affiliateId,
      'product_id': transaction.productId,
      'product_name': transaction.productName,
      'category': transaction.category.name,
      'product_price': transaction.productPrice,
      'quantity': transaction.quantity,
      'subtotal': transaction.subtotal,
      'tax': transaction.tax,
      'shipping': transaction.shipping,
      'total': transaction.total,
      'discount': transaction.discount,
      'coupon_code': transaction.couponCode,
      'status': transaction.status.name,
      'created_at': transaction.createdAt.toIso8601String(),
      'completed_at': transaction.completedAt?.toIso8601String(),
      'cancelled_at': transaction.cancelledAt?.toIso8601String(),
      'cancellation_reason': transaction.cancellationReason,
      'metadata': transaction.metadata.toString(),
    };
  }

  /// Convert Map to Transaction
  Transaction _mapToTransaction(Map<String, dynamic> map) {
    return Transaction(
      id: map['id'],
      orderId: map['order_id'],
      customerId: map['customer_id'],
      vendorId: map['vendor_id'],
      affiliateId: map['affiliate_id'],
      productId: map['product_id'],
      productName: map['product_name'],
      category: ProductCategory.values.firstWhere((e) => e.name == map['category']),
      productPrice: map['product_price'],
      quantity: map['quantity'],
      subtotal: map['subtotal'],
      tax: map['tax'],
      shipping: map['shipping'],
      total: map['total'],
      discount: map['discount'] ?? 0.0,
      couponCode: map['coupon_code'],
      status: TransactionStatus.values.firstWhere((e) => e.name == map['status']),
      createdAt: DateTime.parse(map['created_at']),
      completedAt: map['completed_at'] != null ? DateTime.parse(map['completed_at']) : null,
      cancelledAt: map['cancelled_at'] != null ? DateTime.parse(map['cancelled_at']) : null,
      cancellationReason: map['cancellation_reason'],
      metadata: const {},
    );
  }

  /// Convert Commission to Map
  Map<String, dynamic> _commissionToMap(Commission commission) {
    return {
      'id': commission.id,
      'transaction_id': commission.transactionId,
      'partner_id': commission.partnerId,
      'partner_type': commission.partnerType,
      'category': commission.category.name,
      'sale_amount': commission.saleAmount,
      'commission_rate': commission.commissionRate,
      'commission_amount': commission.commissionAmount,
      'bonus_amount': commission.bonusAmount,
      'total_amount': commission.totalAmount,
      'app_revenue': commission.appRevenue,
      'status': commission.status.name,
      'created_at': commission.createdAt.toIso8601String(),
      'approved_at': commission.approvedAt?.toIso8601String(),
      'paid_at': commission.paidAt?.toIso8601String(),
      'payout_id': commission.payoutId,
      'notes': commission.notes,
      'metadata': commission.metadata.toString(),
    };
  }

  /// Convert Map to Commission
  Commission _mapToCommission(Map<String, dynamic> map) {
    return Commission(
      id: map['id'],
      transactionId: map['transaction_id'],
      partnerId: map['partner_id'],
      partnerType: map['partner_type'],
      category: ProductCategory.values.firstWhere((e) => e.name == map['category']),
      saleAmount: map['sale_amount'],
      commissionRate: map['commission_rate'],
      commissionAmount: map['commission_amount'],
      bonusAmount: map['bonus_amount'] ?? 0.0,
      totalAmount: map['total_amount'],
      appRevenue: map['app_revenue'],
      status: CommissionStatus.values.firstWhere((e) => e.name == map['status']),
      createdAt: DateTime.parse(map['created_at']),
      approvedAt: map['approved_at'] != null ? DateTime.parse(map['approved_at']) : null,
      paidAt: map['paid_at'] != null ? DateTime.parse(map['paid_at']) : null,
      payoutId: map['payout_id'],
      notes: map['notes'],
      metadata: const {},
    );
  }

  /// Convert Payout to Map
  Map<String, dynamic> _payoutToMap(Payout payout) {
    return {
      'id': payout.id,
      'partner_id': payout.partnerId,
      'partner_type': payout.partnerType,
      'commission_ids': payout.commissionIds.join(','),
      'total_amount': payout.totalAmount,
      'fees': payout.fees,
      'net_amount': payout.netAmount,
      'status': payout.status.name,
      'payment_method': payout.paymentMethod,
      'payment_reference': payout.paymentReference,
      'created_at': payout.createdAt.toIso8601String(),
      'processed_at': payout.processedAt?.toIso8601String(),
      'completed_at': payout.completedAt?.toIso8601String(),
      'failure_reason': payout.failureReason,
      'payment_details': payout.paymentDetails.toString(),
      'metadata': payout.metadata.toString(),
    };
  }

  /// Convert Map to Payout
  Payout _mapToPayout(Map<String, dynamic> map) {
    return Payout(
      id: map['id'],
      partnerId: map['partner_id'],
      partnerType: map['partner_type'],
      commissionIds: map['commission_ids'].split(','),
      totalAmount: map['total_amount'],
      fees: map['fees'] ?? 0.0,
      netAmount: map['net_amount'],
      status: PayoutStatus.values.firstWhere((e) => e.name == map['status']),
      paymentMethod: map['payment_method'],
      paymentReference: map['payment_reference'],
      createdAt: DateTime.parse(map['created_at']),
      processedAt: map['processed_at'] != null ? DateTime.parse(map['processed_at']) : null,
      completedAt: map['completed_at'] != null ? DateTime.parse(map['completed_at']) : null,
      failureReason: map['failure_reason'],
      paymentDetails: const {},
      metadata: const {},
    );
  }

  /// Close database connection
  Future<void> close() async {
    if (_database != null) {
      await _database!.close();
      _database = null;
      _isInitialized = false;
    }
  }
}
