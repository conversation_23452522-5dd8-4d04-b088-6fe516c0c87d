import 'package:flutter/foundation.dart';
import 'dart:async';
import 'postgres_service.dart';

/// Service for monitoring commission patterns and detecting anomalies
class CommissionMonitoringService {
  static final CommissionMonitoringService _instance = CommissionMonitoringService._internal();
  factory CommissionMonitoringService() => _instance;
  CommissionMonitoringService._internal();

  final PostgreSQLService _postgresService = PostgreSQLService();
  
  Timer? _monitoringTimer;
  bool _isMonitoring = false;

  /// Initialize monitoring service
  Future<void> initialize() async {
    await _postgresService.initialize();
    debugPrint('✅ Commission Monitoring Service initialized');
  }

  /// Start automated monitoring (run every hour)
  void startMonitoring() {
    if (_isMonitoring) return;

    _monitoringTimer = Timer.periodic(const Duration(hours: 1), (timer) {
      _performMonitoringChecks();
    });
    
    _isMonitoring = true;
    debugPrint('🔍 Commission monitoring started (hourly checks)');
  }

  /// Stop monitoring
  void stopMonitoring() {
    _monitoringTimer?.cancel();
    _isMonitoring = false;
    debugPrint('⏹️ Commission monitoring stopped');
  }

  /// Perform comprehensive monitoring checks
  Future<void> _performMonitoringChecks() async {
    debugPrint('🔍 Running commission monitoring checks...');

    try {
      // Check 1: Partner conversion rates
      await _checkPartnerConversionRates();
      
      // Check 2: Commission cancellation patterns
      await _checkCommissionCancellations();
      
      // Check 3: Fraud detection
      await _checkForFraudulentActivity();
      
      // Check 4: Payout readiness
      await _checkPayoutReadiness();
      
      // Check 5: System health
      await _checkSystemHealth();

      debugPrint('✅ Commission monitoring checks completed');
    } catch (e) {
      debugPrint('❌ Commission monitoring failed: $e');
      await _alertAdmin('Commission monitoring system error', e.toString());
    }
  }

  /// Check partner conversion rates (referrals → purchases)
  Future<void> _checkPartnerConversionRates() async {
    try {
      final partners = await _getActivePartners();
      
      for (final partnerId in partners) {
        final stats = await _getPartnerStats(partnerId);
        final conversionRate = stats['conversion_rate'] as double;
        final totalReferrals = stats['total_referrals'] as int;
        
        // Alert if conversion rate is unusually low (< 2%) with significant traffic
        if (conversionRate < 0.02 && totalReferrals > 50) {
          await _alertPartner(
            partnerId,
            'Low Conversion Rate Alert',
            'Your conversion rate is ${(conversionRate * 100).toStringAsFixed(1)}% with $totalReferrals referrals. Consider optimizing your referral strategy.'
          );
        }
        
        // Alert if conversion rate is suspiciously high (> 50%)
        if (conversionRate > 0.50 && totalReferrals > 10) {
          await _alertAdmin(
            'Suspicious Conversion Rate',
            'Partner $partnerId has ${(conversionRate * 100).toStringAsFixed(1)}% conversion rate - possible fraud'
          );
        }
      }
    } catch (e) {
      debugPrint('❌ Failed to check partner conversion rates: $e');
    }
  }

  /// Check commission cancellation patterns
  Future<void> _checkCommissionCancellations() async {
    try {
      final last24Hours = DateTime.now().subtract(const Duration(hours: 24));
      final cancellationStats = await _getCancellationStats(last24Hours);
      
      final totalCommissions = cancellationStats['total'] as int;
      final cancelledCommissions = cancellationStats['cancelled'] as int;
      final cancellationRate = totalCommissions > 0 ? cancelledCommissions / totalCommissions : 0.0;
      
      // Alert if cancellation rate is high (> 20%)
      if (cancellationRate > 0.20 && totalCommissions > 10) {
        await _alertAdmin(
          'High Commission Cancellation Rate',
          'Cancellation rate: ${(cancellationRate * 100).toStringAsFixed(1)}% ($cancelledCommissions/$totalCommissions) in last 24 hours'
        );
      }
      
      debugPrint('📊 Cancellation rate: ${(cancellationRate * 100).toStringAsFixed(1)}% ($cancelledCommissions/$totalCommissions)');
    } catch (e) {
      debugPrint('❌ Failed to check cancellation patterns: $e');
    }
  }

  /// Check for fraudulent activity patterns
  Future<void> _checkForFraudulentActivity() async {
    try {
      // Check 1: Self-referrals (customer = partner)
      final selfReferrals = await _detectSelfReferrals();
      if (selfReferrals.isNotEmpty) {
        await _alertAdmin(
          'Self-Referral Detected',
          'Found ${selfReferrals.length} potential self-referrals: ${selfReferrals.join(', ')}'
        );
      }
      
      // Check 2: Rapid refund patterns
      final rapidRefunds = await _detectRapidRefunds();
      if (rapidRefunds.isNotEmpty) {
        await _alertAdmin(
          'Rapid Refund Pattern',
          'Partners with suspicious refund patterns: ${rapidRefunds.join(', ')}'
        );
      }
      
      // Check 3: Unusual purchase amounts
      final unusualAmounts = await _detectUnusualPurchaseAmounts();
      if (unusualAmounts.isNotEmpty) {
        await _alertAdmin(
          'Unusual Purchase Amounts',
          'Transactions with suspicious amounts: ${unusualAmounts.join(', ')}'
        );
      }
      
    } catch (e) {
      debugPrint('❌ Failed to check for fraudulent activity: $e');
    }
  }

  /// Check payout readiness for 30-day cycles
  Future<void> _checkPayoutReadiness() async {
    try {
      final tomorrow = DateTime.now().add(const Duration(days: 1));
      final partnersDueSoon = await _getPartnersDueForPayout(tomorrow);
      
      if (partnersDueSoon.isNotEmpty) {
        double totalPayoutAmount = 0.0;
        for (final partner in partnersDueSoon) {
          final pendingAmount = await _calculatePendingCommissions(partner['partner_id']);
          totalPayoutAmount += pendingAmount;
        }
        
        await _alertAdmin(
          'Upcoming Payouts',
          '${partnersDueSoon.length} partners due for payout tomorrow. Total: \$${totalPayoutAmount.toStringAsFixed(2)}'
        );
      }
    } catch (e) {
      debugPrint('❌ Failed to check payout readiness: $e');
    }
  }

  /// Check overall system health
  Future<void> _checkSystemHealth() async {
    try {
      // Check database connection
      final dbHealthy = await _postgresService.testConnection();
      if (!dbHealthy) {
        await _alertAdmin('Database Connection Issue', 'PostgreSQL connection failed');
      }
      
      // Check commission processing delays
      final processingDelays = await _checkProcessingDelays();
      if (processingDelays > 100) {
        await _alertAdmin(
          'Commission Processing Delays',
          '$processingDelays transactions pending commission processing'
        );
      }
      
      debugPrint('💚 System health check completed');
    } catch (e) {
      debugPrint('❌ System health check failed: $e');
    }
  }

  /// Get partner performance statistics
  Future<Map<String, dynamic>> getPartnerPerformanceReport(String partnerId) async {
    try {
      final stats = await _getPartnerStats(partnerId);
      final last30Days = DateTime.now().subtract(const Duration(days: 30));
      
      return {
        'partner_id': partnerId,
        'total_referrals': stats['total_referrals'],
        'completed_purchases': stats['completed_purchases'],
        'conversion_rate': stats['conversion_rate'],
        'total_earnings': stats['total_earnings'],
        'avg_commission': stats['avg_commission'],
        'last_30_days': await _getPartnerStats(partnerId, since: last30Days),
        'fraud_score': await _calculateFraudScore(partnerId),
        'performance_grade': _calculatePerformanceGrade(stats['conversion_rate']),
      };
    } catch (e) {
      debugPrint('❌ Failed to get partner performance report: $e');
      return {};
    }
  }

  /// Get system-wide analytics
  Future<Map<String, dynamic>> getSystemAnalytics() async {
    try {
      final last24Hours = DateTime.now().subtract(const Duration(hours: 24));
      final last7Days = DateTime.now().subtract(const Duration(days: 7));
      
      return {
        'active_partners': (await _getActivePartners()).length,
        'total_commissions_24h': await _getCommissionCount(last24Hours),
        'total_commissions_7d': await _getCommissionCount(last7Days),
        'cancellation_rate_24h': await _getCancellationRate(last24Hours),
        'avg_conversion_rate': await _getAverageConversionRate(),
        'pending_payouts': await _getPendingPayoutAmount(),
        'fraud_alerts_24h': await _getFraudAlertCount(last24Hours),
        'system_health': await _getSystemHealthScore(),
      };
    } catch (e) {
      debugPrint('❌ Failed to get system analytics: $e');
      return {};
    }
  }

  // Helper methods for monitoring checks
  Future<List<String>> _getActivePartners() async {
    // Implementation would query database for active partners
    return ['partner_wellness_guru', 'partner_fitness_coach']; // Mock data
  }

  Future<Map<String, dynamic>> _getPartnerStats(String partnerId, {DateTime? since}) async {
    // Implementation would calculate partner statistics from database
    return {
      'total_referrals': 100,
      'completed_purchases': 15,
      'conversion_rate': 0.15,
      'total_earnings': 75.0,
      'avg_commission': 5.0,
    }; // Mock data
  }

  Future<Map<String, dynamic>> _getCancellationStats(DateTime since) async {
    // Implementation would query cancellation statistics
    return {'total': 50, 'cancelled': 5}; // Mock data
  }

  Future<List<String>> _detectSelfReferrals() async {
    // Implementation would detect self-referrals
    return []; // Mock data
  }

  Future<List<String>> _detectRapidRefunds() async {
    // Implementation would detect rapid refund patterns
    return []; // Mock data
  }

  Future<List<String>> _detectUnusualPurchaseAmounts() async {
    // Implementation would detect unusual amounts
    return []; // Mock data
  }

  Future<List<Map<String, dynamic>>> _getPartnersDueForPayout(DateTime date) async {
    // Implementation would get partners due for payout
    return []; // Mock data
  }

  Future<double> _calculatePendingCommissions(String partnerId) async {
    // Implementation would calculate pending commissions
    return 25.0; // Mock data
  }

  Future<int> _checkProcessingDelays() async {
    // Implementation would check for processing delays
    return 0; // Mock data
  }

  Future<double> _calculateFraudScore(String partnerId) async {
    // Implementation would calculate fraud risk score
    return 0.1; // Mock data (0.0 = no risk, 1.0 = high risk)
  }

  String _calculatePerformanceGrade(double conversionRate) {
    if (conversionRate >= 0.10) return 'A';
    if (conversionRate >= 0.05) return 'B';
    if (conversionRate >= 0.02) return 'C';
    return 'D';
  }

  Future<int> _getCommissionCount(DateTime since) async {
    return 25; // Mock data
  }

  Future<double> _getCancellationRate(DateTime since) async {
    return 0.08; // Mock data
  }

  Future<double> _getAverageConversionRate() async {
    return 0.12; // Mock data
  }

  Future<double> _getPendingPayoutAmount() async {
    return 1250.0; // Mock data
  }

  Future<int> _getFraudAlertCount(DateTime since) async {
    return 0; // Mock data
  }

  Future<double> _getSystemHealthScore() async {
    return 0.95; // Mock data (0.0 = unhealthy, 1.0 = perfect)
  }

  // Alert methods
  Future<void> _alertAdmin(String title, String message) async {
    debugPrint('🚨 ADMIN ALERT: $title - $message');
    // Implementation would send actual alerts (email, Slack, etc.)
  }

  Future<void> _alertPartner(String partnerId, String title, String message) async {
    debugPrint('📢 PARTNER ALERT ($partnerId): $title - $message');
    // Implementation would send partner notifications
  }
}
