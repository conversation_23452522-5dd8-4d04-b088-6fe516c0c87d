import 'dart:convert';
import 'dart:math';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../data/plant_dataset.dart';
import '../models/ai_learning_models.dart';
import '../providers/app_state.dart';

/// Advanced AI learning engine that continuously improves query analysis capabilities
class AILearningEngine {
  static final AILearningEngine _instance = AILearningEngine._internal();
  factory AILearningEngine() => _instance;
  AILearningEngine._internal();

  // Learning data storage keys
  static const String _conditionPatternsKey = 'ai_condition_patterns';
  static const String _symptomPatternsKey = 'ai_symptom_patterns';
  static const String _plantMentionsKey = 'ai_plant_mentions';
  static const String _queryStatsKey = 'ai_query_stats';

  // Learning thresholds
  static const int _minLearningData = 10;
  static const double _confidenceThreshold = 0.7;
  static const int _maxPatternStorage = 1000;

  // Current knowledge state
  Map<String, ConditionPattern> _conditionPatterns = {};
  Map<String, SymptomPattern> _symptomPatterns = {};
  Map<String, PlantMentionPattern> _plantPatterns = {};
  final List<UserFeedback> _userFeedback = [];
  QueryStatistics _queryStats = QueryStatistics();

  /// Initialize the learning engine
  Future<void> initialize() async {
    try {
      await _loadLearningData();
      await _initializeBasePatterns();
      debugPrint('✅ AI Learning Engine initialized');
    } catch (e) {
      debugPrint('❌ Error initializing AI Learning Engine: $e');
    }
  }

  /// Analyze a user query and learn from it
  Future<EnhancedQueryAnalysis> analyzeAndLearn(String query, String? previousContext) async {
    final analysis = await _performEnhancedAnalysis(query, previousContext);

    // Learn from this query
    await _learnFromQuery(query, analysis);

    // Update statistics
    _updateQueryStatistics(analysis);

    return analysis;
  }

  /// Perform enhanced query analysis with learned patterns
  Future<EnhancedQueryAnalysis> _performEnhancedAnalysis(String query, String? previousContext) async {
    final lowerQuery = query.toLowerCase();

    // Enhanced condition detection
    final conditions = await _detectConditionsEnhanced(lowerQuery);

    // Enhanced symptom recognition
    final symptoms = await _detectSymptomsEnhanced(lowerQuery);

    // Enhanced plant identification
    final plants = await _identifyPlantsEnhanced(lowerQuery);

    // Enhanced urgency scoring
    final urgency = await _assessUrgencyEnhanced(lowerQuery, conditions, symptoms);

    // Enhanced context understanding
    final context = await _analyzeContextEnhanced(lowerQuery, previousContext, conditions, symptoms);

    // Calculate confidence scores
    final confidence = _calculateConfidenceScores(conditions, symptoms, plants, urgency);

    return EnhancedQueryAnalysis(
      originalQuery: query,
      detectedConditions: conditions,
      detectedSymptoms: symptoms,
      identifiedPlants: plants,
      urgencyLevel: urgency,
      contextAnalysis: context,
      confidenceScores: confidence,
      suggestedFollowUps: _generateFollowUpSuggestions(conditions, symptoms, plants),
      learningOpportunities: _identifyLearningOpportunities(query, conditions, symptoms),
    );
  }

  /// Enhanced condition detection using learned patterns
  Future<List<DetectedCondition>> _detectConditionsEnhanced(String query) async {
    final conditions = <DetectedCondition>[];

    // Base condition keywords (expanded from original 10+)
    final baseConditions = {
      'diabetes': ['diabetes', 'diabetic', 'blood sugar', 'glucose', 'insulin resistance', 'hyperglycemia'],
      'hypertension': ['high blood pressure', 'hypertension', 'blood pressure', 'bp', 'cardiovascular'],
      'arthritis': ['arthritis', 'joint pain', 'rheumatoid', 'osteoarthritis', 'joint inflammation'],
      'depression': ['depression', 'depressed', 'sad', 'hopeless', 'mood disorder', 'melancholy'],
      'anxiety': ['anxiety', 'anxious', 'panic', 'worry', 'nervous', 'stress disorder'],
      'insomnia': ['insomnia', 'sleep problems', 'can\'t sleep', 'sleepless', 'sleep disorder'],
      'ibs': ['ibs', 'irritable bowel', 'digestive issues', 'bowel problems', 'gut issues'],
      'migraine': ['migraine', 'headache', 'head pain', 'severe headache', 'cluster headache'],
      'eczema': ['eczema', 'skin rash', 'dermatitis', 'atopic dermatitis', 'skin inflammation'],
      'asthma': ['asthma', 'breathing problems', 'wheezing', 'respiratory issues', 'bronchial'],
      'fibromyalgia': ['fibromyalgia', 'chronic pain', 'muscle pain', 'tender points'],
      'gerd': ['gerd', 'acid reflux', 'heartburn', 'gastroesophageal', 'stomach acid'],
      'hypothyroidism': ['hypothyroidism', 'thyroid', 'low thyroid', 'thyroid disorder'],
      'chronic_fatigue': ['chronic fatigue', 'cfs', 'exhaustion', 'persistent fatigue'],
      'autoimmune': ['autoimmune', 'lupus', 'multiple sclerosis', 'ms', 'immune disorder'],
    };

    // Check base conditions
    for (final entry in baseConditions.entries) {
      final conditionName = entry.key;
      final keywords = entry.value;

      double confidence = 0.0;
      final matchedKeywords = <String>[];

      for (final keyword in keywords) {
        if (query.contains(keyword)) {
          matchedKeywords.add(keyword);
          confidence += 0.2; // Base confidence per keyword
        }
      }

      // Check learned patterns
      if (_conditionPatterns.containsKey(conditionName)) {
        final pattern = _conditionPatterns[conditionName]!;
        final learnedConfidence = _evaluateConditionPattern(query, pattern);
        confidence = max(confidence, learnedConfidence);
      }

      if (confidence > (_confidenceThreshold * 0.4)) { // Use threshold for filtering
        conditions.add(DetectedCondition(
          name: conditionName,
          confidence: min(confidence, 1.0),
          matchedKeywords: matchedKeywords,
          source: matchedKeywords.isNotEmpty ? 'base_keywords' : 'learned_pattern',
        ));
      }
    }

    return conditions..sort((a, b) => b.confidence.compareTo(a.confidence));
  }

  /// Enhanced symptom recognition using learned patterns
  Future<List<DetectedSymptom>> _detectSymptomsEnhanced(String query) async {
    final symptoms = <DetectedSymptom>[];

    // Expanded symptom database (20+ symptoms)
    final baseSymptoms = {
      'pain': ['pain', 'ache', 'sore', 'hurt', 'discomfort', 'tender'],
      'fatigue': ['tired', 'fatigue', 'exhausted', 'weary', 'drained', 'low energy'],
      'nausea': ['nausea', 'nauseous', 'sick to stomach', 'queasy', 'upset stomach'],
      'headache': ['headache', 'head pain', 'migraine', 'head ache', 'cranial pain'],
      'fever': ['fever', 'feverish', 'high temperature', 'hot', 'burning up'],
      'cough': ['cough', 'coughing', 'hacking', 'persistent cough', 'dry cough'],
      'congestion': ['congestion', 'stuffy', 'blocked nose', 'nasal congestion'],
      'bloating': ['bloating', 'bloated', 'swollen', 'distended', 'gas'],
      'constipation': ['constipation', 'constipated', 'hard stool', 'difficulty passing'],
      'diarrhea': ['diarrhea', 'loose stool', 'watery stool', 'frequent bowel'],
      'rash': ['rash', 'skin irritation', 'red skin', 'itchy skin', 'skin bumps'],
      'itching': ['itching', 'itchy', 'scratching', 'pruritus', 'skin itch'],
      'swelling': ['swelling', 'swollen', 'inflammation', 'puffiness', 'edema'],
      'dizziness': ['dizziness', 'dizzy', 'lightheaded', 'vertigo', 'unsteady'],
      'shortness_of_breath': ['shortness of breath', 'breathless', 'winded', 'breathing difficulty'],
      'chest_pain': ['chest pain', 'chest discomfort', 'heart pain', 'chest pressure'],
      'muscle_aches': ['muscle aches', 'muscle pain', 'muscle soreness', 'myalgia'],
      'joint_stiffness': ['joint stiffness', 'stiff joints', 'morning stiffness', 'rigid joints'],
      'sleep_problems': ['sleep problems', 'insomnia', 'can\'t sleep', 'restless sleep'],
      'mood_changes': ['mood changes', 'irritable', 'mood swings', 'emotional'],
      'memory_issues': ['memory problems', 'forgetful', 'brain fog', 'cognitive issues'],
      'appetite_changes': ['appetite changes', 'loss of appetite', 'increased appetite'],
      'weight_changes': ['weight gain', 'weight loss', 'weight changes', 'unexplained weight'],
      'hot_flashes': ['hot flashes', 'hot flushes', 'sudden heat', 'night sweats'],
      'cold_sensitivity': ['cold sensitivity', 'always cold', 'cold intolerance', 'chills'],
    };

    // Check base symptoms
    for (final entry in baseSymptoms.entries) {
      final symptomName = entry.key;
      final keywords = entry.value;

      double confidence = 0.0;
      final matchedKeywords = <String>[];

      for (final keyword in keywords) {
        if (query.contains(keyword)) {
          matchedKeywords.add(keyword);
          confidence += 0.25; // Base confidence per keyword
        }
      }

      // Check learned patterns
      if (_symptomPatterns.containsKey(symptomName)) {
        final pattern = _symptomPatterns[symptomName]!;
        final learnedConfidence = _evaluateSymptomPattern(query, pattern);
        confidence = max(confidence, learnedConfidence);
      }

      if (confidence > 0.2) {
        symptoms.add(DetectedSymptom(
          name: symptomName,
          confidence: min(confidence, 1.0),
          matchedKeywords: matchedKeywords,
          severity: _estimateSeverity(query, matchedKeywords),
          duration: _estimateDuration(query),
          source: matchedKeywords.isNotEmpty ? 'base_keywords' : 'learned_pattern',
        ));
      }
    }

    return symptoms..sort((a, b) => b.confidence.compareTo(a.confidence));
  }

  /// Enhanced plant identification using learned patterns
  Future<List<IdentifiedPlant>> _identifyPlantsEnhanced(String query) async {
    final identifiedPlants = <IdentifiedPlant>[];
    final allPlants = PlantDataset.getAllPlants();

    for (final plant in allPlants) {
      double confidence = 0.0;
      final matchedTerms = <String>[];

      // Check common name
      if (query.contains(plant.name.toLowerCase())) {
        confidence += 0.8;
        matchedTerms.add(plant.name);
      }

      // Check scientific name
      if (query.contains(plant.scientificName.toLowerCase())) {
        confidence += 0.9;
        matchedTerms.add(plant.scientificName);
      }

      // Check alternative names and learned patterns
      if (_plantPatterns.containsKey(plant.id)) {
        final pattern = _plantPatterns[plant.id]!;
        final learnedConfidence = _evaluatePlantPattern(query, pattern);
        confidence = max(confidence, learnedConfidence);

        if (learnedConfidence > 0.3) {
          matchedTerms.addAll(pattern.alternativeNames);
        }
      }

      // Check benefits for contextual matching
      for (final benefit in plant.benefits) {
        if (query.contains(benefit.toLowerCase())) {
          confidence += 0.1;
          matchedTerms.add(benefit);
        }
      }

      if (confidence > 0.1) {
        identifiedPlants.add(IdentifiedPlant(
          plant: plant,
          confidence: min(confidence, 1.0),
          matchedTerms: matchedTerms,
          relevanceScore: _calculatePlantRelevance(plant, query),
        ));
      }
    }

    return identifiedPlants..sort((a, b) => b.confidence.compareTo(a.confidence));
  }

  /// Enhanced urgency assessment using learned patterns
  Future<UrgencyLevel> _assessUrgencyEnhanced(String query, List<DetectedCondition> conditions, List<DetectedSymptom> symptoms) async {
    // Emergency keywords (immediate medical attention required)
    final emergencyKeywords = [
      'emergency', 'urgent', 'severe', 'intense', 'unbearable', 'chest pain',
      'difficulty breathing', 'bleeding', 'poisoning', 'overdose', 'suicide',
      'heart attack', 'stroke', 'allergic reaction', 'anaphylaxis'
    ];

    // High urgency keywords
    final highUrgencyKeywords = [
      'persistent', 'chronic', 'worsening', 'getting worse', 'weeks', 'months',
      'severe pain', 'high fever', 'blood', 'swelling', 'infection'
    ];

    // Check for emergency keywords
    if (emergencyKeywords.any((keyword) => query.contains(keyword))) {
      return UrgencyLevel.emergency;
    }

    // Check for high-risk conditions
    final highRiskConditions = ['chest_pain', 'stroke_symptoms', 'severe_allergic_reaction'];
    if (conditions.any((c) => highRiskConditions.contains(c.name) && c.confidence > 0.7)) {
      return UrgencyLevel.emergency;
    }

    // Check for high urgency indicators
    if (highUrgencyKeywords.any((keyword) => query.contains(keyword))) {
      return UrgencyLevel.high;
    }

    // Check symptom severity
    final severeSymptomsCount = symptoms.where((s) => s.severity == 'severe').length;
    if (severeSymptomsCount >= 2) {
      return UrgencyLevel.high;
    }

    return UrgencyLevel.normal;
  }

  /// Enhanced context analysis
  Future<ContextAnalysis> _analyzeContextEnhanced(String query, String? previousContext, List<DetectedCondition> conditions, List<DetectedSymptom> symptoms) async {
    final context = ContextAnalysis();

    // Determine query intent
    if (query.contains('how to') || query.contains('what is') || query.contains('tell me about')) {
      context.intent = QueryIntent.informational;
    } else if (query.contains('help') || query.contains('treatment') || query.contains('remedy')) {
      context.intent = QueryIntent.treatmentSeeking;
    } else if (query.contains('safe') || query.contains('side effects') || query.contains('interactions')) {
      context.intent = QueryIntent.safetyConcern;
    } else {
      context.intent = QueryIntent.general;
    }

    // Determine response depth needed
    if (conditions.isNotEmpty || symptoms.length > 2) {
      context.responseDepth = ResponseDepth.comprehensive;
    } else if (symptoms.isNotEmpty) {
      context.responseDepth = ResponseDepth.moderate;
    } else {
      context.responseDepth = ResponseDepth.basic;
    }

    // Analyze user expertise level
    final medicalTerms = ['pathophysiology', 'pharmacokinetics', 'contraindications', 'bioavailability'];
    if (medicalTerms.any((term) => query.contains(term))) {
      context.userExpertiseLevel = ExpertiseLevel.advanced;
    } else if (query.length > 100 && conditions.isNotEmpty) {
      context.userExpertiseLevel = ExpertiseLevel.intermediate;
    } else {
      context.userExpertiseLevel = ExpertiseLevel.beginner;
    }

    return context;
  }

  /// Calculate confidence scores for analysis components
  Map<String, double> _calculateConfidenceScores(List<DetectedCondition> conditions, List<DetectedSymptom> symptoms, List<IdentifiedPlant> plants, UrgencyLevel urgency) {
    return {
      'overall': _calculateOverallConfidence(conditions, symptoms, plants),
      'conditions': conditions.isEmpty ? 0.0 : conditions.map((c) => c.confidence).reduce((a, b) => a + b) / conditions.length,
      'symptoms': symptoms.isEmpty ? 0.0 : symptoms.map((s) => s.confidence).reduce((a, b) => a + b) / symptoms.length,
      'plants': plants.isEmpty ? 0.0 : plants.map((p) => p.confidence).reduce((a, b) => a + b) / plants.length,
      'urgency': urgency == UrgencyLevel.emergency ? 1.0 : urgency == UrgencyLevel.high ? 0.7 : 0.3,
    };
  }

  /// Calculate overall confidence score
  double _calculateOverallConfidence(List<DetectedCondition> conditions, List<DetectedSymptom> symptoms, List<IdentifiedPlant> plants) {
    double score = 0.0;
    int components = 0;

    if (conditions.isNotEmpty) {
      score += conditions.map((c) => c.confidence).reduce((a, b) => a + b) / conditions.length;
      components++;
    }

    if (symptoms.isNotEmpty) {
      score += symptoms.map((s) => s.confidence).reduce((a, b) => a + b) / symptoms.length;
      components++;
    }

    if (plants.isNotEmpty) {
      score += plants.map((p) => p.confidence).reduce((a, b) => a + b) / plants.length;
      components++;
    }

    return components > 0 ? score / components : 0.0;
  }

  /// Generate follow-up suggestions
  List<String> _generateFollowUpSuggestions(List<DetectedCondition> conditions, List<DetectedSymptom> symptoms, List<IdentifiedPlant> plants) {
    final suggestions = <String>[];

    if (conditions.isNotEmpty) {
      suggestions.add('Would you like to know more about natural approaches for ${conditions.first.name}?');
    }

    if (symptoms.isNotEmpty && symptoms.length > 1) {
      suggestions.add('Are these symptoms related or separate concerns?');
    }

    if (plants.isNotEmpty) {
      suggestions.add('Would you like dosage and preparation information for ${plants.first.plant.name}?');
    }

    suggestions.add('Do you have any existing health conditions or take medications?');
    suggestions.add('Are you looking for immediate relief or long-term support?');

    return suggestions.take(3).toList();
  }

  /// Identify learning opportunities from the query
  List<String> _identifyLearningOpportunities(String query, List<DetectedCondition> conditions, List<DetectedSymptom> symptoms) {
    final opportunities = <String>[];

    // Check for new condition patterns
    if (conditions.isEmpty && (query.contains('condition') || query.contains('disease'))) {
      opportunities.add('potential_new_condition');
    }

    // Check for new symptom patterns
    if (symptoms.isEmpty && (query.contains('feel') || query.contains('experiencing'))) {
      opportunities.add('potential_new_symptom');
    }

    // Check for plant usage patterns
    if (query.contains('use') || query.contains('take') || query.contains('dosage')) {
      opportunities.add('usage_pattern');
    }

    return opportunities;
  }

  /// Learn from query and update patterns
  Future<void> _learnFromQuery(String query, EnhancedQueryAnalysis analysis) async {
    // Update condition patterns
    for (final condition in analysis.detectedConditions) {
      await _updateConditionPattern(condition, query);
    }

    // Update symptom patterns
    for (final symptom in analysis.detectedSymptoms) {
      await _updateSymptomPattern(symptom, query);
    }

    // Update plant patterns
    for (final plant in analysis.identifiedPlants) {
      await _updatePlantPattern(plant, query);
    }

    // Save learning data
    await _saveLearningData();
  }

  /// Load learning data from storage
  Future<void> _loadLearningData() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // Load condition patterns
      final conditionData = prefs.getString(_conditionPatternsKey);
      if (conditionData != null) {
        final Map<String, dynamic> data = jsonDecode(conditionData);
        _conditionPatterns = data.map((key, value) =>
          MapEntry(key, ConditionPattern.fromJson(value)));
      }

      // Load symptom patterns
      final symptomData = prefs.getString(_symptomPatternsKey);
      if (symptomData != null) {
        final Map<String, dynamic> data = jsonDecode(symptomData);
        _symptomPatterns = data.map((key, value) =>
          MapEntry(key, SymptomPattern.fromJson(value)));
      }

      // Load plant patterns
      final plantData = prefs.getString(_plantMentionsKey);
      if (plantData != null) {
        final Map<String, dynamic> data = jsonDecode(plantData);
        _plantPatterns = data.map((key, value) =>
          MapEntry(key, PlantMentionPattern.fromJson(value)));
      }

      // Load query statistics
      final statsData = prefs.getString(_queryStatsKey);
      if (statsData != null) {
        _queryStats = QueryStatistics.fromJson(jsonDecode(statsData));
      }

      debugPrint('✅ Learning data loaded successfully');
    } catch (e) {
      debugPrint('⚠️ Error loading learning data: $e');
    }
  }

  /// Save learning data to storage
  Future<void> _saveLearningData() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // Save condition patterns
      final conditionData = _conditionPatterns.map((key, value) =>
        MapEntry(key, value.toJson()));
      await prefs.setString(_conditionPatternsKey, jsonEncode(conditionData));

      // Save symptom patterns
      final symptomData = _symptomPatterns.map((key, value) =>
        MapEntry(key, value.toJson()));
      await prefs.setString(_symptomPatternsKey, jsonEncode(symptomData));

      // Save plant patterns
      final plantData = _plantPatterns.map((key, value) =>
        MapEntry(key, value.toJson()));
      await prefs.setString(_plantMentionsKey, jsonEncode(plantData));

      // Save query statistics
      await prefs.setString(_queryStatsKey, jsonEncode(_queryStats.toJson()));

      debugPrint('✅ Learning data saved successfully');
    } catch (e) {
      debugPrint('❌ Error saving learning data: $e');
    }
  }

  /// Initialize base patterns for learning
  Future<void> _initializeBasePatterns() async {
    // Initialize with empty patterns - they will be built through learning
    // Ensure we don't exceed max pattern storage
    if (_conditionPatterns.length > _maxPatternStorage) {
      _pruneOldPatterns();
    }
    debugPrint('✅ Base patterns initialized');
  }

  /// Prune old patterns to maintain performance
  void _pruneOldPatterns() {
    // Keep only the most recent patterns within storage limit
    if (_conditionPatterns.length > _maxPatternStorage) {
      final sortedPatterns = _conditionPatterns.entries.toList()
        ..sort((a, b) => b.value.lastUpdated.compareTo(a.value.lastUpdated));

      _conditionPatterns = Map.fromEntries(
        sortedPatterns.take(_maxPatternStorage ~/ 2)
      );
    }

    if (_symptomPatterns.length > _maxPatternStorage) {
      final sortedPatterns = _symptomPatterns.entries.toList()
        ..sort((a, b) => b.value.lastUpdated.compareTo(a.value.lastUpdated));

      _symptomPatterns = Map.fromEntries(
        sortedPatterns.take(_maxPatternStorage ~/ 2)
      );
    }
  }

  /// Update query statistics
  void _updateQueryStatistics(EnhancedQueryAnalysis analysis) {
    _queryStats.totalQueries++;
    _queryStats.lastUpdated = DateTime.now();

    if (analysis.confidenceScores['overall']! > 0.5) {
      _queryStats.successfulAnalyses++;
    }

    switch (analysis.urgencyLevel) {
      case UrgencyLevel.emergency:
        _queryStats.emergencyQueries++;
        break;
      case UrgencyLevel.high:
        _queryStats.highUrgencyQueries++;
        break;
      case UrgencyLevel.normal:
        break;
    }

    // Update condition counts
    for (final condition in analysis.detectedConditions) {
      _queryStats.conditionCounts[condition.name] =
        (_queryStats.conditionCounts[condition.name] ?? 0) + 1;
    }

    // Update symptom counts
    for (final symptom in analysis.detectedSymptoms) {
      _queryStats.symptomCounts[symptom.name] =
        (_queryStats.symptomCounts[symptom.name] ?? 0) + 1;
    }

    // Update plant mentions
    for (final plant in analysis.identifiedPlants) {
      _queryStats.plantMentions[plant.plant.name] =
        (_queryStats.plantMentions[plant.plant.name] ?? 0) + 1;
    }

    // Update average confidence
    final totalConfidence = _queryStats.averageConfidence * (_queryStats.totalQueries - 1);
    _queryStats.averageConfidence =
      (totalConfidence + analysis.confidenceScores['overall']!) / _queryStats.totalQueries;
  }

  /// Evaluate condition pattern against query
  double _evaluateConditionPattern(String query, ConditionPattern pattern) {
    double confidence = 0.0;

    for (final keyword in pattern.keywords) {
      if (query.contains(keyword)) {
        confidence += pattern.keywordWeights[keyword] ?? 0.1;
      }
    }

    for (final contextPattern in pattern.contextPatterns) {
      if (query.contains(contextPattern)) {
        confidence += 0.15;
      }
    }

    return min(confidence, 1.0);
  }

  /// Evaluate symptom pattern against query
  double _evaluateSymptomPattern(String query, SymptomPattern pattern) {
    double confidence = 0.0;

    for (final keyword in pattern.keywords) {
      if (query.contains(keyword)) {
        confidence += pattern.keywordWeights[keyword] ?? 0.1;
      }
    }

    for (final indicator in pattern.severityIndicators) {
      if (query.contains(indicator)) {
        confidence += 0.1;
      }
    }

    return min(confidence, 1.0);
  }

  /// Evaluate plant pattern against query
  double _evaluatePlantPattern(String query, PlantMentionPattern pattern) {
    double confidence = 0.0;

    for (final name in pattern.alternativeNames) {
      if (query.contains(name.toLowerCase())) {
        confidence += pattern.nameWeights[name] ?? 0.2;
      }
    }

    for (final keyword in pattern.contextKeywords) {
      if (query.contains(keyword)) {
        confidence += 0.1;
      }
    }

    return min(confidence, 1.0);
  }

  /// Estimate symptom severity from query context
  String _estimateSeverity(String query, List<String> matchedKeywords) {
    final severeIndicators = ['severe', 'intense', 'unbearable', 'excruciating', 'extreme'];
    final moderateIndicators = ['moderate', 'noticeable', 'significant', 'troublesome'];
    final mildIndicators = ['mild', 'slight', 'minor', 'little', 'barely'];

    if (severeIndicators.any((indicator) => query.contains(indicator))) {
      return 'severe';
    } else if (moderateIndicators.any((indicator) => query.contains(indicator))) {
      return 'moderate';
    } else if (mildIndicators.any((indicator) => query.contains(indicator))) {
      return 'mild';
    } else {
      return 'unknown';
    }
  }

  /// Estimate symptom duration from query context
  String _estimateDuration(String query) {
    final acuteIndicators = ['sudden', 'just started', 'today', 'this morning', 'recently'];
    final chronicIndicators = ['chronic', 'ongoing', 'months', 'years', 'always', 'persistent'];
    final subacuteIndicators = ['weeks', 'few days', 'several days', 'week ago'];

    if (chronicIndicators.any((indicator) => query.contains(indicator))) {
      return 'chronic';
    } else if (subacuteIndicators.any((indicator) => query.contains(indicator))) {
      return 'subacute';
    } else if (acuteIndicators.any((indicator) => query.contains(indicator))) {
      return 'acute';
    } else {
      return 'unknown';
    }
  }

  /// Calculate plant relevance score based on query context
  double _calculatePlantRelevance(Plant plant, String query) {
    double relevance = 0.0;

    // Check if plant benefits match query context
    for (final benefit in plant.benefits) {
      if (query.contains(benefit.toLowerCase())) {
        relevance += 0.3;
      }
    }

    // Check for usage context
    final usageKeywords = ['use', 'take', 'dosage', 'how much', 'preparation'];
    if (usageKeywords.any((keyword) => query.contains(keyword))) {
      relevance += 0.2;
    }

    // Check for safety context
    final safetyKeywords = ['safe', 'side effects', 'interactions', 'contraindications'];
    if (safetyKeywords.any((keyword) => query.contains(keyword))) {
      relevance += 0.2;
    }

    return min(relevance, 1.0);
  }

  /// Update condition pattern with new learning data
  Future<void> _updateConditionPattern(DetectedCondition condition, String query) async {
    final existing = _conditionPatterns[condition.name];

    if (existing != null) {
      // Update existing pattern
      final updatedKeywords = Set<String>.from(existing.keywords)
        ..addAll(condition.matchedKeywords);

      final updatedWeights = Map<String, double>.from(existing.keywordWeights);
      for (final keyword in condition.matchedKeywords) {
        updatedWeights[keyword] = (updatedWeights[keyword] ?? 0.0) + 0.1;
      }

      _conditionPatterns[condition.name] = ConditionPattern(
        conditionName: condition.name,
        keywords: updatedKeywords.toList(),
        contextPatterns: existing.contextPatterns,
        keywordWeights: updatedWeights,
        occurrenceCount: existing.occurrenceCount + 1,
        averageConfidence: (existing.averageConfidence * existing.occurrenceCount + condition.confidence) / (existing.occurrenceCount + 1),
        lastUpdated: DateTime.now(),
      );
    } else {
      // Create new pattern
      final keywordWeights = <String, double>{};
      for (final keyword in condition.matchedKeywords) {
        keywordWeights[keyword] = 0.2;
      }

      _conditionPatterns[condition.name] = ConditionPattern(
        conditionName: condition.name,
        keywords: condition.matchedKeywords,
        contextPatterns: [],
        keywordWeights: keywordWeights,
        occurrenceCount: 1,
        averageConfidence: condition.confidence,
        lastUpdated: DateTime.now(),
      );
    }
  }

  /// Update symptom pattern with new learning data
  Future<void> _updateSymptomPattern(DetectedSymptom symptom, String query) async {
    final existing = _symptomPatterns[symptom.name];

    if (existing != null) {
      // Update existing pattern
      final updatedKeywords = Set<String>.from(existing.keywords)
        ..addAll(symptom.matchedKeywords);

      final updatedWeights = Map<String, double>.from(existing.keywordWeights);
      for (final keyword in symptom.matchedKeywords) {
        updatedWeights[keyword] = (updatedWeights[keyword] ?? 0.0) + 0.1;
      }

      _symptomPatterns[symptom.name] = SymptomPattern(
        symptomName: symptom.name,
        keywords: updatedKeywords.toList(),
        severityIndicators: existing.severityIndicators,
        durationIndicators: existing.durationIndicators,
        keywordWeights: updatedWeights,
        occurrenceCount: existing.occurrenceCount + 1,
        averageConfidence: (existing.averageConfidence * existing.occurrenceCount + symptom.confidence) / (existing.occurrenceCount + 1),
        lastUpdated: DateTime.now(),
      );
    } else {
      // Create new pattern
      final keywordWeights = <String, double>{};
      for (final keyword in symptom.matchedKeywords) {
        keywordWeights[keyword] = 0.2;
      }

      _symptomPatterns[symptom.name] = SymptomPattern(
        symptomName: symptom.name,
        keywords: symptom.matchedKeywords,
        severityIndicators: [],
        durationIndicators: [],
        keywordWeights: keywordWeights,
        occurrenceCount: 1,
        averageConfidence: symptom.confidence,
        lastUpdated: DateTime.now(),
      );
    }
  }

  /// Update plant pattern with new learning data
  Future<void> _updatePlantPattern(IdentifiedPlant identifiedPlant, String query) async {
    final plant = identifiedPlant.plant;
    final existing = _plantPatterns[plant.id];

    if (existing != null) {
      // Update existing pattern
      final updatedNames = Set<String>.from(existing.alternativeNames)
        ..addAll(identifiedPlant.matchedTerms);

      final updatedWeights = Map<String, double>.from(existing.nameWeights);
      for (final term in identifiedPlant.matchedTerms) {
        updatedWeights[term] = (updatedWeights[term] ?? 0.0) + 0.1;
      }

      _plantPatterns[plant.id] = PlantMentionPattern(
        plantId: plant.id,
        alternativeNames: updatedNames.toList(),
        contextKeywords: existing.contextKeywords,
        nameWeights: updatedWeights,
        mentionCount: existing.mentionCount + 1,
        averageRelevance: (existing.averageRelevance * existing.mentionCount + identifiedPlant.relevanceScore) / (existing.mentionCount + 1),
        lastUpdated: DateTime.now(),
      );
    } else {
      // Create new pattern
      final nameWeights = <String, double>{};
      for (final term in identifiedPlant.matchedTerms) {
        nameWeights[term] = 0.3;
      }

      _plantPatterns[plant.id] = PlantMentionPattern(
        plantId: plant.id,
        alternativeNames: identifiedPlant.matchedTerms,
        contextKeywords: [],
        nameWeights: nameWeights,
        mentionCount: 1,
        averageRelevance: identifiedPlant.relevanceScore,
        lastUpdated: DateTime.now(),
      );
    }
  }

  /// Get learning statistics for monitoring
  Map<String, dynamic> getLearningStatistics() {
    return {
      'totalQueries': _queryStats.totalQueries,
      'successfulAnalyses': _queryStats.successfulAnalyses,
      'averageConfidence': _queryStats.averageConfidence,
      'conditionPatternsCount': _conditionPatterns.length,
      'symptomPatternsCount': _symptomPatterns.length,
      'plantPatternsCount': _plantPatterns.length,
      'topConditions': _getTopItems(_queryStats.conditionCounts, 5),
      'topSymptoms': _getTopItems(_queryStats.symptomCounts, 5),
      'topPlants': _getTopItems(_queryStats.plantMentions, 5),
      'emergencyQueries': _queryStats.emergencyQueries,
      'highUrgencyQueries': _queryStats.highUrgencyQueries,
      'hasMinimumData': _queryStats.totalQueries >= _minLearningData,
      'confidenceThreshold': _confidenceThreshold,
      'storageUtilization': (_conditionPatterns.length + _symptomPatterns.length) / _maxPatternStorage,
    };
  }

  /// Get top items from a count map
  List<MapEntry<String, int>> _getTopItems(Map<String, int> counts, int limit) {
    final sorted = counts.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));
    return sorted.take(limit).toList();
  }

  /// Reset learning data (for testing or fresh start)
  Future<void> resetLearningData() async {
    _conditionPatterns.clear();
    _symptomPatterns.clear();
    _plantPatterns.clear();
    _userFeedback.clear();
    _queryStats = QueryStatistics();

    await _saveLearningData();
    debugPrint('🔄 Learning data reset successfully');
  }

  /// Export learning data for backup
  Future<String> exportLearningData() async {
    final exportData = {
      'conditionPatterns': _conditionPatterns.map((k, v) => MapEntry(k, v.toJson())),
      'symptomPatterns': _symptomPatterns.map((k, v) => MapEntry(k, v.toJson())),
      'plantPatterns': _plantPatterns.map((k, v) => MapEntry(k, v.toJson())),
      'queryStats': _queryStats.toJson(),
      'exportDate': DateTime.now().toIso8601String(),
    };

    return jsonEncode(exportData);
  }

  /// Import learning data from backup
  Future<void> importLearningData(String jsonData) async {
    try {
      final data = jsonDecode(jsonData);

      // Import condition patterns
      if (data['conditionPatterns'] != null) {
        _conditionPatterns = Map<String, ConditionPattern>.from(
          data['conditionPatterns'].map((k, v) => MapEntry(k, ConditionPattern.fromJson(v)))
        );
      }

      // Import symptom patterns
      if (data['symptomPatterns'] != null) {
        _symptomPatterns = Map<String, SymptomPattern>.from(
          data['symptomPatterns'].map((k, v) => MapEntry(k, SymptomPattern.fromJson(v)))
        );
      }

      // Import plant patterns
      if (data['plantPatterns'] != null) {
        _plantPatterns = Map<String, PlantMentionPattern>.from(
          data['plantPatterns'].map((k, v) => MapEntry(k, PlantMentionPattern.fromJson(v)))
        );
      }

      // Import query stats
      if (data['queryStats'] != null) {
        _queryStats = QueryStatistics.fromJson(data['queryStats']);
      }

      await _saveLearningData();
      debugPrint('✅ Learning data imported successfully');
    } catch (e) {
      debugPrint('❌ Error importing learning data: $e');
      throw Exception('Failed to import learning data: $e');
    }
  }
}