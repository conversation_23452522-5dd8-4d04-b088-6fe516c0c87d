import 'package:test/test.dart';
import 'package:commission_server/controllers/commission_controller.dart';

void main() {
  group('Commission Tests', () {
    test('Basic vendor pays 5% platform fee', () {
      final result = CommissionController.calculateCommissions(100, 'Basic');
      expect(result['platformFee'], equals(5.0));
    });

    test('Invalid sale amount throws error', () {
      expect(() => CommissionController.calculateCommissions(-50, 'Premium'),
        throwsArgumentError);
    });
  });
}
