# 📅 30-Day Partner Payout System Guide

## 🎯 **Partner Payout Schedule: Every 30 Days**

Your commission system now includes automated 30-day partner payouts with full tracking and Stripe integration.

## 📊 **How 30-Day Payouts Work**

### **Payout Cycle:**
```
Day 1-30: Partner earns commissions
Day 30: Automatic payout processing
Day 31: New 30-day cycle begins
```

### **Example Sc<PERSON>rio:**
```
Partner joins: January 1st
First payout: January 31st (30 days later)
Second payout: March 2nd (30 days after first)
Third payout: April 1st (30 days after second)
```

## 💰 **Commission Accumulation**

### **Daily Commission Tracking:**
```
Day 1: $100 sale → Partner earns $5 (5%)
Day 10: $200 sale → Partner earns $10 (5%)
Day 20: $150 sale → Partner earns $7.50 (5%)
Day 30: $300 sale → Partner earns $15 (5%)

Total 30-day earnings: $37.50
```

### **Database Tracking:**
```sql
-- Each commission is tracked individually
INSERT INTO commissions (
  partner_id = 'partner_wellness_guru',
  partner_type = 'affiliate',
  commission_amount = 5.00,
  status = 'pending',
  created_at = '2024-01-01'
);

-- Payout schedule tracks cycles
INSERT INTO payout_schedules (
  partner_id = 'partner_wellness_guru',
  cycle_start_date = '2024-01-01',
  cycle_end_date = '2024-01-31',
  next_payout_date = '2024-01-31'
);
```

## 🔄 **Automated Payout Processing**

### **Daily Automation:**
```dart
// Run this daily (e.g., via cron job)
final payoutScheduler = PayoutSchedulerService();
await payoutScheduler.processDuePayouts();

// Automatically processes all partners due for payout
```

### **Payout Processing Steps:**
1. **Check Due Partners:** Find partners with `next_payout_date <= today`
2. **Calculate Earnings:** Sum all pending commissions for 30-day period
3. **Calculate Fees:** Apply Stripe fees (2.9% + $0.30)
4. **Create Payout:** Generate payout record with net amount
5. **Mark Commissions Paid:** Update commission status to 'paid'
6. **Schedule Next Cycle:** Set next payout date 30 days forward
7. **Process Payment:** Send to Stripe for actual payment

## 💳 **Stripe Integration & Fees**

### **Fee Structure:**
```
Gross Earnings: $37.50
Stripe Fee: (37.50 × 2.9%) + $0.30 = $1.39
Net Payout: $37.50 - $1.39 = $36.11
```

### **Payment Processing:**
```dart
// Stripe payout processing
await stripeService.createPayout(
  partnerId: 'partner_wellness_guru',
  amount: 36.11, // Net amount after fees
  currency: 'usd',
  method: 'instant', // or 'standard'
);
```

## 🗄️ **Database Schema**

### **Payout Schedules Table:**
```sql
CREATE TABLE payout_schedules (
  id VARCHAR(255) PRIMARY KEY,
  partner_id VARCHAR(255) NOT NULL,
  partner_type VARCHAR(50) NOT NULL,
  schedule_type VARCHAR(50) DEFAULT 'monthly_30_days',
  cycle_start_date DATE NOT NULL,
  cycle_end_date DATE NOT NULL,
  next_payout_date DATE NOT NULL,
  total_commissions DECIMAL(10,2) DEFAULT 0.0,
  total_payouts DECIMAL(10,2) DEFAULT 0.0,
  pending_amount DECIMAL(10,2) DEFAULT 0.0,
  status VARCHAR(50) DEFAULT 'active',
  created_at TIMESTAMP NOT NULL,
  updated_at TIMESTAMP NOT NULL
);
```

### **Enhanced Payouts Table:**
```sql
CREATE TABLE payouts (
  id VARCHAR(255) PRIMARY KEY,
  partner_id VARCHAR(255) NOT NULL,
  partner_type VARCHAR(50) NOT NULL,
  commission_ids TEXT[] NOT NULL,
  total_amount DECIMAL(10,2) NOT NULL,
  fees DECIMAL(10,2) DEFAULT 0.0,
  net_amount DECIMAL(10,2) NOT NULL,
  status VARCHAR(50) NOT NULL,
  payment_method VARCHAR(100) NOT NULL,
  payout_schedule VARCHAR(50) DEFAULT 'monthly',
  next_payout_date DATE,
  created_at TIMESTAMP NOT NULL,
  processed_at TIMESTAMP,
  completed_at TIMESTAMP
);
```

## 📈 **Partner Dashboard Features**

### **Real-Time Tracking:**
```dart
// Partner can see their earnings and next payout
final schedule = await payoutScheduler.getPartnerPayoutSchedule(partnerId);
final pendingAmount = await payoutScheduler.calculatePendingCommissions(partnerId);
final payoutHistory = await payoutScheduler.getPartnerPayoutHistory(partnerId);

// Display to partner:
print('Next Payout: ${schedule['next_payout_date']}');
print('Pending Earnings: \$${pendingAmount.toStringAsFixed(2)}');
print('Days Until Payout: ${_calculateDaysUntilPayout(schedule['next_payout_date'])}');
```

### **Payout History:**
```dart
// Show partner their payout history
for (final payout in payoutHistory) {
  print('${payout['created_at']}: \$${payout['net_amount']} (${payout['status']})');
}
```

## 🔧 **Admin Management**

### **Payout Management Dashboard:**
```dart
// Admin can see all pending payouts
final partnersDue = await payoutScheduler.getPartnersDueForPayout();

for (final partner in partnersDue) {
  final pendingAmount = await payoutScheduler.calculatePendingCommissions(partner['partner_id']);
  print('${partner['partner_id']}: \$${pendingAmount.toStringAsFixed(2)} due');
}
```

### **Manual Payout Processing:**
```dart
// Admin can manually trigger payouts
await payoutScheduler.processPartnerPayout(partnerId);
```

## 🧪 **Testing the System**

### **Test 30-Day Cycle:**
```bash
# Run comprehensive test
dart test_30_day_payouts.dart

# Expected output:
# ✅ Payout schedule created (30-day cycles)
# ✅ Commissions accumulated: $37.50
# ✅ Payout processed: $36.11 (after fees)
# ✅ Next cycle scheduled
```

### **Test Scenarios:**
1. **New Partner Registration:** Creates 30-day schedule
2. **Commission Accumulation:** Tracks earnings over 30 days
3. **Automatic Payout:** Processes on day 30
4. **Fee Calculation:** Applies Stripe fees correctly
5. **Cycle Renewal:** Sets up next 30-day period

## 📊 **Analytics & Reporting**

### **Partner Performance:**
```sql
-- Partner earnings over time
SELECT 
  DATE_TRUNC('month', created_at) as month,
  SUM(total_amount) as monthly_earnings,
  COUNT(*) as payout_count
FROM payouts 
WHERE partner_id = 'partner_wellness_guru'
GROUP BY month
ORDER BY month DESC;
```

### **System Metrics:**
```sql
-- Overall payout statistics
SELECT 
  COUNT(DISTINCT partner_id) as active_partners,
  SUM(total_amount) as total_payouts,
  AVG(total_amount) as avg_payout,
  SUM(fees) as total_fees
FROM payouts 
WHERE created_at >= NOW() - INTERVAL '30 days';
```

## 🚀 **Production Deployment**

### **Automated Scheduling:**
```bash
# Set up daily cron job for payout processing
0 9 * * * cd /path/to/app && dart run_payout_processor.dart
```

### **Monitoring:**
```dart
// Set up alerts for payout processing
if (payoutsFailed > 0) {
  await notificationService.alertAdmin(
    'Payout Processing Alert',
    '$payoutsFailed payouts failed to process'
  );
}
```

### **Backup & Recovery:**
```sql
-- Regular backup of payout data
pg_dump -t payouts -t payout_schedules -t commissions naturesplace_db > payout_backup.sql
```

## 💡 **Key Benefits**

### **For Partners:**
- ✅ **Predictable Income:** Know exactly when they'll be paid
- ✅ **Real-Time Tracking:** See earnings accumulate daily
- ✅ **Transparent Fees:** Clear breakdown of Stripe charges
- ✅ **Payment History:** Complete record of all payouts

### **For Your Business:**
- ✅ **Automated Processing:** No manual payout management
- ✅ **Cost Control:** Predictable Stripe fees
- ✅ **Cash Flow Management:** 30-day payment cycles
- ✅ **Compliance:** Complete audit trail

### **System Features:**
- ✅ **Fault Tolerance:** Handles payment failures gracefully
- ✅ **Scalability:** Processes thousands of partners automatically
- ✅ **Flexibility:** Easy to adjust payout schedules
- ✅ **Integration:** Works seamlessly with existing commission system

Your 30-day partner payout system is now production-ready! 🎉

Partners will automatically receive payouts every 30 days via Stripe, with full tracking, fee transparency, and automated cycle management.
