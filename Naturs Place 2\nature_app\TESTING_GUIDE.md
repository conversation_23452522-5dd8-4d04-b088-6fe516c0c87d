# 🧪 Testing Guide for PostgreSQL Integration

## 🎯 **Quick Start Testing**

Your PostgreSQL integration is ready! Follow these steps to test everything:

### **Step 1: Setup Environment**
```bash
# Option A: Run the setup script (Windows)
setup_environment.bat

# Option B: Set manually
export POSTGRES_HOST=localhost
export POSTGRES_PORT=5432
export POSTGRES_DB="Natures Place"
export POSTGRES_USER=postgres
export POSTGRES_PASSWORD=your_password_here
```

### **Step 2: Create Database**
```sql
-- Connect to PostgreSQL and run:
CREATE DATABASE "Natures Place"
    WITH
    OWNER = postgres
    ENCODING = 'UTF8'
    LC_COLLATE = 'English_Canada.1252'
    LC_CTYPE = 'English_Canada.1252'
    LOCALE_PROVIDER = 'libc'
    TABLESPACE = pg_default
    CONNECTION LIMIT = -1
    IS_TEMPLATE = False;
```

### **Step 3: Test Connection**
```bash
# Test basic PostgreSQL connection
dart test_postgresql_connection.dart
```

### **Step 4: Test Commission System**
```bash
# Test complete commission system with PostgreSQL
dart test_commission_system.dart
```

## 🔍 **Test Scripts Explained**

### **1. `test_postgresql_connection.dart`**
- ✅ Tests basic PostgreSQL connection
- ✅ Creates database tables automatically
- ✅ Inserts sample transaction and commission
- ✅ Verifies data insertion works

**Expected Output:**
```
🔍 Testing PostgreSQL Connection...
✅ PostgreSQL connection successful!
✅ Database tables created successfully!
✅ Test transaction inserted successfully!
✅ Test commission inserted successfully!
🎉 All PostgreSQL tests passed!
```

### **2. `test_commission_system.dart`**
- ✅ Tests complete commission workflow
- ✅ Creates vendor and processes transaction
- ✅ Calculates commissions automatically
- ✅ Generates analytics and payouts
- ✅ Syncs data to PostgreSQL

**Expected Output:**
```
💰 Testing Nature's Place Commission System
✅ Commission service initialized!
✅ Transaction processed successfully!
✅ Commission created successfully!
✅ Analytics retrieved
✅ Payout created successfully!
✅ Data synchronized to PostgreSQL successfully!
🎉 Commission System Test Complete!
```

## 🛠️ **Troubleshooting**

### **Connection Issues**
```
❌ PostgreSQL connection failed!
```
**Solutions:**
1. Check PostgreSQL is running: `pg_ctl status`
2. Verify database exists: `psql -l | grep "Natures Place"`
3. Test credentials: `psql -h localhost -U postgres -d "Natures Place"`
4. Check firewall settings

### **Permission Issues**
```
❌ permission denied for database
```
**Solutions:**
1. Grant permissions: `GRANT ALL PRIVILEGES ON DATABASE "Natures Place" TO postgres;`
2. Check user exists: `\du` in psql
3. Create user if needed: `CREATE USER postgres WITH PASSWORD 'your_password';`

### **Table Creation Issues**
```
❌ Failed to create PostgreSQL tables
```
**Solutions:**
1. Check database connection
2. Verify user has CREATE permissions
3. Check database encoding is UTF8

## 📊 **What Gets Tested**

### **Database Operations**
- ✅ Connection establishment
- ✅ Table creation with proper schema
- ✅ Transaction insertion with conflict resolution
- ✅ Commission calculation and storage
- ✅ Payout creation and management
- ✅ Data synchronization between SQLite and PostgreSQL

### **Commission Logic**
- ✅ Vendor commission calculation (15% default)
- ✅ Affiliate commission calculation (if applicable)
- ✅ App revenue calculation (remaining amount)
- ✅ Commission status management
- ✅ Payout processing

### **Data Integrity**
- ✅ Foreign key relationships
- ✅ Data validation
- ✅ Conflict resolution (ON CONFLICT DO UPDATE)
- ✅ Transaction atomicity
- ✅ Error handling and recovery

## 🎯 **Production Readiness Checklist**

After successful testing, verify:

- [ ] ✅ PostgreSQL connection works
- [ ] ✅ All tables created successfully
- [ ] ✅ Transactions process correctly
- [ ] ✅ Commissions calculate accurately
- [ ] ✅ Payouts create properly
- [ ] ✅ Data syncs to PostgreSQL
- [ ] ✅ Error handling works
- [ ] ✅ Offline mode functions
- [ ] ✅ Analytics generate correctly

## 🚀 **Next Steps After Testing**

1. **Production Setup:**
   - Configure production PostgreSQL server
   - Set up SSL connections
   - Configure backup strategy

2. **Monitoring:**
   - Set up database monitoring
   - Configure alerting for connection failures
   - Monitor commission accuracy

3. **Scaling:**
   - Consider connection pooling for high traffic
   - Implement read replicas for analytics
   - Set up automated backups

4. **Security:**
   - Use environment variables for credentials
   - Enable SSL/TLS connections
   - Implement proper user permissions

## 📞 **Support**

If tests fail or you need help:

1. **Check the logs** - All operations are logged with detailed messages
2. **Verify configuration** - Use the setup scripts to ensure correct settings
3. **Test incrementally** - Start with connection test, then move to full system
4. **Check dependencies** - Ensure all packages are installed correctly

Your PostgreSQL integration is production-ready once all tests pass! 🎉
