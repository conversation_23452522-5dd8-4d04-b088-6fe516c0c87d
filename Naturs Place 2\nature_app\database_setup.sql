-- Database setup for "Natures Place" PostgreSQL database
-- This script creates the necessary tables for the commission system

-- Create database (run this separately if needed)
-- CREATE DATABASE "Natures Place"
--     WITH
--     OWNER = postgres
--     ENCODING = 'UTF8'
--     LC_COLLATE = 'English_Canada.1252'
--     LC_CTYPE = 'English_Canada.1252'
--     LOCALE_PROVIDER = 'libc'
--     TABLESPACE = pg_default
--     CONNECTION LIMIT = -1
--     IS_TEMPLATE = False;

-- Connect to the database before running the following commands
-- \c "Natures Place"

-- Create transactions table
CREATE TABLE IF NOT EXISTS transactions (
    id VARCHAR(255) PRIMARY KEY,
    order_id VARCHAR(255) NOT NULL,
    customer_id VARCHAR(255) NOT NULL,
    vendor_id VARCHAR(255) NOT NULL,
    affiliate_id VARCHAR(255),
    product_id VARCHAR(255) NOT NULL,
    product_name VARCHAR(500) NOT NULL,
    category VARCHAR(100) NOT NULL,
    product_price DECIMAL(10,2) NOT NULL,
    quantity INTEGER NOT NULL,
    subtotal DECIMAL(10,2) NOT NULL,
    tax DECIMAL(10,2) NOT NULL,
    shipping DECIMAL(10,2) NOT NULL,
    total DECIMAL(10,2) NOT NULL,
    discount DECIMAL(10,2) DEFAULT 0.0,
    coupon_code VARCHAR(100),
    status VARCHAR(50) NOT NULL,
    created_at TIMESTAMP NOT NULL,
    completed_at TIMESTAMP,
    cancelled_at TIMESTAMP,
    cancellation_reason TEXT,
    metadata JSONB NOT NULL DEFAULT '{}'::jsonb
);

-- Create commissions table
CREATE TABLE IF NOT EXISTS commissions (
    id VARCHAR(255) PRIMARY KEY,
    transaction_id VARCHAR(255) NOT NULL,
    partner_id VARCHAR(255) NOT NULL,
    partner_type VARCHAR(50) NOT NULL,
    category VARCHAR(100) NOT NULL,
    sale_amount DECIMAL(10,2) NOT NULL,
    commission_rate DECIMAL(5,2) NOT NULL,
    commission_amount DECIMAL(10,2) NOT NULL,
    bonus_amount DECIMAL(10,2) DEFAULT 0.0,
    total_amount DECIMAL(10,2) NOT NULL,
    app_revenue DECIMAL(10,2) NOT NULL,
    status VARCHAR(50) NOT NULL,
    created_at TIMESTAMP NOT NULL,
    approved_at TIMESTAMP,
    paid_at TIMESTAMP,
    payout_id VARCHAR(255),
    notes TEXT,
    metadata JSONB NOT NULL DEFAULT '{}'::jsonb,
    FOREIGN KEY (transaction_id) REFERENCES transactions (id)
);

-- Create payouts table
CREATE TABLE IF NOT EXISTS payouts (
    id VARCHAR(255) PRIMARY KEY,
    partner_id VARCHAR(255) NOT NULL,
    partner_type VARCHAR(50) NOT NULL,
    commission_ids TEXT[] NOT NULL,
    total_amount DECIMAL(10,2) NOT NULL,
    fees DECIMAL(10,2) DEFAULT 0.0,
    net_amount DECIMAL(10,2) NOT NULL,
    status VARCHAR(50) NOT NULL,
    payment_method VARCHAR(100) NOT NULL,
    payment_reference VARCHAR(255),
    created_at TIMESTAMP NOT NULL,
    processed_at TIMESTAMP,
    completed_at TIMESTAMP,
    failure_reason TEXT,
    payment_details JSONB NOT NULL DEFAULT '{}'::jsonb,
    metadata JSONB NOT NULL DEFAULT '{}'::jsonb
);

-- Create vendors table
CREATE TABLE IF NOT EXISTS vendors (
    id VARCHAR(255) PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255) NOT NULL UNIQUE,
    phone VARCHAR(50),
    business_name VARCHAR(255) NOT NULL,
    business_address TEXT,
    tax_id VARCHAR(100),
    vendor_type VARCHAR(50) NOT NULL,
    status VARCHAR(50) NOT NULL,
    tier VARCHAR(50) NOT NULL,
    join_date TIMESTAMP NOT NULL,
    verification_date TIMESTAMP,
    categories TEXT[] NOT NULL,
    commission_rates JSONB NOT NULL DEFAULT '{}'::jsonb,
    monthly_listing_fee DECIMAL(10,2) DEFAULT 0.0,
    is_featured BOOLEAN DEFAULT FALSE,
    total_sales DECIMAL(10,2) DEFAULT 0.0,
    total_commission_paid DECIMAL(10,2) DEFAULT 0.0,
    total_orders INTEGER DEFAULT 0,
    average_rating DECIMAL(3,2) DEFAULT 0.0,
    certifications TEXT[],
    logo_url VARCHAR(500),
    description TEXT,
    metadata JSONB NOT NULL DEFAULT '{}'::jsonb
);

-- Create affiliates table
CREATE TABLE IF NOT EXISTS affiliates (
    id VARCHAR(255) PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255) NOT NULL UNIQUE,
    phone VARCHAR(50),
    referral_code VARCHAR(100) NOT NULL UNIQUE,
    tier VARCHAR(50) NOT NULL,
    join_date TIMESTAMP NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    commission_rates JSONB NOT NULL DEFAULT '{}'::jsonb,
    total_earnings DECIMAL(10,2) DEFAULT 0.0,
    total_referrals INTEGER DEFAULT 0,
    conversion_rate DECIMAL(5,2) DEFAULT 0.0,
    social_media_links JSONB DEFAULT '{}'::jsonb,
    bio TEXT,
    profile_image_url VARCHAR(500),
    metadata JSONB NOT NULL DEFAULT '{}'::jsonb
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_transactions_vendor_id ON transactions(vendor_id);
CREATE INDEX IF NOT EXISTS idx_transactions_affiliate_id ON transactions(affiliate_id);
CREATE INDEX IF NOT EXISTS idx_transactions_status ON transactions(status);
CREATE INDEX IF NOT EXISTS idx_transactions_created_at ON transactions(created_at);

CREATE INDEX IF NOT EXISTS idx_commissions_partner_id ON commissions(partner_id);
CREATE INDEX IF NOT EXISTS idx_commissions_status ON commissions(status);
CREATE INDEX IF NOT EXISTS idx_commissions_transaction_id ON commissions(transaction_id);
CREATE INDEX IF NOT EXISTS idx_commissions_created_at ON commissions(created_at);

CREATE INDEX IF NOT EXISTS idx_payouts_partner_id ON payouts(partner_id);
CREATE INDEX IF NOT EXISTS idx_payouts_status ON payouts(status);
CREATE INDEX IF NOT EXISTS idx_payouts_created_at ON payouts(created_at);

CREATE INDEX IF NOT EXISTS idx_vendors_email ON vendors(email);
CREATE INDEX IF NOT EXISTS idx_vendors_status ON vendors(status);
CREATE INDEX IF NOT EXISTS idx_vendors_type ON vendors(vendor_type);

CREATE INDEX IF NOT EXISTS idx_affiliates_email ON affiliates(email);
CREATE INDEX IF NOT EXISTS idx_affiliates_referral_code ON affiliates(referral_code);
CREATE INDEX IF NOT EXISTS idx_affiliates_active ON affiliates(is_active);

-- Create views for analytics
CREATE OR REPLACE VIEW commission_analytics AS
SELECT 
    COUNT(*) as total_commissions,
    SUM(total_amount) as total_commission_amount,
    SUM(CASE WHEN status = 'paid' THEN total_amount ELSE 0 END) as paid_commissions,
    SUM(CASE WHEN status = 'pending' THEN total_amount ELSE 0 END) as pending_commissions,
    SUM(CASE WHEN status = 'approved' THEN total_amount ELSE 0 END) as approved_commissions,
    SUM(app_revenue) as total_app_revenue,
    AVG(commission_rate) as average_commission_rate
FROM commissions;

CREATE OR REPLACE VIEW vendor_performance AS
SELECT 
    v.id,
    v.name,
    v.business_name,
    v.status,
    v.tier,
    COUNT(t.id) as total_transactions,
    SUM(t.total) as total_sales,
    SUM(c.commission_amount) as total_commissions_owed,
    AVG(c.commission_rate) as average_commission_rate
FROM vendors v
LEFT JOIN transactions t ON v.id = t.vendor_id
LEFT JOIN commissions c ON t.id = c.transaction_id AND c.partner_type = 'vendor'
GROUP BY v.id, v.name, v.business_name, v.status, v.tier;

CREATE OR REPLACE VIEW affiliate_performance AS
SELECT 
    a.id,
    a.name,
    a.email,
    a.tier,
    a.is_active,
    COUNT(t.id) as total_referrals,
    SUM(t.total) as total_sales_generated,
    SUM(c.total_amount) as total_earnings,
    AVG(c.commission_rate) as average_commission_rate
FROM affiliates a
LEFT JOIN transactions t ON a.id = t.affiliate_id
LEFT JOIN commissions c ON t.id = c.transaction_id AND c.partner_type = 'affiliate'
GROUP BY a.id, a.name, a.email, a.tier, a.is_active;

-- Insert sample data (optional)
-- You can uncomment and modify these to add test data

-- INSERT INTO vendors (id, name, email, business_name, vendor_type, status, tier, join_date, commission_rates) VALUES
-- ('vendor_001', 'Natural Health Co', '<EMAIL>', 'Natural Health Company', 'regular', 'verified', 'tier2', NOW(), '{"supplements": 10.0, "essentialOils": 15.0}'::jsonb);

-- INSERT INTO affiliates (id, name, email, referral_code, tier, join_date, commission_rates) VALUES
-- ('affiliate_001', 'Health Blogger', '<EMAIL>', 'HEALTH2024', 'tier1', NOW(), '{"supplements": 5.0, "essentialOils": 8.0}'::jsonb);

COMMENT ON DATABASE "Natures Place" IS 'Database for Nature''s Place app commission and vendor management system';
COMMENT ON TABLE transactions IS 'Stores all transaction records for commission calculation';
COMMENT ON TABLE commissions IS 'Stores commission records for vendors and affiliates';
COMMENT ON TABLE payouts IS 'Stores payout records for batch payments';
COMMENT ON TABLE vendors IS 'Stores vendor information and settings';
COMMENT ON TABLE affiliates IS 'Stores affiliate partner information';
