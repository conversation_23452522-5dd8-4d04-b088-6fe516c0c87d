import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:provider/provider.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import '../providers/app_state.dart';
import '../providers/theme_provider.dart';
import '../services/auth_service.dart';
import '../services/admin_service.dart';
import '../services/daily_plant_update_service.dart';
import '../models/user_models.dart';
import 'help_centre_screen.dart';
import 'debug_performance_screen.dart';

class SettingsScreen extends StatefulWidget {
  const SettingsScreen({super.key});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  bool _notificationsEnabled = true;
  bool _emailNotifications = true;
  bool _pushNotifications = true;
  bool _autoSync = true;
  String _language = 'English';
  String _region = 'United States';

  // Debug options
  final bool _debugMode = kDebugMode;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Settings'),
      ),
      body: ListView(
        padding: const EdgeInsets.all(16.0),
        children: [
          // Account Section
          _buildSectionHeader('Account'),
          Card(
            child: Column(
              children: [
                Consumer<AuthService>(
                  builder: (context, authService, child) {
                    return ListTile(
                      leading: const Icon(Icons.person),
                      title: const Text('Profile'),
                      subtitle: Text(authService.currentUser != null ? '${authService.currentUser!.firstName} ${authService.currentUser!.lastName}'.trim() : 'Not logged in'),
                      trailing: const Icon(Icons.chevron_right),
                      onTap: () {
                        Navigator.of(context).pushNamed('/profile');
                      },
                    );
                  },
                ),
                const Divider(height: 1),
                Consumer<AppState>(
                  builder: (context, appState, child) {
                    return ListTile(
                      leading: const Icon(Icons.star),
                      title: const Text('Subscription'),
                      subtitle: Text('${appState.subscriptionTier.name.toUpperCase()} Plan'),
                      trailing: const Icon(Icons.chevron_right),
                      onTap: () {
                        _showSubscriptionDialog();
                      },
                    );
                  },
                ),
                const Divider(height: 1),
                ListTile(
                  leading: const Icon(Icons.security),
                  title: const Text('Privacy & Security'),
                  trailing: const Icon(Icons.chevron_right),
                  onTap: () {
                    _showPrivacyDialog();
                  },
                ),
              ],
            ),
          ),
          
          const SizedBox(height: 24),
          
          // Notifications Section
          _buildSectionHeader('Notifications'),
          Card(
            child: Column(
              children: [
                SwitchListTile(
                  secondary: const Icon(Icons.notifications),
                  title: const Text('Enable Notifications'),
                  subtitle: const Text('Receive app notifications'),
                  value: _notificationsEnabled,
                  onChanged: (value) {
                    setState(() {
                      _notificationsEnabled = value;
                      if (!value) {
                        _emailNotifications = false;
                        _pushNotifications = false;
                      }
                    });
                  },
                ),
                const Divider(height: 1),
                SwitchListTile(
                  secondary: const Icon(Icons.email),
                  title: const Text('Email Notifications'),
                  subtitle: const Text('Receive notifications via email'),
                  value: _emailNotifications,
                  onChanged: _notificationsEnabled ? (value) {
                    setState(() {
                      _emailNotifications = value;
                    });
                  } : null,
                ),
                const Divider(height: 1),
                SwitchListTile(
                  secondary: const Icon(Icons.phone_android),
                  title: const Text('Push Notifications'),
                  subtitle: const Text('Receive push notifications'),
                  value: _pushNotifications,
                  onChanged: _notificationsEnabled ? (value) {
                    setState(() {
                      _pushNotifications = value;
                    });
                  } : null,
                ),
              ],
            ),
          ),
          
          const SizedBox(height: 24),
          
          // App Preferences Section
          _buildSectionHeader('App Preferences'),
          Card(
            child: Column(
              children: [
                Consumer<ThemeProvider>(
                  builder: (context, themeProvider, child) {
                    return SwitchListTile(
                      secondary: const Icon(Icons.dark_mode),
                      title: const Text('Dark Mode'),
                      subtitle: const Text('Use dark theme'),
                      value: themeProvider.isDarkMode,
                      onChanged: (value) {
                        themeProvider.toggleTheme();
                      },
                    );
                  },
                ),
                const Divider(height: 1),
                SwitchListTile(
                  secondary: const Icon(Icons.sync),
                  title: const Text('Auto Sync'),
                  subtitle: const Text('Automatically sync data'),
                  value: _autoSync,
                  onChanged: (value) {
                    setState(() {
                      _autoSync = value;
                    });
                  },
                ),
                const Divider(height: 1),
                ListTile(
                  leading: const Icon(Icons.language),
                  title: const Text('Language'),
                  subtitle: Text(_language),
                  trailing: const Icon(Icons.chevron_right),
                  onTap: () {
                    _showLanguageDialog();
                  },
                ),
                const Divider(height: 1),
                ListTile(
                  leading: const Icon(Icons.location_on),
                  title: const Text('Region'),
                  subtitle: Text(_region),
                  trailing: const Icon(Icons.chevron_right),
                  onTap: () {
                    _showRegionDialog();
                  },
                ),
              ],
            ),
          ),

          const SizedBox(height: 24),

          // Daily Plant Updates Section
          _buildSectionHeader('Daily Plant Updates'),
          Consumer<AdminService>(
            builder: (context, adminService, child) {
              return Card(
                child: Column(
                  children: [
                    ListTile(
                      leading: const FaIcon(FontAwesomeIcons.seedling, color: Color(0xFF22c55e)),
                      title: const Text('Plant Update Status'),
                      subtitle: const Text('View daily healing plant additions'),
                      trailing: const Icon(Icons.chevron_right),
                      onTap: () {
                        _showPlantUpdateStatusDialog();
                      },
                    ),
                    // Only show Force Update for admins
                    if (adminService.canAccessForceUpdate()) ...[
                      const Divider(height: 1),
                      ListTile(
                        leading: const Icon(Icons.refresh, color: Colors.red),
                        title: const Text('Force Update'),
                        subtitle: const Text('Admin: Manually trigger plant update'),
                        trailing: const Icon(Icons.chevron_right),
                        onTap: () {
                          _forceUpdatePlants();
                        },
                      ),
                    ],
                    const Divider(height: 1),
                    ListTile(
                      leading: const Icon(Icons.schedule),
                      title: const Text('Update Schedule'),
                      subtitle: const Text('Daily at midnight'),
                      trailing: const Icon(Icons.info_outline),
                      onTap: () {
                        _showUpdateScheduleDialog();
                      },
                    ),
                  ],
                ),
              );
            },
          ),

          const SizedBox(height: 24),

          // Data & Storage Section
          _buildSectionHeader('Data & Storage'),
          Card(
            child: Column(
              children: [
                ListTile(
                  leading: const Icon(Icons.clear_all),
                  title: const Text('Clear Cache'),
                  subtitle: const Text('Free up storage space'),
                  trailing: const Icon(Icons.chevron_right),
                  onTap: () {
                    _showClearCacheDialog();
                  },
                ),
              ],
            ),
          ),

          const SizedBox(height: 24),

          // Debug Section (only in debug mode)
          if (_debugMode) ...[
            _buildSectionHeader('Debug Tools'),
            Card(
              child: ListTile(
                leading: const Icon(Icons.developer_mode, color: Colors.orange),
                title: const Text('Performance Tools'),
                subtitle: const Text('FPS monitor, performance overlay & debug info'),
                trailing: const Icon(Icons.chevron_right),
                onTap: () {
                  Navigator.of(context).push(
                    MaterialPageRoute(
                      builder: (context) => const DebugPerformanceScreen(),
                    ),
                  );
                },
              ),
            ),
            const SizedBox(height: 24),
          ],

          // Role-Specific Dashboard Section
          Consumer<AuthService>(
            builder: (context, authService, child) {
              if (authService.currentUser == null) {
                return _buildBusinessPartnersSection();
              }

              final user = authService.currentUser!;
              if (user.role == UserRole.vendor) {
                return _buildVendorDashboardSection();
              } else if (user.role == UserRole.partner) {
                return _buildPartnerDashboardSection();
              } else {
                return _buildBusinessPartnersSection();
              }
            },
          ),

          const SizedBox(height: 24),

          // Support Section
          _buildSectionHeader('Support'),
          Card(
            child: Column(
              children: [
                ListTile(
                  leading: const Icon(Icons.help),
                  title: const Text('Help Center'),
                  trailing: const Icon(Icons.chevron_right),
                  onTap: () {
                    Navigator.of(context).push(
                      MaterialPageRoute(
                        builder: (context) => const HelpCentreScreen(),
                      ),
                    );
                  },
                ),
                const Divider(height: 1),
                ListTile(
                  leading: const Icon(Icons.feedback),
                  title: const Text('Send Feedback'),
                  trailing: const Icon(Icons.chevron_right),
                  onTap: () {
                    _showFeedbackDialog();
                  },
                ),
                const Divider(height: 1),
                ListTile(
                  leading: const Icon(Icons.info),
                  title: const Text('About'),
                  trailing: const Icon(Icons.chevron_right),
                  onTap: () {
                    _showAboutDialog();
                  },
                ),
              ],
            ),
          ),
          
          const SizedBox(height: 32),
          
          // Version Info
          Center(
            child: Text(
              'Version 1.0.0',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Colors.grey[600],
              ),
            ),
          ),
          
          const SizedBox(height: 16),
        ],
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: Text(
        title,
        style: Theme.of(context).textTheme.titleMedium?.copyWith(
          fontWeight: FontWeight.bold,
          color: const Color(0xFF22c55e),
        ),
      ),
    );
  }

  void _showSubscriptionDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Subscription'),
        content: Consumer<AppState>(
          builder: (context, appState, child) {
            return Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('Current Plan: ${appState.subscriptionTier.name.toUpperCase()}'),
                const SizedBox(height: 16),
                if (!appState.isPremium) ...[
                  const Text('Upgrade to Premium or Pro to unlock:'),
                  const SizedBox(height: 8),
                  const Text('• Plant scanning'),
                  const Text('• Unlimited AI assistant'),
                  const Text('• Full plant encyclopedia'),
                  const Text('• Disease detection (Pro only)'),
                ],
              ],
            );
          },
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
          Consumer<AppState>(
            builder: (context, appState, child) {
              if (!appState.isPremium) {
                return ElevatedButton(
                  onPressed: () {
                    Navigator.of(context).pop();
                    // TODO: Navigate to subscription screen
                  },
                  child: const Text('Upgrade'),
                );
              }
              return const SizedBox.shrink();
            },
          ),
        ],
      ),
    );
  }

  void _showPrivacyDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Privacy & Security'),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Your privacy is important to us.'),
            SizedBox(height: 16),
            Text('• We encrypt all your personal data'),
            Text('• We never share your information'),
            Text('• You can delete your account anytime'),
            Text('• All plant data is stored securely'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _showLanguageDialog() {
    final languages = ['English', 'Spanish', 'French', 'German', 'Italian'];
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Select Language'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: languages.map((lang) => RadioListTile<String>(
            title: Text(lang),
            value: lang,
            groupValue: _language,
            onChanged: (value) {
              setState(() {
                _language = value!;
              });
              Navigator.of(context).pop();
              if (lang != 'English') {
                _showFeatureComingSoon('Multi-language support');
              }
            },
          )).toList(),
        ),
      ),
    );
  }

  void _showRegionDialog() {
    final regions = [
      'United States',
      'Canada',
      'United Kingdom',
      'Australia',
      'Germany',
      'France',
      'Spain',
      'Italy',
      'Netherlands',
      'Belgium',
      'Switzerland',
      'Austria',
      'Sweden',
      'Norway',
      'Denmark',
      'Finland',
      'Ireland',
      'Portugal',
      'Japan',
      'South Korea',
      'Singapore',
      'Hong Kong',
      'New Zealand',
      'South Africa',
      'Brazil',
      'Mexico',
      'Argentina',
      'Chile',
      'India',
      'Thailand',
      'Malaysia',
      'Philippines',
      'Indonesia',
      'Vietnam',
      'Other'
    ];

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Select Region'),
        content: SizedBox(
          width: double.maxFinite,
          height: 400,
          child: ListView(
            children: regions.map((region) => RadioListTile<String>(
              title: Text(region),
              value: region,
              groupValue: _region,
              onChanged: (value) {
                setState(() {
                  _region = value!;
                });
                Navigator.of(context).pop();
              },
            )).toList(),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }



  void _showClearCacheDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Clear Cache'),
        content: const Text('This will clear cached images and temporary data. Your favorites and chat history will be preserved.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Cache cleared successfully'),
                  backgroundColor: Color(0xFF22c55e),
                ),
              );
            },
            child: const Text('Clear'),
          ),
        ],
      ),
    );
  }

  void _showFeedbackDialog() {
    final feedbackController = TextEditingController();
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Send Feedback'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('We value your feedback! Let us know how we can improve.'),
            const SizedBox(height: 16),
            TextField(
              controller: feedbackController,
              maxLines: 4,
              decoration: const InputDecoration(
                hintText: 'Enter your feedback here...',
                border: OutlineInputBorder(),
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Thank you for your feedback!'),
                  backgroundColor: Color(0xFF22c55e),
                ),
              );
            },
            child: const Text('Send'),
          ),
        ],
      ),
    );
  }

  void _showAboutDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            FaIcon(
              FontAwesomeIcons.leaf,
              color: Color(0xFF22c55e),
              size: 24,
            ),
            SizedBox(width: 8),
            Text("Nature's Place"),
          ],
        ),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Version 1.0.0',
              style: TextStyle(fontWeight: FontWeight.w600),
            ),
            SizedBox(height: 16),
            Text('Your trusted companion for natural healing and plant knowledge.'),
            SizedBox(height: 16),
            Text('Discover the power of nature with our comprehensive plant encyclopedia, AI assistant, and marketplace for natural remedies.'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _showFeatureComingSoon(String feature) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('$feature coming soon!'),
        backgroundColor: Colors.orange,
      ),
    );
  }

  void _showPlantUpdateStatusDialog() {
    final updateService = DailyPlantUpdateService();
    final stats = updateService.getStatistics();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            FaIcon(FontAwesomeIcons.seedling, color: Color(0xFF22c55e), size: 20),
            SizedBox(width: 8),
            Text('Plant Update Status'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildStatRow('Service Status', stats['isServiceRunning'] ? 'Active' : 'Inactive'),
            _buildStatRow('Total Plants Added', '${stats['totalPlantsAdded']}'),
            _buildStatRow('Today\'s Plants', '${stats['todaysPlants']}'),
            _buildStatRow('Total Plants', '${stats['totalPlants']}'),
            if (stats['lastUpdateDate'] != null)
              _buildStatRow('Last Update', _formatDate(stats['lastUpdateDate'])),
            if (updateService.nextUpdateTime != null)
              _buildStatRow('Next Update', _formatDate(updateService.nextUpdateTime!)),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  Widget _buildStatRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label, style: const TextStyle(fontWeight: FontWeight.w500)),
          Text(value),
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year} ${date.hour.toString().padLeft(2, '0')}:${date.minute.toString().padLeft(2, '0')}';
  }

  void _forceUpdatePlants() async {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const AlertDialog(
        content: Row(
          children: [
            CircularProgressIndicator(),
            SizedBox(width: 16),
            Text('Updating plants...'),
          ],
        ),
      ),
    );

    try {
      final updateService = DailyPlantUpdateService();
      await updateService.forceUpdate();

      if (mounted) {
        Navigator.of(context).pop(); // Close loading dialog

        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Plants updated successfully!'),
            backgroundColor: Color(0xFF22c55e),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        Navigator.of(context).pop(); // Close loading dialog

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Update failed: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _showUpdateScheduleDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Update Schedule'),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Daily Plant Updates are scheduled to run:'),
            SizedBox(height: 16),
            Text('• Every day at midnight'),
            Text('• Automatically when app starts'),
            Text('• 3-5 new healing plants added daily'),
            Text('• Plants sourced from traditional medicine databases'),
            SizedBox(height: 16),
            Text('The service runs in the background to ensure you always have access to new healing plants and herbs.'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  /// Build business partners section for non-logged-in users
  Widget _buildBusinessPartnersSection() {
    return Column(
      children: [
        _buildSectionHeader('Business Partners'),
        Card(
          child: Column(
            children: [
              ListTile(
                leading: const FaIcon(FontAwesomeIcons.store, color: Color(0xFF22c55e)),
                title: const Text('Vendor Portal'),
                subtitle: const Text('Access vendor dashboard'),
                trailing: const Icon(Icons.chevron_right),
                onTap: () {
                  Navigator.of(context).pushNamed('/vendor-login');
                },
              ),
              const Divider(height: 1),
              ListTile(
                leading: const FaIcon(FontAwesomeIcons.handshake, color: Colors.blue),
                title: const Text('Partner Portal'),
                subtitle: const Text('Access affiliate dashboard'),
                trailing: const Icon(Icons.chevron_right),
                onTap: () {
                  Navigator.of(context).pushNamed('/affiliate-login');
                },
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// Build vendor dashboard section for logged-in vendors
  Widget _buildVendorDashboardSection() {
    return Column(
      children: [
        _buildSectionHeader('Vendor Dashboard'),
        Card(
          child: ListTile(
            leading: const FaIcon(FontAwesomeIcons.store, color: Color(0xFF22c55e)),
            title: const Text('Vendor Dashboard'),
            subtitle: const Text('Manage products, orders, and analytics'),
            trailing: const Icon(Icons.chevron_right),
            onTap: () {
              Navigator.of(context).pushNamed('/vendor-dashboard');
            },
          ),
        ),
      ],
    );
  }

  /// Build partner dashboard section for logged-in partners
  Widget _buildPartnerDashboardSection() {
    return Column(
      children: [
        _buildSectionHeader('Partner Dashboard'),
        Card(
          child: ListTile(
            leading: const FaIcon(FontAwesomeIcons.handshake, color: Colors.blue),
            title: const Text('Partner Dashboard'),
            subtitle: const Text('Track commissions and referrals'),
            trailing: const Icon(Icons.chevron_right),
            onTap: () {
              Navigator.of(context).pushNamed('/partner-dashboard');
            },
          ),
        ),
      ],
    );
  }
}
