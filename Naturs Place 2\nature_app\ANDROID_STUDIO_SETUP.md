# Android Studio Setup Guide for Nature's Place App

## 🚀 Quick Setup for Android Studio

### Prerequisites
- Android Studio (latest version)
- Flutter SDK installed and configured
- Android SDK and emulator set up

### Step 1: Open Project in Android Studio

1. **Open Android Studio**
2. **Select "Open an existing project"**
3. **Navigate to**: `D:\Healing App\Naturs Place 2\nature_app`
4. **Click "Open"**

### Step 2: Configure Flutter SDK

1. Go to **File → Settings** (or **Android Studio → Preferences** on Mac)
2. Navigate to **Languages & Frameworks → Flutter**
3. Set Flutter SDK path (usually `C:\flutter` or where you installed Flutter)
4. Click **Apply** and **OK**

### Step 3: Resolve Dependencies

```bash
# In Android Studio terminal or external terminal
cd "D:\Healing App\Naturs Place 2\nature_app"
flutter clean
flutter pub get
```

### Step 4: Fix Kotlin Compilation Issues (if any)

If you encounter Kotlin compilation errors:

```bash
# Clean all caches
flutter clean
cd android
./gradlew clean
cd ..
flutter pub get
```

### Step 5: Run the App

#### Option A: Using Android Studio UI
1. Select your target device (emulator or physical device)
2. Click the **Run** button (green play icon)
3. Or press **Shift + F10**

#### Option B: Using Terminal
```bash
# For Android device/emulator
flutter run

# For web (Chrome)
flutter run -d chrome

# For specific device
flutter devices  # List available devices
flutter run -d <device-id>
```

## 🔧 Troubleshooting Common Issues

### Issue 1: Kotlin Compilation Errors
**Solution:**
```bash
flutter clean
cd android
./gradlew clean
./gradlew build --refresh-dependencies
cd ..
flutter pub get
```

### Issue 2: Gradle Sync Issues
**Solution:**
1. In Android Studio: **File → Sync Project with Gradle Files**
2. Or manually: `cd android && ./gradlew sync`

### Issue 3: Flutter SDK Not Found
**Solution:**
1. **File → Settings → Languages & Frameworks → Flutter**
2. Set correct Flutter SDK path
3. Restart Android Studio

### Issue 4: Android SDK Issues
**Solution:**
1. **Tools → SDK Manager**
2. Install required Android SDK versions
3. Accept all licenses: `flutter doctor --android-licenses`

## 📱 Device Configuration

### Android Emulator
1. **Tools → AVD Manager**
2. **Create Virtual Device**
3. Choose **Pixel 7 Pro** or similar
4. Select **API Level 34** (Android 14)
5. Click **Finish**

### Physical Device
1. Enable **Developer Options** on your Android device
2. Enable **USB Debugging**
3. Connect via USB
4. Accept debugging permissions

## 🌐 Web Development

### Running on Web
```bash
# Start web development server
flutter run -d chrome --web-port 8080

# Build for web
flutter build web
```

### Web Features Available
- ✅ All vendor/partner/affiliate dashboards
- ✅ Mobile-first responsive design
- ✅ PWA capabilities
- ✅ Crypto payment infrastructure ready
- ✅ Enhanced security features

## 🔒 Security & Crypto Features

### Crypto Payment Integration Ready
The app includes a complete cryptocurrency payment infrastructure:

- **Supported Currencies**: BTC, ETH, USDT, BNB
- **Payment Processing**: Secure address generation and verification
- **Security**: SHA-256 hashing and secure token management
- **Cross-platform**: Works on both mobile and web

### Security Features
- Enhanced authentication with rate limiting
- Secure session management
- Input validation and sanitization
- Cross-platform security measures

## 📊 Project Structure

```
nature_app/
├── lib/
│   ├── main.dart                 # App entry point
│   ├── screens/                  # UI screens
│   │   ├── vendor_login_screen.dart
│   │   ├── affiliate_login_screen.dart
│   │   └── *_dashboard_screen.dart
│   ├── services/                 # Business logic
│   │   ├── crypto_payment_service.dart
│   │   ├── auth_service.dart
│   │   └── performance_optimization_service.dart
│   └── widgets/                  # Reusable components
├── android/                      # Android-specific code
├── web/                         # Web-specific assets
└── pubspec.yaml                 # Dependencies
```

## 🚀 Key Features

### Mobile App Features
- 🌿 Plant identification and scanning
- 📚 Comprehensive plant encyclopedia
- 🤖 AI-powered health assistant
- 🛒 Marketplace with vendor/affiliate support
- 💰 Crypto payment ready
- 📊 Analytics and reporting dashboards

### Web App Features
- 🎨 Mobile-first responsive design
- 📱 PWA installation capability
- 🔒 Enhanced security measures
- 💳 Crypto payment infrastructure
- 🌐 Cross-platform compatibility

## 🔄 Development Workflow

### Daily Development
1. **Pull latest changes** (if using version control)
2. **Run `flutter pub get`** to update dependencies
3. **Start development server**: `flutter run`
4. **Hot reload**: Press `r` in terminal or use Android Studio hot reload

### Before Committing
1. **Run tests**: `flutter test`
2. **Check code quality**: `flutter analyze`
3. **Format code**: `flutter format .`
4. **Build for release**: `flutter build apk --release`

## 📞 Support

### Common Commands
```bash
# Check Flutter installation
flutter doctor

# List connected devices
flutter devices

# Run app with verbose output
flutter run -v

# Build release APK
flutter build apk --release

# Build web version
flutter build web
```

### Getting Help
- Check **Flutter Doctor**: `flutter doctor`
- Review **Android Studio logs** in the bottom panel
- Check **Flutter documentation**: https://flutter.dev/docs
- Review **pubspec.yaml** for dependency conflicts

## ✅ Verification Checklist

- [ ] Flutter SDK configured in Android Studio
- [ ] Android SDK and emulator set up
- [ ] Dependencies resolved (`flutter pub get`)
- [ ] App runs successfully on emulator
- [ ] Web version accessible at localhost:8080
- [ ] Vendor/Partner/Affiliate dashboards functional
- [ ] No compilation errors in Android Studio

The app is now ready for development in Android Studio with full cross-platform support, enhanced security, and crypto payment capabilities!
