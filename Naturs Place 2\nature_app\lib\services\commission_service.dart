import 'package:flutter/foundation.dart';
import '../models/commission_models.dart';
import '../models/vendor_models.dart';
import 'vendor_management_service.dart';

/// Service for tracking commissions and payouts
class CommissionService {
  static final CommissionService _instance = CommissionService._internal();
  factory CommissionService() => _instance;
  CommissionService._internal();

  final VendorManagementService _vendorService = VendorManagementService();
  final List<Transaction> _transactions = [];
  final List<Commission> _commissions = [];
  final List<Payout> _payouts = [];

  /// Initialize service
  Future<void> initialize() async {
    await _loadSampleTransactions();
    debugPrint('✅ Commission Service initialized');
  }

  /// Get all transactions
  List<Transaction> get transactions => List.unmodifiable(_transactions);

  /// Get all commissions
  List<Commission> get commissions => List.unmodifiable(_commissions);

  /// Get all payouts
  List<Payout> get payouts => List.unmodifiable(_payouts);

  /// Process a new transaction and calculate commissions
  Future<String> processTransaction(Transaction transaction) async {
    // Add transaction
    _transactions.add(transaction);

    // Calculate and create commissions if transaction is completed
    if (transaction.isEligibleForCommission) {
      await _createCommissions(transaction);
    }

    debugPrint('✅ Processed transaction: ${transaction.id}');
    return transaction.id;
  }

  /// Create commissions for a transaction
  Future<void> _createCommissions(Transaction transaction) async {
    // Create vendor commission
    final vendorCommission = await _createVendorCommission(transaction);
    if (vendorCommission != null) {
      _commissions.add(vendorCommission);
    }

    // Create affiliate commission if applicable
    if (transaction.affiliateId != null) {
      final affiliateCommission = await _createAffiliateCommission(transaction);
      if (affiliateCommission != null) {
        _commissions.add(affiliateCommission);
      }
    }
  }

  /// Create vendor commission
  Future<Commission?> _createVendorCommission(Transaction transaction) async {
    final vendor = _vendorService.getVendorById(transaction.vendorId);
    if (vendor == null || vendor.status != VerificationStatus.verified) {
      return null;
    }

    // Vendors *pay* commission, so calculate the amount they owe
    final commissionRate = vendor.commissionRates[transaction.category] ?? 0.0;
    final commissionAmount = transaction.netAmount * (commissionRate / 100);
    final appRevenue = commissionAmount * 0.2; // App takes 20% of commission
    const metadata = <String, dynamic>{};

    final commission = Commission(
      id: 'comm_\${DateTime.now().millisecondsSinceEpoch}_vendor',
      transactionId: transaction.id,
      partnerId: vendor.id,
      partnerType: 'vendor',
      category: transaction.category,
      saleAmount: transaction.netAmount,
      commissionRate: commissionRate,
      commissionAmount: commissionAmount,
      bonusAmount: 0.0,
      totalAmount: commissionAmount,
      metadata: metadata,
      appRevenue: appRevenue,
      status: CommissionStatus.pending,
      createdAt: DateTime.now(),
      approvedAt: null,
      paidAt: null,
      payoutId: null,
      notes: null,
    );

    // Update vendor stats
    await _updateVendorStats(vendor.id, transaction.netAmount, commissionAmount);

    return commission;
  }

  /// Create affiliate commission
  Future<Commission?> _createAffiliateCommission(Transaction transaction) async {
    if (transaction.affiliateId == null) return null;

    final affiliate = _vendorService.getAffiliateById(transaction.affiliateId!);
    if (affiliate == null || !affiliate.isActive) {
      return null;
    }

    final commissionAmount = affiliate.calculateCommission(transaction.category, transaction.netAmount);
    final commissionRate = affiliate.commissionRates[transaction.category] ?? 0.0;

    // Calculate bonus for new customer acquisition
    double bonusAmount = 0.0;
    final isNewCustomer = await _isNewCustomer(transaction.customerId);
    if (isNewCustomer) {
      bonusAmount = 10.0; // $10 bonus for new customer
    }
    const metadata = <String, dynamic>{};

    final commission = Commission(
      id: 'comm_\${DateTime.now().millisecondsSinceEpoch}_affiliate',
      transactionId: transaction.id,
      partnerId: affiliate.id,
      partnerType: 'affiliate',
      category: transaction.category,
      saleAmount: transaction.netAmount,
      commissionRate: commissionRate,
      commissionAmount: commissionAmount,
      bonusAmount: bonusAmount,
      totalAmount: commissionAmount + bonusAmount,
      appRevenue: 0.0, // Affiliates don't pay commission
      status: CommissionStatus.pending,
      createdAt: DateTime.now(),
      approvedAt: null,
      paidAt: null,
      payoutId: null,
      notes: null,
      metadata: metadata,
    );

    // Update affiliate stats
    _updateAffiliateStats(affiliate.id, commissionAmount + bonusAmount);

    return commission;
  }

  /// Update affiliate statistics
  Future<void> _updateAffiliateStats(String affiliateId, double commissionAmount) async {
    final affiliate = _vendorService.getAffiliateById(affiliateId);
    if (affiliate == null) return;

    final updatedAffiliate = affiliate.copyWith(
      totalEarnings: affiliate.totalEarnings + commissionAmount,
      totalReferrals: affiliate.totalReferrals + 1,
    );

    await _vendorService.updateAffiliate(affiliateId, updatedAffiliate);

    // Check for tier upgrade
    if (updatedAffiliate.qualifiesForTierUpgrade()) {
      await _vendorService.upgradeAffiliateTier(affiliateId);
    }
  }

  /// Update vendor statistics
  Future<void> _updateVendorStats(String vendorId, double saleAmount, double commissionAmount) async {
    final vendor = _vendorService.getVendorById(vendorId);
    if (vendor == null) return;

    // Subtract commission from vendor's total sales
    final updatedVendor = vendor.copyWith(
      totalSales: vendor.totalSales + saleAmount,
      totalCommissionPaid: vendor.totalCommissionPaid + commissionAmount, // Track total commission paid
      totalOrders: vendor.totalOrders + 1,
    );

    await _vendorService.updateVendor(vendorId, updatedVendor);

    // Check for tier upgrade
    if (updatedVendor.qualifiesForTierUpgrade()) {
      await _vendorService.upgradeVendorTier(vendorId);
    }
  }

  /// Create payout for partner
  Future<String> createPayout(String partnerId, String partnerType, String paymentMethod) async {
    List<Commission> pendingCommissions = _commissions
        .where((c) => c.partnerId == partnerId &&
            c.partnerType == partnerType &&
            c.isReadyForPayout)
        .toList();

    if (pendingCommissions.isEmpty) {
      throw Exception('No pending commissions found for partner');
    }
    const Map<String, dynamic> metadata = <String, dynamic>{};

    double totalPayoutAmount = pendingCommissions.fold(0.0, (sum, c) => sum + c.totalAmount);

    double calculatePayoutFees(String paymentMethod, double amount) {
      switch (paymentMethod.toLowerCase()) {
        case 'paypal':
          return amount * 0.029 + 0.30; // PayPal fees
        case 'stripe':
          return amount * 0.029 + 0.30; // Stripe fees
        case 'bank_transfer':
          return 5.00; // Flat fee for bank transfers
        default:
          return 0.0;
      }
    }
    final fees = calculatePayoutFees(paymentMethod, totalPayoutAmount);
    final netAmount = totalPayoutAmount - fees;

    final payout = Payout(
      id: 'payout_\${DateTime.now().millisecondsSinceEpoch}',
      partnerId: partnerId,
      partnerType: partnerType,
      commissionIds: pendingCommissions.map((c) => c.id).toList(),
      totalAmount: totalPayoutAmount,
      fees: fees,
      netAmount: netAmount,
      paymentDetails: const {},
      metadata: metadata,
      status: PayoutStatus.pending,
      paymentMethod: paymentMethod,
      createdAt: DateTime.now(),
    );

    _payouts.add(payout);

    // Mark commissions as paid
    for (final commission in pendingCommissions) {
      final index = _commissions.indexWhere((c) => c.id == commission.id);
      if (index != -1) {
        _commissions[index] = _commissions[index].copyWith(
          status: CommissionStatus.paid,
          paidAt: DateTime.now(),
          payoutId: payout.id,
        );
      }
    }

    debugPrint('✅ Created payout: ${payout.id} for $netAmount');
    return payout.id;
  }

  /// Get commissions by partner
  List<Commission> getCommissionsByPartner(String partnerId, String partnerType) {
    return _commissions
        .where((c) => c.partnerId == partnerId && c.partnerType == partnerType)
        .toList();
  }

  /// Get pending commissions
  List<Commission> get pendingCommissions {
    return _commissions.where((c) => c.status == CommissionStatus.pending).toList();
  }

  /// Get approved commissions ready for payout
  List<Commission> get readyForPayout {
    return _commissions.where((c) => c.isReadyForPayout).toList();
  }

  /// Get commission analytics
  Map<String, dynamic> getCommissionAnalytics() {
    final totalCommissions = _commissions.fold<double>(0.0, (sum, c) => sum + c.totalAmount);
    final paidCommissions = _commissions
        .where((c) => c.status == CommissionStatus.paid)
        .fold<double>(0.0, (sum, c) => sum + c.totalAmount);
    final pendingCommissions = _commissions
        .where((c) => c.status == CommissionStatus.pending)
        .fold<double>(0.0, (sum, c) => sum + c.totalAmount);

    return {
      'totalCommissions': totalCommissions,
      'paidCommissions': paidCommissions,
      'pendingCommissions': pendingCommissions,
      'totalTransactions': _transactions.length,
      'completedTransactions': _transactions.where((t) => t.status == TransactionStatus.completed).length,
      'totalPayouts': _payouts.length,
      'totalPayoutAmount': _payouts.fold<double>(0.0, (sum, c) => sum + c.netAmount),
    };
  }

  /// Check if customer is new
  Future<bool> _isNewCustomer(String customerId) async {
    final customerTransactions = _transactions.where((t) => t.customerId == customerId).length;
    return customerTransactions <= 1;
  }

  /// Load sample transactions for demonstration
  Future<void> _loadSampleTransactions() async {
    final sampleTransactions = [
      Transaction(
        id: 'txn_001',
        orderId: 'order_001',
        customerId: 'customer_001',
        vendorId: 'vendor_001',
        affiliateId: 'affiliate_001',
        productId: 'product_001',
        productName: 'Organic Turmeric Capsules',
        category: ProductCategory.supplements,
        productPrice: 29.99,
        quantity: 2,
        subtotal: 59.98,
        tax: 4.80,
        shipping: 5.99,
        total: 70.77,
        status: TransactionStatus.completed,
        createdAt: DateTime.now().subtract(const Duration(days: 1)),
        completedAt: DateTime.now().subtract(const Duration(hours: 23)),
      ),
      Transaction(
        id: 'txn_002',
        orderId: 'order_002',
        customerId: 'customer_002',
        vendorId: 'vendor_001',
        productId: 'product_002',
        productName: 'Lavender Essential Oil',
        category: ProductCategory.essentialOils,
        productPrice: 24.99,
        quantity: 1,
        subtotal: 24.99,
        tax: 2.00,
        shipping: 3.99,
        total: 30.98,
        status: TransactionStatus.completed,
        createdAt: DateTime.now().subtract(const Duration(days: 2)),
        completedAt: DateTime.now().subtract(const Duration(days: 2, hours: 1)),
      ),
    ];

    for (final transaction in sampleTransactions) {
      await processTransaction(transaction);
    }
  }
}
