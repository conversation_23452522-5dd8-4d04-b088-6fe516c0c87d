import 'package:flutter/foundation.dart';
import '../models/commission_models.dart';
import '../models/vendor_models.dart';
import 'vendor_management_service.dart';
import 'database_service.dart';
import 'postgres_service.dart';

/// Commission Service for managing transactions, commissions, and payouts
class CommissionService {
  static final CommissionService _instance = CommissionService._internal();
  factory CommissionService() => _instance;
  CommissionService._internal();

  final VendorManagementService _vendorService = VendorManagementService();
  final DatabaseService _databaseService = DatabaseService();
  final PostgreSQLService _postgresService = PostgreSQLService();
  final List<Transaction> _transactions = [];
  final List<Commission> _commissions = [];
  final List<Payout> _payouts = [];

  /// Initialize service
  Future<void> initialize() async {
    // Initialize database services
    await _databaseService.initialize();
    await _postgresService.initialize();

    // Load existing data from database
    await _loadDataFromDatabase();

    // Load sample data if database is empty
    if (_transactions.isEmpty) {
      await _loadSampleData();
    }

    debugPrint('✅ Commission Service initialized');
  }

  /// Get all transactions
  List<Transaction> get transactions => List.unmodifiable(_transactions);

  /// Get all commissions
  List<Commission> get commissions => List.unmodifiable(_commissions);

  /// Get all payouts
  List<Payout> get payouts => List.unmodifiable(_payouts);

  /// Process a transaction
  Future<String> processTransaction(Transaction transaction) async {
    _transactions.add(transaction);

    // Save to database
    await _databaseService.insertTransaction(transaction);
    await _postgresService.insertTransaction(transaction);

    if (transaction.isEligibleForCommission) {
      await _createCommissions(transaction);
    }

    debugPrint('✅ Processed transaction: ${transaction.id}');
    return transaction.id;
  }

  /// Create commissions for a transaction
  Future<void> _createCommissions(Transaction transaction) async {
    // Create vendor commission
    final vendorCommission = await _createVendorCommission(transaction);
    if (vendorCommission != null) {
      _commissions.add(vendorCommission);
      await _databaseService.insertCommission(vendorCommission);
      await _postgresService.insertCommission(vendorCommission);
    }

    // Create affiliate commission if applicable
    if (transaction.affiliateId != null) {
      final affiliateCommission = await _createAffiliateCommission(transaction);
      if (affiliateCommission != null) {
        _commissions.add(affiliateCommission);
        await _databaseService.insertCommission(affiliateCommission);
        await _postgresService.insertCommission(affiliateCommission);
      }
    }
  }

  /// Create vendor commission (vendor owes us commission on their sales)
  Future<Commission?> _createVendorCommission(Transaction transaction) async {
    final vendor = _vendorService.getVendorById(transaction.vendorId);
    if (vendor == null || vendor.status != VerificationStatus.verified) {
      return null;
    }

    // Vendor commission rate (e.g., 10% - vendor owes us this percentage)
    final commissionRate = vendor.commissionRates[transaction.category] ?? 10.0;

    // Amount vendor owes us (e.g., $100 sale × 10% = $10 vendor owes us)
    final commissionOwedToUs = transaction.total * (commissionRate / 100);

    // Amount vendor receives after commission (e.g., $100 - $10 = $90 vendor keeps)
    final vendorReceives = transaction.total - commissionOwedToUs;

    return Commission(
      id: 'comm_${DateTime.now().millisecondsSinceEpoch}_vendor',
      transactionId: transaction.id,
      partnerId: vendor.id,
      partnerType: 'vendor',
      category: transaction.category,
      saleAmount: transaction.total,
      commissionRate: commissionRate,
      commissionAmount: commissionOwedToUs, // Amount vendor owes us
      totalAmount: commissionOwedToUs,
      appRevenue: commissionOwedToUs, // We earn this from vendor
      status: CommissionStatus.pending,
      createdAt: DateTime.now(),
      notes: 'Vendor owes us \$${commissionOwedToUs.toStringAsFixed(2)} commission on \$${transaction.total} sale. Vendor receives \$${vendorReceives.toStringAsFixed(2)}',
      metadata: {
        'vendor_receives': vendorReceives.toStringAsFixed(2),
        'commission_owed_to_app': commissionOwedToUs.toStringAsFixed(2),
        'commission_type': 'vendor_owes_us'
      },
    );
  }

  /// Create affiliate commission (we pay partners 5% for referrals)
  Future<Commission?> _createAffiliateCommission(Transaction transaction) async {
    if (transaction.affiliateId == null) return null;

    final affiliate = _vendorService.getAffiliateById(transaction.affiliateId!);
    if (affiliate == null || !affiliate.isActive) {
      return null;
    }

    // Partner commission rate (e.g., 5% - we pay them this percentage)
    final commissionRate = affiliate.commissionRates[transaction.category] ?? 5.0;

    // Amount we pay the partner (e.g., $100 sale × 5% = $5 we pay partner)
    final commissionWePayPartner = transaction.total * (commissionRate / 100);

    double bonusAmount = 0.0;
    // Check for new customer bonus
    final isNewCustomer = await _isNewCustomer(transaction.customerId);
    if (isNewCustomer) {
      bonusAmount = 10.0; // Extra bonus for bringing new customers
    }

    final totalPayoutToPartner = commissionWePayPartner + bonusAmount;

    return Commission(
      id: 'comm_${DateTime.now().millisecondsSinceEpoch}_affiliate',
      transactionId: transaction.id,
      partnerId: affiliate.id,
      partnerType: 'affiliate',
      category: transaction.category,
      saleAmount: transaction.total,
      commissionRate: commissionRate,
      commissionAmount: commissionWePayPartner, // Amount we pay partner
      bonusAmount: bonusAmount,
      totalAmount: totalPayoutToPartner, // Total we owe partner
      appRevenue: -totalPayoutToPartner, // Negative because we pay out
      status: CommissionStatus.pending,
      createdAt: DateTime.now(),
      notes: 'We pay partner \$${totalPayoutToPartner.toStringAsFixed(2)} for \$${transaction.total} referral sale',
      metadata: {
        'partner_earns': totalPayoutToPartner.toStringAsFixed(2),
        'commission_type': 'we_pay_partner',
        'referral_bonus': bonusAmount.toStringAsFixed(2),
        'is_new_customer': isNewCustomer.toString()
      },
    );
  }

  /// Create payout for partner
  Future<String> createPayout(String partnerId, String partnerType, String paymentMethod) async {
    final pendingCommissions = _commissions
        .where((c) => c.partnerId == partnerId &&
            c.partnerType == partnerType &&
            c.isReadyForPayout)
        .toList();

    if (pendingCommissions.isEmpty) {
      throw Exception('No pending commissions found for partner');
    }

    final totalPayoutAmount = pendingCommissions.fold(0.0, (sum, c) => sum + c.totalAmount);
    final fees = _calculatePayoutFees(paymentMethod, totalPayoutAmount);
    final netAmount = totalPayoutAmount - fees;

    final payout = Payout(
      id: 'payout_${DateTime.now().millisecondsSinceEpoch}',
      partnerId: partnerId,
      partnerType: partnerType,
      commissionIds: pendingCommissions.map((c) => c.id).toList(),
      totalAmount: totalPayoutAmount,
      fees: fees,
      netAmount: netAmount,
      status: PayoutStatus.pending,
      paymentMethod: paymentMethod,
      createdAt: DateTime.now(),
    );

    _payouts.add(payout);

    // Save payout to database
    await _databaseService.insertPayout(payout);
    await _postgresService.insertPayout(payout);

    // Mark commissions as paid
    for (final commission in pendingCommissions) {
      final index = _commissions.indexWhere((c) => c.id == commission.id);
      if (index != -1) {
        _commissions[index] = commission.copyWith(
          status: CommissionStatus.paid,
          paidAt: DateTime.now(),
          payoutId: payout.id,
        );

        // Update commission status in database
        await _databaseService.updateCommissionStatus(
          commission.id,
          CommissionStatus.paid,
          paidAt: DateTime.now(),
          payoutId: payout.id
        );
      }
    }

    debugPrint('✅ Created payout: ${payout.id} for $netAmount');
    return payout.id;
  }

  /// Get commissions by partner
  List<Commission> getCommissionsByPartner(String partnerId, String partnerType) {
    return _commissions
        .where((c) => c.partnerId == partnerId && c.partnerType == partnerType)
        .toList();
  }

  /// Get pending commissions
  List<Commission> get pendingCommissions {
    return _commissions.where((c) => c.status == CommissionStatus.pending).toList();
  }

  /// Get approved commissions ready for payout
  List<Commission> get readyForPayout {
    return _commissions.where((c) => c.isReadyForPayout).toList();
  }

  /// Get commission analytics
  Map<String, dynamic> getCommissionAnalytics() {
    final totalCommissions = _commissions.fold<double>(0.0, (sum, c) => sum + c.totalAmount);
    final paidCommissions = _commissions
        .where((c) => c.status == CommissionStatus.paid)
        .fold<double>(0.0, (sum, c) => sum + c.totalAmount);
    final pendingCommissions = _commissions
        .where((c) => c.status == CommissionStatus.pending)
        .fold<double>(0.0, (sum, c) => sum + c.totalAmount);

    return {
      'totalCommissions': totalCommissions,
      'paidCommissions': paidCommissions,
      'pendingCommissions': pendingCommissions,
      'totalTransactions': _transactions.length,
      'totalPayoutAmount': _payouts.fold<double>(0.0, (sum, c) => sum + c.netAmount),
    };
  }

  /// Calculate payout fees
  double _calculatePayoutFees(String paymentMethod, double amount) {
    switch (paymentMethod.toLowerCase()) {
      case 'paypal':
        return amount * 0.029 + 0.30;
      case 'bank_transfer':
        return 5.00;
      case 'stripe':
        return amount * 0.029 + 0.30;
      default:
        return 0.0;
    }
  }

  /// Check if customer is new
  Future<bool> _isNewCustomer(String customerId) async {
    final customerTransactions = _transactions.where((t) => t.customerId == customerId).length;
    return customerTransactions <= 1;
  }

  /// Load sample data
  Future<void> _loadSampleData() async {
    final sampleTransaction = Transaction(
      id: 'txn_001',
      orderId: 'order_001',
      customerId: 'customer_001',
      vendorId: 'vendor_001',
      affiliateId: 'affiliate_001',
      productId: 'product_001',
      productName: 'Organic Turmeric Capsules',
      category: ProductCategory.supplements,
      productPrice: 29.99,
      quantity: 2,
      subtotal: 59.98,
      tax: 4.80,
      shipping: 5.99,
      total: 70.77,
      status: TransactionStatus.completed,
      createdAt: DateTime.now().subtract(const Duration(days: 1)),
      completedAt: DateTime.now().subtract(const Duration(hours: 23)),
    );

    await processTransaction(sampleTransaction);
  }

  /// Load data from database
  Future<void> _loadDataFromDatabase() async {
    try {
      // Load from local database
      final transactions = await _databaseService.getTransactions();
      final commissions = await _databaseService.getCommissions();
      final payouts = await _databaseService.getPayouts();

      _transactions.clear();
      _commissions.clear();
      _payouts.clear();

      _transactions.addAll(transactions);
      _commissions.addAll(commissions);
      _payouts.addAll(payouts);

      debugPrint('📊 Loaded ${transactions.length} transactions, ${commissions.length} commissions, ${payouts.length} payouts from database');
    } catch (e) {
      debugPrint('⚠️ Failed to load data from database: $e');
    }
  }

  /// Sync data to PostgreSQL
  Future<void> syncToPostgreSQL() async {
    try {
      await _postgresService.syncToPostgreSQL(_transactions, _commissions, _payouts);
      debugPrint('✅ Successfully synced data to PostgreSQL');
    } catch (e) {
      debugPrint('❌ Failed to sync to PostgreSQL: $e');
    }
  }
}
