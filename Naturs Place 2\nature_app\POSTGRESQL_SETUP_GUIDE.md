# PostgreSQL Setup Guide for Nature's Place Commission System

## 🎉 **Your PostgreSQL Integration is Ready!**

This guide will help you set up and use the complete PostgreSQL database integration for your Nature's Place commission system.

## 📋 **What's Been Implemented**

### ✅ **Core Services**
- **PostgreSQL Service** - Real database operations with your "Natures Place" database
- **Database Config Service** - Manages connection settings and preferences
- **Database Connection Manager** - Connection pooling, retry logic, and health monitoring
- **Commission Service** - Updated to use both local SQLite and PostgreSQL

### ✅ **Features**
- **Dual Storage**: Local SQLite for offline + PostgreSQL for server analytics
- **Connection Pooling**: Efficient database connection management
- **Automatic Retry**: Handles connection failures gracefully
- **Health Monitoring**: Automatic connection health checks
- **Configuration Management**: Easy setup and environment variable support

## 🚀 **Quick Setup**

### 1. **Create Your PostgreSQL Database**

Run your database creation command:
```sql
CREATE DATABASE "Natures Place"
    WITH
    OWNER = postgres
    ENCODING = 'UTF8'
    LC_COLLATE = 'English_Canada.1252'
    LC_CTYPE = 'English_Canada.1252'
    LOCALE_PROVIDER = 'libc'
    TABLESPACE = pg_default
    CONNECTION LIMIT = -1
    IS_TEMPLATE = False;
```

### 2. **Setup Database Tables**

Execute the setup script:
```bash
psql -U postgres -d "Natures Place" -f database_setup.sql
```

### 3. **Configure Connection**

#### Option A: Environment Variables (Recommended)
```bash
export POSTGRES_HOST=localhost
export POSTGRES_PORT=5432
export POSTGRES_DB="Natures Place"
export POSTGRES_USER=postgres
export POSTGRES_PASSWORD=your_password_here
```

#### Option B: In-App Configuration
```dart
final configService = DatabaseConfigService();
await configService.initialize();

await configService.setPostgreSQLConfig(
  host: 'localhost',
  port: 5432,
  database: 'Natures Place',
  username: 'postgres',
  password: 'your_password_here',
);
```

### 4. **Initialize in Your App**

```dart
// In your main.dart or app initialization
final commissionService = CommissionService();
await commissionService.initialize();
```

## 🔧 **Advanced Configuration**

### **Connection Pool Settings**
```dart
await configService.setPerformanceSettings(
  connectionPoolSize: 10,        // Max connections
  queryCacheEnabled: true,       // Enable query caching
  cacheSizeMb: 100,             // Cache size
  batchInsertSize: 500,         // Batch size for inserts
);
```

### **Sync Settings**
```dart
await configService.setSyncSettings(
  autoSyncEnabled: true,         // Auto sync to PostgreSQL
  syncIntervalMinutes: 15,       // Sync every 15 minutes
  syncOnStartup: true,           // Sync when app starts
  syncOnTransaction: true,       // Sync after each transaction
  offlineMode: false,            // Allow offline operation
);
```

## 📊 **Usage Examples**

### **Process Transactions**
```dart
final transaction = Transaction(
  id: 'txn_001',
  orderId: 'order_001',
  customerId: 'customer_001',
  vendorId: 'vendor_001',
  // ... other fields
);

// Automatically saves to both SQLite and PostgreSQL
await commissionService.processTransaction(transaction);
```

### **Get Analytics**
```dart
final analytics = await commissionService.getCommissionAnalytics();
print('Total Commissions: \${analytics['totalCommissions']}');
print('Paid Commissions: \${analytics['paidCommissions']}');
print('Pending Commissions: \${analytics['pendingCommissions']}');
```

### **Manual Sync**
```dart
// Force sync local data to PostgreSQL
await commissionService.syncToPostgreSQL();
```

### **Test Connection**
```dart
final postgresService = PostgreSQLService();
final isConnected = await postgresService.testConnection();
print('PostgreSQL Connected: $isConnected');
```

## 🔍 **Monitoring & Debugging**

### **Connection Pool Statistics**
```dart
final connectionManager = DatabaseConnectionManager();
final stats = connectionManager.getPoolStatistics();
print('Pool Utilization: \${stats['pool_utilization']}');
print('Available Connections: \${stats['available_connections']}');
```

### **Configuration Validation**
```dart
final configService = DatabaseConfigService();
final validation = configService.validateConfiguration();
if (!validation['isValid']) {
  print('Configuration Issues: \${validation['issues']}');
}
```

## 🛠️ **Troubleshooting**

### **Common Issues**

1. **Connection Failed**
   ```
   ❌ PostgreSQL initialization failed: connection refused
   ```
   - Check PostgreSQL service is running
   - Verify host and port settings
   - Check firewall settings

2. **Authentication Failed**
   ```
   ❌ Failed to connect: authentication failed
   ```
   - Verify username and password
   - Check PostgreSQL user permissions
   - Ensure database exists

3. **Table Not Found**
   ```
   ❌ relation "transactions" does not exist
   ```
   - Run the database setup script
   - Check database name is correct
   - Verify user has table creation permissions

### **Debug Mode**

Enable detailed logging:
```dart
// The services automatically log debug information
// Check console for detailed connection and operation logs
```

## 📈 **Performance Tips**

1. **Connection Pooling**: Increase pool size for high-traffic apps
2. **Batch Operations**: Use batch inserts for multiple transactions
3. **Indexing**: The setup script creates optimal indexes
4. **Caching**: Enable query caching for better performance
5. **Monitoring**: Use health checks to detect issues early

## 🔒 **Security Best Practices**

1. **Use Environment Variables**: Never hardcode credentials
2. **SSL Connections**: Enable SSL in production
3. **User Permissions**: Create dedicated database user with minimal permissions
4. **Network Security**: Use VPN or private networks
5. **Regular Backups**: Implement automated backup strategy

## 🚀 **Production Deployment**

### **Environment Setup**
```bash
# Production environment variables
export POSTGRES_HOST=your-production-host
export POSTGRES_PORT=5432
export POSTGRES_DB="Natures Place"
export POSTGRES_USER=naturesplace_app
export POSTGRES_PASSWORD=secure_production_password
export POSTGRES_SSL_MODE=require
```

### **Performance Settings**
```dart
await configService.setPerformanceSettings(
  connectionPoolSize: 20,
  queryCacheEnabled: true,
  cacheSizeMb: 200,
  enableCompression: true,
  logSlowQueries: true,
  slowQueryThresholdMs: 500,
);
```

## 📚 **Next Steps**

1. **Test the Setup**: Run the commission system with sample data
2. **Monitor Performance**: Check connection pool statistics
3. **Setup Backups**: Implement regular database backups
4. **Add Monitoring**: Set up alerts for connection failures
5. **Scale as Needed**: Adjust pool size and settings based on usage

## 🆘 **Support**

If you encounter issues:
1. Check the console logs for detailed error messages
2. Verify database connectivity using `testConnection()`
3. Validate configuration using `validateConfiguration()`
4. Check the DATABASE_SETUP.md for additional troubleshooting

Your PostgreSQL integration is now ready for production use! 🎉
