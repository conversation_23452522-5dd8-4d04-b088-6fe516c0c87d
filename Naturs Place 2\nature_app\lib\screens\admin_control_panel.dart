import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../services/affiliate_marketplace_service.dart';

class AdminControlPanel extends StatefulWidget {
  const AdminControlPanel({super.key});

  @override
  State<AdminControlPanel> createState() => _AdminControlPanelState();
}

class _AdminControlPanelState extends State<AdminControlPanel> {
  final String _adminPin = '2024'; // Admin PIN code
  bool _isAuthenticated = false;
  final _pinController = TextEditingController();

  @override
  void dispose() {
    _pinController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (!_isAuthenticated) {
      return _buildPinEntryScreen();
    }

    return DefaultTabController(
      length: 6,
      child: Scaffold(
        appBar: AppBar(
          title: const Text('Admin Control Panel'),
          backgroundColor: Colors.red[700],
          foregroundColor: Colors.white,
          actions: [
            IconButton(
              onPressed: _logout,
              icon: const Icon(Icons.logout),
              tooltip: 'Logout',
            ),
          ],
          bottom: const TabBar(
            labelColor: Colors.white,
            unselectedLabelColor: Colors.white70,
            indicatorColor: Colors.white,
            isScrollable: true,
            tabs: [
              Tab(icon: Icon(Icons.dashboard), text: 'Overview'),
              Tab(icon: Icon(Icons.attach_money), text: 'Commissions'),
              Tab(icon: Icon(Icons.shopping_cart), text: 'Purchases'),
              Tab(icon: Icon(Icons.people), text: 'Vendors'),
              Tab(icon: Icon(Icons.verified), text: 'Verification'),
              Tab(icon: Icon(Icons.notifications), text: 'Notifications'),
            ],
          ),
        ),
        body: TabBarView(
          children: [
            _buildOverviewTab(),
            _buildCommissionsTab(),
            _buildPurchasesTab(),
            _buildVendorsTab(),
            _buildVerificationTab(),
            _buildNotificationsTab(),
          ],
        ),
      ),
    );
  }

  Widget _buildPinEntryScreen() {
    return Scaffold(
      backgroundColor: Colors.red[50],
      body: Center(
        child: Card(
          margin: const EdgeInsets.all(32),
          child: Padding(
            padding: const EdgeInsets.all(32),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Icon(
                  Icons.admin_panel_settings,
                  size: 64,
                  color: Colors.red,
                ),
                const SizedBox(height: 24),
                Text(
                  'Admin Access Required',
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: Colors.red[700],
                  ),
                ),
                const SizedBox(height: 16),
                const Text(
                  'Enter the admin PIN to access the control panel',
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 24),
                SizedBox(
                  width: 200,
                  child: TextField(
                    controller: _pinController,
                    obscureText: true,
                    textAlign: TextAlign.center,
                    keyboardType: TextInputType.number,
                    maxLength: 4,
                    decoration: const InputDecoration(
                      labelText: 'PIN Code',
                      border: OutlineInputBorder(),
                      counterText: '',
                    ),
                    onSubmitted: (_) => _verifyPin(),
                  ),
                ),
                const SizedBox(height: 24),
                ElevatedButton(
                  onPressed: _verifyPin,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.red[700],
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 12),
                  ),
                  child: const Text('Access Panel'),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _verifyPin() {
    if (_pinController.text == _adminPin) {
      setState(() {
        _isAuthenticated = true;
      });
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Invalid PIN. Access denied.'),
          backgroundColor: Colors.red,
        ),
      );
      _pinController.clear();
    }
  }

  void _logout() {
    setState(() {
      _isAuthenticated = false;
      _pinController.clear();
    });
  }

  Widget _buildOverviewTab() {
    return Consumer<PartnerMarketplaceService>(
      builder: (context, service, child) {
        final totalPurchases = service.purchases.length;
        final totalRevenue = service.purchases.fold(0.0, (sum, p) => sum + p.purchaseAmount);
        final totalVendors = service.partners.length;
        final totalProducts = service.products.length;
        final totalReviews = service.reviews.length;
        final verifiedProducts = service.verifications.length;

        return SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'System Overview',
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),

              // Stats Grid
              GridView.count(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                crossAxisCount: 2,
                childAspectRatio: 1.5,
                crossAxisSpacing: 16,
                mainAxisSpacing: 16,
                children: [
                  _buildStatCard(
                    'Total Revenue',
                    '\$${totalRevenue.toStringAsFixed(2)}',
                    Icons.attach_money,
                    Colors.green,
                  ),
                  _buildStatCard(
                    'Total Purchases',
                    totalPurchases.toString(),
                    Icons.shopping_cart,
                    Colors.blue,
                  ),
                  _buildStatCard(
                    'Active Vendors',
                    totalVendors.toString(),
                    Icons.people,
                    Colors.orange,
                  ),
                  _buildStatCard(
                    'Total Products',
                    totalProducts.toString(),
                    Icons.inventory,
                    Colors.purple,
                  ),
                  _buildStatCard(
                    'Product Reviews',
                    totalReviews.toString(),
                    Icons.star,
                    Colors.amber,
                  ),
                  _buildStatCard(
                    'Verified Products',
                    verifiedProducts.toString(),
                    Icons.verified,
                    Colors.teal,
                  ),
                ],
              ),

              const SizedBox(height: 24),

              // Recent Activity
              Text(
                'Recent Activity',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),

              ...service.purchases.take(5).map((purchase) => Card(
                child: ListTile(
                  leading: const CircleAvatar(
                    backgroundColor: Colors.green,
                    child: Icon(Icons.shopping_cart, color: Colors.white),
                  ),
                  title: Text('Purchase: ${purchase.id}'),
                  subtitle: Text(
                    'Customer: ${purchase.customerName}\n'
                    'Amount: \$${purchase.purchaseAmount.toStringAsFixed(2)}',
                  ),
                  trailing: Text(
                    _formatDateTime(purchase.purchaseDate),
                    style: Theme.of(context).textTheme.bodySmall,
                  ),
                  isThreeLine: true,
                ),
              )),
            ],
          ),
        );
      },
    );
  }

  Widget _buildCommissionsTab() {
    return Consumer<PartnerMarketplaceService>(
      builder: (context, service, child) {
        final commissionAnalytics = service.getPlatformCommissionAnalytics();

        return SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Warning Banner
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.red[50],
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.red[200]!),
                ),
                child: Row(
                  children: [
                    const Icon(Icons.security, color: Colors.red),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'CONFIDENTIAL - ADMIN ONLY',
                            style: TextStyle(
                              color: Colors.red[700],
                              fontWeight: FontWeight.bold,
                              fontSize: 16,
                            ),
                          ),
                          Text(
                            'Platform commission data is private and only visible to administrators.',
                            style: TextStyle(
                              color: Colors.red[600],
                              fontSize: 12,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 24),

              Text(
                'Platform Commission Analytics',
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),

              // Commission Stats Grid
              GridView.count(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                crossAxisCount: 2,
                childAspectRatio: 1.5,
                crossAxisSpacing: 16,
                mainAxisSpacing: 16,
                children: [
                  _buildStatCard(
                    'Total Platform Earnings',
                    '\$${commissionAnalytics['totalPlatformEarnings'].toStringAsFixed(2)}',
                    Icons.account_balance,
                    Colors.green,
                  ),
                  _buildStatCard(
                    'Vendor Platform Fees',
                    '\$${commissionAnalytics['totalVendorFees'].toStringAsFixed(2)}',
                    Icons.people,
                    Colors.blue,
                  ),
                  _buildStatCard(
                    'Affiliate Commission Fees',
                    '\$${commissionAnalytics['totalAffiliateFees'].toStringAsFixed(2)}',
                    Icons.handshake,
                    Colors.orange,
                  ),
                  _buildStatCard(
                    'Total Sales Volume',
                    '\$${commissionAnalytics['totalSalesVolume'].toStringAsFixed(2)}',
                    Icons.trending_up,
                    Colors.purple,
                  ),
                  _buildStatCard(
                    'Platform Margin',
                    '${commissionAnalytics['platformMargin'].toStringAsFixed(1)}%',
                    Icons.percent,
                    Colors.teal,
                  ),
                  _buildStatCard(
                    'Avg Earnings/Sale',
                    '\$${commissionAnalytics['averageEarningsPerSale'].toStringAsFixed(2)}',
                    Icons.calculate,
                    Colors.indigo,
                  ),
                ],
              ),

              const SizedBox(height: 24),

              // Revenue Breakdown
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.green[50],
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.green[200]!),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Revenue Breakdown',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: Colors.green[700],
                      ),
                    ),
                    const SizedBox(height: 12),
                    Row(
                      children: [
                        Expanded(
                          child: Column(
                            children: [
                              Text(
                                'Vendor Fees',
                                style: TextStyle(
                                  color: Colors.green[600],
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                              Text(
                                '${commissionAnalytics['vendorFeePercentage'].toStringAsFixed(1)}%',
                                style: TextStyle(
                                  color: Colors.green[700],
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                        ),
                        Expanded(
                          child: Column(
                            children: [
                              Text(
                                'Affiliate Fees',
                                style: TextStyle(
                                  color: Colors.green[600],
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                              Text(
                                '${commissionAnalytics['affiliateFeePercentage'].toStringAsFixed(1)}%',
                                style: TextStyle(
                                  color: Colors.green[700],
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 24),

              // Earnings by Vendor Tier
              Text(
                'Earnings from Vendor Platform Fees',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),

              ...((commissionAnalytics['earningsByTier'] as Map<String, double>).entries.map((entry) =>
                Card(
                  child: ListTile(
                    leading: CircleAvatar(
                      backgroundColor: _getTierColor(entry.key),
                      child: const Icon(Icons.star, color: Colors.white),
                    ),
                    title: Text('${entry.key} Tier Vendors'),
                    subtitle: Text('Platform fees collected from ${entry.key.toLowerCase()} tier vendors'),
                    trailing: Text(
                      '\$${entry.value.toStringAsFixed(2)}',
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                  ),
                ),
              )),

              const SizedBox(height: 24),

              // Earnings by Affiliate Network
              Text(
                'Earnings from Affiliate Commissions',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),

              ...((commissionAnalytics['earningsByNetwork'] as Map<String, double>).entries.map((entry) =>
                Card(
                  child: ListTile(
                    leading: CircleAvatar(
                      backgroundColor: Colors.orange,
                      child: const Icon(Icons.handshake, color: Colors.white),
                    ),
                    title: Text(entry.key),
                    subtitle: Text('Commission fees collected from ${entry.key} affiliate sales'),
                    trailing: Text(
                      '\$${entry.value.toStringAsFixed(2)}',
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                  ),
                ),
              )),

              const SizedBox(height: 24),

              // Commission Breakdown
              Text(
                'Commission Breakdown',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),

              ...service.platformCommissions.take(10).map((commission) => Card(
                child: ExpansionTile(
                  leading: CircleAvatar(
                    backgroundColor: Colors.green,
                    child: const Icon(Icons.attach_money, color: Colors.white),
                  ),
                  title: Text('Transaction ${commission.transactionId}'),
                  subtitle: Text(
                    'Sale: \$${commission.saleAmount.toStringAsFixed(2)} | '
                    'Your Earnings: \$${commission.totalPlatformEarnings.toStringAsFixed(2)}',
                  ),
                  trailing: Text(
                    _formatDateTime(commission.date),
                    style: Theme.of(context).textTheme.bodySmall,
                  ),
                  children: [
                    Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        children: [
                          _buildDetailRow('Vendor ID', commission.vendorId),
                          _buildDetailRow('Product ID', commission.productId),
                          _buildDetailRow('Vendor Tier', commission.tier),
                          _buildDetailRow('Platform Fee Rate', '${(commission.platformFeeRate * 100).toStringAsFixed(1)}%'),
                          _buildDetailRow('Affiliate Commission Rate', '${(commission.affiliateCommissionRate * 100).toStringAsFixed(1)}%'),
                          _buildDetailRow('Sale Amount', '\$${commission.saleAmount.toStringAsFixed(2)}'),
                          _buildDetailRow('Vendor Platform Fee', '\$${commission.vendorPlatformFee.toStringAsFixed(2)}'),
                          _buildDetailRow('Affiliate Commission Fee', '\$${commission.affiliateCommissionFee.toStringAsFixed(2)}'),
                          _buildDetailRow('Total Platform Earnings', '\$${commission.totalPlatformEarnings.toStringAsFixed(2)}'),
                        ],
                      ),
                    ),
                  ],
                ),
              )),
            ],
          ),
        );
      },
    );
  }

  Widget _buildPurchasesTab() {
    return Consumer<PartnerMarketplaceService>(
      builder: (context, service, child) {
        return ListView.builder(
          padding: const EdgeInsets.all(16),
          itemCount: service.purchases.length,
          itemBuilder: (context, index) {
            final purchase = service.purchases[index];
            return Card(
              child: ExpansionTile(
                leading: CircleAvatar(
                  backgroundColor: _getStatusColor(purchase.status),
                  child: const Icon(Icons.shopping_cart, color: Colors.white),
                ),
                title: Text('Order ${purchase.orderId ?? purchase.id}'),
                subtitle: Text(
                  'Customer: ${purchase.customerName}\n'
                  'Amount: \$${purchase.purchaseAmount.toStringAsFixed(2)}',
                ),
                trailing: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: _getStatusColor(purchase.status).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    purchase.status.toUpperCase(),
                    style: TextStyle(
                      color: _getStatusColor(purchase.status),
                      fontSize: 10,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                children: [
                  Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        _buildDetailRow('Product ID', purchase.productId),
                        _buildDetailRow('Vendor ID', purchase.vendorId),
                        _buildDetailRow('Customer Email', purchase.customerEmail),
                        _buildDetailRow('Payment Method', purchase.paymentMethod),
                        _buildDetailRow('App Commission', '\$${purchase.appCommissionAmount.toStringAsFixed(2)}'),
                        _buildDetailRow('Purchase Date', _formatDateTime(purchase.purchaseDate)),
                        _buildDetailRow('Vendor Redirect', purchase.isVendorSiteRedirect ? 'Yes' : 'No'),
                        _buildDetailRow('Purchase URL', purchase.purchaseUrl),
                      ],
                    ),
                  ),
                ],
              ),
            );
          },
        );
      },
    );
  }

  Widget _buildVendorsTab() {
    return Consumer<PartnerMarketplaceService>(
      builder: (context, service, child) {
        return ListView.builder(
          padding: const EdgeInsets.all(16),
          itemCount: service.partners.length,
          itemBuilder: (context, index) {
            final vendor = service.partners[index];
            final vendorPurchases = service.getVendorPurchases(vendor.id);
            final analytics = service.getVendorPurchaseAnalytics(vendor.id);
            
            return Card(
              child: ExpansionTile(
                leading: CircleAvatar(
                  backgroundImage: NetworkImage(vendor.logoUrl),
                ),
                title: Text(vendor.name),
                subtitle: Text(
                  '${vendor.tierDisplayName} Tier\n'
                  'Products: ${vendor.uploadedProductsCount}/${vendor.uploadLimit}',
                ),
                trailing: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      '\$${analytics['totalRevenue'].toStringAsFixed(2)}',
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        color: Colors.green,
                      ),
                    ),
                    Text(
                      '${vendorPurchases.length} sales',
                      style: Theme.of(context).textTheme.bodySmall,
                    ),
                  ],
                ),
                children: [
                  Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        _buildDetailRow('Network', vendor.network),
                        _buildDetailRow('Commission Rate', '${(vendor.commissionRate * 100).toInt()}%'),
                        _buildDetailRow('Total Earnings', '\$${vendor.totalEarnings.toStringAsFixed(2)}'),
                        _buildDetailRow('Monthly Fees Paid', '\$${vendor.monthlyFeesPaid.toStringAsFixed(2)}'),
                        _buildDetailRow('Platform Fees Owed', '\$${vendor.platformFeesOwed.toStringAsFixed(2)}'),
                        _buildDetailRow('Join Date', _formatDateTime(vendor.joinedDate)),
                        _buildDetailRow('Active', vendor.isActive ? 'Yes' : 'No'),
                      ],
                    ),
                  ),
                ],
              ),
            );
          },
        );
      },
    );
  }

  Widget _buildVerificationTab() {
    return Consumer<PartnerMarketplaceService>(
      builder: (context, service, child) {
        final naturalOrganicStats = service.getNaturalOrganicStats();

        return SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Natural & Organic Verification',
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),

              // Verification Stats
              GridView.count(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                crossAxisCount: 2,
                childAspectRatio: 1.5,
                crossAxisSpacing: 16,
                mainAxisSpacing: 16,
                children: [
                  _buildStatCard(
                    'Verification Rate',
                    '${naturalOrganicStats['verificationRate'].toStringAsFixed(1)}%',
                    Icons.verified,
                    Colors.green,
                  ),
                  _buildStatCard(
                    'Natural Products',
                    '${naturalOrganicStats['naturalProducts']}',
                    Icons.eco,
                    Colors.lightGreen,
                  ),
                  _buildStatCard(
                    'Organic Products',
                    '${naturalOrganicStats['organicProducts']}',
                    Icons.agriculture,
                    Colors.orange,
                  ),
                  _buildStatCard(
                    'Certified Products',
                    '${naturalOrganicStats['certifiedProducts']}',
                    Icons.workspace_premium,
                    Colors.blue,
                  ),
                  _buildStatCard(
                    'Avg Natural Score',
                    '${naturalOrganicStats['averageNaturalScore'].toStringAsFixed(1)}',
                    Icons.nature,
                    Colors.teal,
                  ),
                  _buildStatCard(
                    'Avg Organic Score',
                    '${naturalOrganicStats['averageOrganicScore'].toStringAsFixed(1)}',
                    Icons.local_florist,
                    Colors.purple,
                  ),
                ],
              ),

              const SizedBox(height: 24),

              // Product Verifications
              Text(
                'Product Verifications',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),

              ...service.verifications.map((verification) {
                final product = service.products.firstWhere(
                  (p) => p.id == verification.productId,
                  orElse: () => service.products.first,
                );

                return Card(
                  child: ExpansionTile(
                    leading: CircleAvatar(
                      backgroundColor: verification.isOrganic ? Colors.green : Colors.orange,
                      child: Icon(
                        verification.isOrganic ? Icons.eco : Icons.nature,
                        color: Colors.white,
                      ),
                    ),
                    title: Text(product.name),
                    subtitle: Text(
                      'Natural: ${verification.naturalScore.toStringAsFixed(1)} | '
                      'Organic: ${verification.organicScore.toStringAsFixed(1)}',
                    ),
                    trailing: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        if (verification.isCertified)
                          const Icon(Icons.verified, color: Colors.green, size: 16),
                        Text(
                          _formatDateTime(verification.verifiedDate),
                          style: Theme.of(context).textTheme.bodySmall,
                        ),
                      ],
                    ),
                    children: [
                      Padding(
                        padding: const EdgeInsets.all(16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            _buildDetailRow('Product ID', verification.productId),
                            _buildDetailRow('Verified By', verification.verifiedBy),
                            _buildDetailRow('Natural Score', '${verification.naturalScore}/100'),
                            _buildDetailRow('Organic Score', '${verification.organicScore}/100'),
                            _buildDetailRow('Is Natural', verification.isNatural ? 'Yes' : 'No'),
                            _buildDetailRow('Is Organic', verification.isOrganic ? 'Yes' : 'No'),
                            _buildDetailRow('Is Certified', verification.isCertified ? 'Yes' : 'No'),

                            if (verification.certifications.isNotEmpty) ...[
                              const SizedBox(height: 12),
                              Text(
                                'Certifications:',
                                style: Theme.of(context).textTheme.titleSmall?.copyWith(
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              const SizedBox(height: 4),
                              ...verification.certifications.map((cert) =>
                                Padding(
                                  padding: const EdgeInsets.symmetric(vertical: 2),
                                  child: Row(
                                    children: [
                                      const Icon(Icons.check, color: Colors.green, size: 16),
                                      const SizedBox(width: 8),
                                      Text(cert),
                                    ],
                                  ),
                                ),
                              ),
                            ],

                            const SizedBox(height: 12),
                            Text(
                              'Ingredient Analysis:',
                              style: Theme.of(context).textTheme.titleSmall?.copyWith(
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: 4),
                            ...verification.ingredientAnalysis.entries.map((entry) =>
                              Padding(
                                padding: const EdgeInsets.symmetric(vertical: 2),
                                child: Row(
                                  children: [
                                    Icon(
                                      entry.value ? Icons.check : Icons.close,
                                      color: entry.value ? Colors.green : Colors.red,
                                      size: 16,
                                    ),
                                    const SizedBox(width: 8),
                                    Expanded(child: Text(entry.key)),
                                  ],
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                );
              }),
            ],
          ),
        );
      },
    );
  }

  Widget _buildNotificationsTab() {
    return Consumer<PartnerMarketplaceService>(
      builder: (context, service, child) {
        return ListView.builder(
          padding: const EdgeInsets.all(16),
          itemCount: service.notifications.length,
          itemBuilder: (context, index) {
            final notification = service.notifications[index];
            return Card(
              color: notification.isRead ? null : Colors.blue[50],
              child: ListTile(
                leading: CircleAvatar(
                  backgroundColor: _getNotificationColor(notification.type),
                  child: Icon(
                    _getNotificationIcon(notification.type),
                    color: Colors.white,
                  ),
                ),
                title: Text(notification.title),
                subtitle: Text(
                  '${notification.message}\n'
                  'Vendor: ${notification.vendorId}',
                ),
                trailing: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                      decoration: BoxDecoration(
                        color: _getPriorityColor(notification.priority).withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Text(
                        notification.priority.toUpperCase(),
                        style: TextStyle(
                          color: _getPriorityColor(notification.priority),
                          fontSize: 10,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      _formatDateTime(notification.createdAt),
                      style: Theme.of(context).textTheme.bodySmall,
                    ),
                  ],
                ),
                isThreeLine: true,
                onTap: () {
                  if (!notification.isRead) {
                    service.markNotificationAsRead(notification.id);
                  }
                },
              ),
            );
          },
        );
      },
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, size: 32, color: color),
            const SizedBox(height: 8),
            Text(
              value,
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            Text(
              title,
              style: Theme.of(context).textTheme.bodySmall,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
          ),
          Expanded(
            child: Text(value),
          ),
        ],
      ),
    );
  }

  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'completed':
        return Colors.green;
      case 'pending':
        return Colors.orange;
      case 'failed':
        return Colors.red;
      case 'refunded':
        return Colors.purple;
      default:
        return Colors.grey;
    }
  }

  Color _getNotificationColor(String type) {
    switch (type) {
      case 'purchase':
        return Colors.green;
      case 'commission':
        return Colors.blue;
      case 'system':
        return Colors.orange;
      case 'marketing':
        return Colors.purple;
      default:
        return Colors.grey;
    }
  }

  IconData _getNotificationIcon(String type) {
    switch (type) {
      case 'purchase':
        return Icons.shopping_cart;
      case 'commission':
        return Icons.attach_money;
      case 'system':
        return Icons.settings;
      case 'marketing':
        return Icons.campaign;
      default:
        return Icons.notifications;
    }
  }

  Color _getPriorityColor(String priority) {
    switch (priority) {
      case 'urgent':
        return Colors.red;
      case 'high':
        return Colors.orange;
      case 'medium':
        return Colors.blue;
      case 'low':
        return Colors.green;
      default:
        return Colors.grey;
    }
  }

  Color _getTierColor(String tier) {
    switch (tier.toLowerCase()) {
      case 'basic':
        return Colors.grey[600]!;
      case 'premium':
        return Colors.blue;
      case 'enterprise':
        return Colors.purple;
      default:
        return Colors.grey;
    }
  }

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.day}/${dateTime.month}/${dateTime.year} ${dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')}';
  }
}
