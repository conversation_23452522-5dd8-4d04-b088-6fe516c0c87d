@echo off
echo 🔄 Restoring Normal Memory Mode for Nature's Place App 🔄
echo.

echo Checking for backup...
if exist "android\gradle.properties.backup" (
    echo Restoring from backup...
    copy "android\gradle.properties.backup" "android\gradle.properties"
    echo ✅ Normal mode restored from backup
) else (
    echo No backup found. Creating default configuration...
    echo # Gradle JVM settings > "android\gradle.properties"
    echo org.gradle.jvmargs=-Xmx2G -XX:MaxMetaspaceSize=1G -XX:ReservedCodeCacheSize=256m -XX:+UseG1GC -Dfile.encoding=UTF-8 >> "android\gradle.properties"
    echo. >> "android\gradle.properties"
    echo # Android settings >> "android\gradle.properties"
    echo android.useAndroidX=true >> "android\gradle.properties"
    echo android.enableJetifier=true >> "android\gradle.properties"
    echo. >> "android\gradle.properties"
    echo # Performance settings >> "android\gradle.properties"
    echo org.gradle.parallel=false >> "android\gradle.properties"
    echo org.gradle.caching=false >> "android\gradle.properties"
    echo org.gradle.configureondemand=false >> "android\gradle.properties"
    echo. >> "android\gradle.properties"
    echo # Daemon settings >> "android\gradle.properties"
    echo org.gradle.daemon=true >> "android\gradle.properties"
    echo ✅ Default configuration created
)
echo.

echo Current memory settings:
type "android\gradle.properties"
echo.

echo 🎯 Normal memory mode restored!
pause
