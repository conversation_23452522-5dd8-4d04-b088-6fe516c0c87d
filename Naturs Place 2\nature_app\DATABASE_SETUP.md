# Database Setup for Nature's Place Commission System

This document explains how to set up and use the database integration for the Nature's Place app commission system.

## Overview

The commission system now supports both local SQLite storage (for mobile devices) and PostgreSQL (for server-side operations and analytics).

## PostgreSQL Setup

### 1. Create the Database

Use the SQL command you provided to create the PostgreSQL database:

```sql
CREATE DATABASE "Natures Place"
    WITH
    OWNER = postgres
    ENCODING = 'UTF8'
    LC_COLLATE = 'English_Canada.1252'
    LC_CTYPE = 'English_Canada.1252'
    LOCALE_PROVIDER = 'libc'
    TABLESPACE = pg_default
    CONNECTION LIMIT = -1
    IS_TEMPLATE = False;
```

### 2. Run the Setup Script

Execute the `database_setup.sql` file to create all necessary tables:

```bash
psql -U postgres -d "Natures Place" -f database_setup.sql
```

### 3. Configure Connection

Set environment variables for PostgreSQL connection:

```bash
export POSTGRES_HOST=localhost
export POSTGRES_PORT=5432
export POSTGRES_DB="Natures Place"
export POSTGRES_USER=postgres
export POSTGRES_PASSWORD=your_password_here
```

## Database Architecture

### Tables Created

1. **transactions** - Stores all transaction records
2. **commissions** - Stores commission calculations for vendors and affiliates
3. **payouts** - Stores batch payout records
4. **vendors** - Stores vendor information and settings
5. **affiliates** - Stores affiliate partner information

### Views Created

1. **commission_analytics** - Provides commission summary statistics
2. **vendor_performance** - Shows vendor performance metrics
3. **affiliate_performance** - Shows affiliate performance metrics

## Usage in Flutter App

### Initialize Services

```dart
// Initialize database services
final commissionService = CommissionService();
await commissionService.initialize();
```

### Process Transactions

```dart
final transaction = Transaction(
  id: 'txn_001',
  orderId: 'order_001',
  customerId: 'customer_001',
  vendorId: 'vendor_001',
  // ... other fields
);

await commissionService.processTransaction(transaction);
```

### Sync to PostgreSQL

```dart
// Sync local data to PostgreSQL
await commissionService.syncToPostgreSQL();
```

## Features

### Local Storage (SQLite)
- ✅ Offline support
- ✅ Fast local queries
- ✅ Automatic data persistence
- ✅ Cross-platform compatibility

### PostgreSQL Integration
- ✅ Server-side analytics
- ✅ Advanced reporting
- ✅ Data backup and recovery
- ✅ Multi-user access
- ✅ JSONB support for metadata

### Commission System
- ✅ Automatic commission calculation
- ✅ Vendor and affiliate support
- ✅ Batch payout processing
- ✅ Status tracking
- ✅ Performance analytics

## Database Schema

### Key Relationships

```
transactions (1) → (many) commissions
commissions (many) → (1) payouts
vendors (1) → (many) transactions
affiliates (1) → (many) transactions
```

### Indexes

Performance indexes are created on:
- Transaction vendor_id, affiliate_id, status
- Commission partner_id, status, transaction_id
- Payout partner_id, status
- Vendor and affiliate email addresses

## Analytics Queries

### Commission Summary
```sql
SELECT * FROM commission_analytics;
```

### Vendor Performance
```sql
SELECT * FROM vendor_performance WHERE total_sales > 1000;
```

### Affiliate Performance
```sql
SELECT * FROM affiliate_performance WHERE is_active = true;
```

## Maintenance

### Backup Database
```bash
pg_dump -U postgres "Natures Place" > natures_place_backup.sql
```

### Restore Database
```bash
psql -U postgres -d "Natures Place" < natures_place_backup.sql
```

### Monitor Performance
```sql
-- Check table sizes
SELECT schemaname, tablename, pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size
FROM pg_tables WHERE schemaname = 'public';

-- Check index usage
SELECT schemaname, tablename, indexname, idx_scan, idx_tup_read, idx_tup_fetch
FROM pg_stat_user_indexes;
```

## Security Considerations

1. **Connection Security**: Use SSL connections in production
2. **Access Control**: Implement proper user roles and permissions
3. **Data Encryption**: Consider encrypting sensitive data
4. **Backup Security**: Secure backup files and storage

## Troubleshooting

### Common Issues

1. **Connection Failed**: Check PostgreSQL service is running
2. **Permission Denied**: Verify user has proper database permissions
3. **Table Not Found**: Ensure setup script was executed successfully
4. **Sync Errors**: Check network connectivity and credentials

### Debug Mode

Enable debug logging by setting:
```dart
debugPrint('Commission system debug info');
```

## Next Steps

1. Implement real PostgreSQL connection in `postgresql_service.dart`
2. Add data validation and error handling
3. Implement connection pooling for better performance
4. Add automated backup scheduling
5. Create admin dashboard for database monitoring

## Support

For database-related issues:
1. Check the logs for error messages
2. Verify database connectivity
3. Ensure all required tables exist
4. Check data integrity constraints
