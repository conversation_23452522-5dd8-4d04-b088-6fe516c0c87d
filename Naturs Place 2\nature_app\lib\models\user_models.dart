/// User role enumeration for role-based access control
enum UserRole {
  customer,
  partner,
  vendor,
  admin,
}

/// User account status
enum UserStatus {
  active,
  inactive,
  suspended,
  pending,
}

/// Main user model for authentication and role management
class User {
  final String id;
  final String email;
  final String firstName;
  final String lastName;
  final UserRole role;
  final UserStatus status;
  final DateTime createdAt;
  final DateTime? lastLoginAt;
  final Map<String, dynamic> metadata;

  User({
    required this.id,
    required this.email,
    required this.firstName,
    required this.lastName,
    required this.role,
    required this.status,
    required this.createdAt,
    this.lastLoginAt,
    this.metadata = const {},
  });

  String get fullName => '$firstName $lastName';

  String get displayRole {
    switch (role) {
      case UserRole.customer:
        return 'Customer';
      case UserRole.partner:
        return 'Partner';
      case UserRole.vendor:
        return 'Vendor';
      case UserRole.admin:
        return 'Administrator';
    }
  }

  bool get isActive => status == UserStatus.active;

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'email': email,
      'firstName': firstName,
      'lastName': lastName,
      'role': role.name,
      'status': status.name,
      'createdAt': createdAt.toIso8601String(),
      'lastLoginAt': lastLoginAt?.toIso8601String(),
      'metadata': metadata,
    };
  }

  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      id: json['id'],
      email: json['email'],
      firstName: json['firstName'],
      lastName: json['lastName'],
      role: UserRole.values.firstWhere((e) => e.name == json['role']),
      status: UserStatus.values.firstWhere((e) => e.name == json['status']),
      createdAt: DateTime.parse(json['createdAt']),
      lastLoginAt: json['lastLoginAt'] != null ? DateTime.parse(json['lastLoginAt']) : null,
      metadata: json['metadata'] ?? {},
    );
  }
}

/// Partner-specific user profile
class PartnerUser extends User {
  final String? socialMediaHandle;
  final String? website;
  final String? audienceDescription;
  final int followerCount;
  final List<String> platforms;
  final double totalEarnings;
  final double conversionRate;
  final String? stripeAccountId;

  PartnerUser({
    required super.id,
    required super.email,
    required super.firstName,
    required super.lastName,
    required super.status,
    required super.createdAt,
    super.lastLoginAt,
    super.metadata,
    this.socialMediaHandle,
    this.website,
    this.audienceDescription,
    this.followerCount = 0,
    this.platforms = const [],
    this.totalEarnings = 0.0,
    this.conversionRate = 0.0,
    this.stripeAccountId,
  }) : super(role: UserRole.partner);

  String get performanceGrade {
    if (conversionRate >= 0.10) return 'A';
    if (conversionRate >= 0.05) return 'B';
    if (conversionRate >= 0.02) return 'C';
    return 'D';
  }

  @override
  Map<String, dynamic> toJson() {
    final json = super.toJson();
    json.addAll({
      'socialMediaHandle': socialMediaHandle,
      'website': website,
      'audienceDescription': audienceDescription,
      'followerCount': followerCount,
      'platforms': platforms,
      'totalEarnings': totalEarnings,
      'conversionRate': conversionRate,
      'stripeAccountId': stripeAccountId,
    });
    return json;
  }

  factory PartnerUser.fromJson(Map<String, dynamic> json) {
    return PartnerUser(
      id: json['id'],
      email: json['email'],
      firstName: json['firstName'],
      lastName: json['lastName'],
      status: UserStatus.values.firstWhere((e) => e.name == json['status']),
      createdAt: DateTime.parse(json['createdAt']),
      lastLoginAt: json['lastLoginAt'] != null ? DateTime.parse(json['lastLoginAt']) : null,
      metadata: json['metadata'] ?? {},
      socialMediaHandle: json['socialMediaHandle'],
      website: json['website'],
      audienceDescription: json['audienceDescription'],
      followerCount: json['followerCount'] ?? 0,
      platforms: List<String>.from(json['platforms'] ?? []),
      totalEarnings: (json['totalEarnings'] ?? 0.0).toDouble(),
      conversionRate: (json['conversionRate'] ?? 0.0).toDouble(),
      stripeAccountId: json['stripeAccountId'],
    );
  }
}

/// Vendor-specific user profile
class VendorUser extends User {
  final String businessName;
  final String businessAddress;
  final String taxId;
  final VendorTier tier;
  final double monthlyFee;
  final DateTime? lastPaymentDate;
  final DateTime? nextPaymentDate;
  final bool paymentCurrent;
  final double totalSales;
  final double totalCommissionOwed;
  final int productCount;

  VendorUser({
    required super.id,
    required super.email,
    required super.firstName,
    required super.lastName,
    required super.status,
    required super.createdAt,
    super.lastLoginAt,
    super.metadata,
    required this.businessName,
    required this.businessAddress,
    required this.taxId,
    required this.tier,
    required this.monthlyFee,
    this.lastPaymentDate,
    this.nextPaymentDate,
    this.paymentCurrent = false,
    this.totalSales = 0.0,
    this.totalCommissionOwed = 0.0,
    this.productCount = 0,
  }) : super(role: UserRole.vendor);

  bool get isPaymentOverdue {
    if (nextPaymentDate == null) return false;
    return DateTime.now().isAfter(nextPaymentDate!) && !paymentCurrent;
  }

  int get daysUntilPayment {
    if (nextPaymentDate == null) return 0;
    return nextPaymentDate!.difference(DateTime.now()).inDays;
  }

  @override
  Map<String, dynamic> toJson() {
    final json = super.toJson();
    json.addAll({
      'businessName': businessName,
      'businessAddress': businessAddress,
      'taxId': taxId,
      'tier': tier.name,
      'monthlyFee': monthlyFee,
      'lastPaymentDate': lastPaymentDate?.toIso8601String(),
      'nextPaymentDate': nextPaymentDate?.toIso8601String(),
      'paymentCurrent': paymentCurrent,
      'totalSales': totalSales,
      'totalCommissionOwed': totalCommissionOwed,
      'productCount': productCount,
    });
    return json;
  }

  factory VendorUser.fromJson(Map<String, dynamic> json) {
    return VendorUser(
      id: json['id'],
      email: json['email'],
      firstName: json['firstName'],
      lastName: json['lastName'],
      status: UserStatus.values.firstWhere((e) => e.name == json['status']),
      createdAt: DateTime.parse(json['createdAt']),
      lastLoginAt: json['lastLoginAt'] != null ? DateTime.parse(json['lastLoginAt']) : null,
      metadata: json['metadata'] ?? {},
      businessName: json['businessName'],
      businessAddress: json['businessAddress'],
      taxId: json['taxId'],
      tier: VendorTier.values.firstWhere((e) => e.name == json['tier']),
      monthlyFee: (json['monthlyFee'] ?? 0.0).toDouble(),
      lastPaymentDate: json['lastPaymentDate'] != null ? DateTime.parse(json['lastPaymentDate']) : null,
      nextPaymentDate: json['nextPaymentDate'] != null ? DateTime.parse(json['nextPaymentDate']) : null,
      paymentCurrent: json['paymentCurrent'] ?? false,
      totalSales: (json['totalSales'] ?? 0.0).toDouble(),
      totalCommissionOwed: (json['totalCommissionOwed'] ?? 0.0).toDouble(),
      productCount: json['productCount'] ?? 0,
    );
  }
}

/// Vendor tier enumeration
enum VendorTier {
  basic,
  standard,
  premium,
  enterprise,
}

/// Extension for vendor tier utilities
extension VendorTierExtension on VendorTier {
  String get displayName {
    switch (this) {
      case VendorTier.basic:
        return 'Basic';
      case VendorTier.standard:
        return 'Standard';
      case VendorTier.premium:
        return 'Premium';
      case VendorTier.enterprise:
        return 'Enterprise';
    }
  }

  double get monthlyFee {
    switch (this) {
      case VendorTier.basic:
        return 29.99;
      case VendorTier.standard:
        return 59.99;
      case VendorTier.premium:
        return 99.99;
      case VendorTier.enterprise:
        return 199.99;
    }
  }

  int get maxProducts {
    switch (this) {
      case VendorTier.basic:
        return 50;
      case VendorTier.standard:
        return 200;
      case VendorTier.premium:
        return 500;
      case VendorTier.enterprise:
        return 1000; // High limit for performance
    }
  }

  double get commissionRate {
    switch (this) {
      case VendorTier.basic:
        return 15.0;
      case VendorTier.standard:
        return 12.0;
      case VendorTier.premium:
        return 10.0;
      case VendorTier.enterprise:
        return 8.0;
    }
  }
}
