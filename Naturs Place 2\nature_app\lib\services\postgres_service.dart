import 'package:flutter/foundation.dart';
import 'package:postgres/postgres.dart';
import '../models/commission_models.dart';
import 'database_config_service.dart';

/// Simple PostgreSQL service for the Nature's Place commission system
class PostgreSQLService {
  static final PostgreSQLService _instance = PostgreSQLService._internal();
  factory PostgreSQLService() => _instance;
  PostgreSQLService._internal();

  final DatabaseConfigService _configService = DatabaseConfigService();
  Connection? _connection;
  bool _isInitialized = false;

  /// Initialize PostgreSQL connection
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      await _configService.initialize();
      await _establishConnection();
      await _createTablesIfNotExists();
      _isInitialized = true;
      debugPrint('✅ PostgreSQL Service initialized');
    } catch (e) {
      debugPrint('❌ PostgreSQL initialization failed: $e');
      // Don't rethrow - allow app to continue with local storage only
      debugPrint('📱 Continuing with local storage only');
    }
  }

  /// Establish connection to PostgreSQL
  Future<void> _establishConnection() async {
    final config = _configService.getPostgreSQLConfig();

    try {
      _connection = await Connection.open(
        Endpoint(
          host: config['host'],
          port: config['port'],
          database: config['database'],
          username: config['username'],
          password: config['password'],
        ),
        settings: ConnectionSettings(
          sslMode: SslMode.require,
          connectTimeout: Duration(seconds: config['connect_timeout']),
          queryTimeout: Duration(seconds: config['query_timeout']),
        ),
      );

      debugPrint('🔗 Connected to PostgreSQL: ${config['host']}:${config['port']}/${config['database']}');
    } catch (e) {
      debugPrint('❌ Failed to connect to PostgreSQL: $e');
      rethrow;
    }
  }

  /// Create database tables if they don't exist
  Future<void> _createTablesIfNotExists() async {
    if (_connection == null) {
      debugPrint('⚠️ No PostgreSQL connection available for table creation');
      return;
    }

    try {
      // Create transactions table
      await _connection!.execute('''
        CREATE TABLE IF NOT EXISTS transactions (
          id VARCHAR(255) PRIMARY KEY,
          order_id VARCHAR(255) NOT NULL,
          customer_id VARCHAR(255) NOT NULL,
          vendor_id VARCHAR(255) NOT NULL,
          affiliate_id VARCHAR(255),
          product_id VARCHAR(255) NOT NULL,
          product_name VARCHAR(500) NOT NULL,
          category VARCHAR(100) NOT NULL,
          product_price DECIMAL(10,2) NOT NULL,
          quantity INTEGER NOT NULL,
          subtotal DECIMAL(10,2) NOT NULL,
          tax DECIMAL(10,2) NOT NULL,
          shipping DECIMAL(10,2) NOT NULL,
          total DECIMAL(10,2) NOT NULL,
          discount DECIMAL(10,2) DEFAULT 0.0,
          coupon_code VARCHAR(100),
          status VARCHAR(50) NOT NULL,
          created_at TIMESTAMP NOT NULL,
          completed_at TIMESTAMP,
          cancelled_at TIMESTAMP,
          cancellation_reason TEXT,
          metadata JSONB NOT NULL DEFAULT '{}'::jsonb
        )
      ''');

      // Create commissions table
      await _connection!.execute('''
        CREATE TABLE IF NOT EXISTS commissions (
          id VARCHAR(255) PRIMARY KEY,
          transaction_id VARCHAR(255) NOT NULL,
          partner_id VARCHAR(255) NOT NULL,
          partner_type VARCHAR(50) NOT NULL,
          category VARCHAR(100) NOT NULL,
          sale_amount DECIMAL(10,2) NOT NULL,
          commission_rate DECIMAL(5,2) NOT NULL,
          commission_amount DECIMAL(10,2) NOT NULL,
          bonus_amount DECIMAL(10,2) DEFAULT 0.0,
          total_amount DECIMAL(10,2) NOT NULL,
          app_revenue DECIMAL(10,2) NOT NULL,
          status VARCHAR(50) NOT NULL,
          created_at TIMESTAMP NOT NULL,
          approved_at TIMESTAMP,
          paid_at TIMESTAMP,
          payout_id VARCHAR(255),
          notes TEXT,
          metadata JSONB NOT NULL DEFAULT '{}'::jsonb,
          FOREIGN KEY (transaction_id) REFERENCES transactions (id)
        )
      ''');

      // Create payouts table
      await _connection!.execute('''
        CREATE TABLE IF NOT EXISTS payouts (
          id VARCHAR(255) PRIMARY KEY,
          partner_id VARCHAR(255) NOT NULL,
          partner_type VARCHAR(50) NOT NULL,
          commission_ids TEXT[] NOT NULL,
          total_amount DECIMAL(10,2) NOT NULL,
          fees DECIMAL(10,2) DEFAULT 0.0,
          net_amount DECIMAL(10,2) NOT NULL,
          status VARCHAR(50) NOT NULL,
          payment_method VARCHAR(100) NOT NULL,
          payment_reference VARCHAR(255),
          payout_schedule VARCHAR(50) NOT NULL DEFAULT 'monthly',
          next_payout_date DATE,
          created_at TIMESTAMP NOT NULL,
          processed_at TIMESTAMP,
          completed_at TIMESTAMP,
          failure_reason TEXT,
          payment_details JSONB NOT NULL DEFAULT '{}'::jsonb,
          metadata JSONB NOT NULL DEFAULT '{}'::jsonb
        )
      ''');

      // Create payout schedules table for tracking 30-day cycles
      await _connection!.execute('''
        CREATE TABLE IF NOT EXISTS payout_schedules (
          id VARCHAR(255) PRIMARY KEY,
          partner_id VARCHAR(255) NOT NULL,
          partner_type VARCHAR(50) NOT NULL,
          schedule_type VARCHAR(50) NOT NULL DEFAULT 'monthly',
          cycle_start_date DATE NOT NULL,
          cycle_end_date DATE NOT NULL,
          next_payout_date DATE NOT NULL,
          total_commissions DECIMAL(10,2) DEFAULT 0.0,
          total_payouts DECIMAL(10,2) DEFAULT 0.0,
          pending_amount DECIMAL(10,2) DEFAULT 0.0,
          status VARCHAR(50) NOT NULL DEFAULT 'active',
          created_at TIMESTAMP NOT NULL,
          updated_at TIMESTAMP NOT NULL,
          metadata JSONB NOT NULL DEFAULT '{}'::jsonb
        )
      ''');

      // Create indexes
      await _connection!.execute('CREATE INDEX IF NOT EXISTS idx_transactions_vendor_id ON transactions(vendor_id)');
      await _connection!.execute('CREATE INDEX IF NOT EXISTS idx_transactions_affiliate_id ON transactions(affiliate_id)');
      await _connection!.execute('CREATE INDEX IF NOT EXISTS idx_commissions_partner_id ON commissions(partner_id)');
      await _connection!.execute('CREATE INDEX IF NOT EXISTS idx_commissions_status ON commissions(status)');
      await _connection!.execute('CREATE INDEX IF NOT EXISTS idx_payouts_partner_id ON payouts(partner_id)');
      await _connection!.execute('CREATE INDEX IF NOT EXISTS idx_payouts_next_payout_date ON payouts(next_payout_date)');
      await _connection!.execute('CREATE INDEX IF NOT EXISTS idx_payout_schedules_partner_id ON payout_schedules(partner_id)');
      await _connection!.execute('CREATE INDEX IF NOT EXISTS idx_payout_schedules_next_payout ON payout_schedules(next_payout_date)');

      debugPrint('✅ PostgreSQL tables and indexes created successfully');
    } catch (e) {
      debugPrint('❌ Failed to create PostgreSQL tables: $e');
      rethrow;
    }
  }

  /// Insert transaction into PostgreSQL
  Future<void> insertTransaction(Transaction transaction) async {
    if (_connection == null) {
      debugPrint('⚠️ No PostgreSQL connection available');
      return;
    }

    try {
      await _connection!.execute(
        '''
        INSERT INTO transactions (
          id, order_id, customer_id, vendor_id, affiliate_id, product_id,
          product_name, category, product_price, quantity, subtotal, tax,
          shipping, total, discount, coupon_code, status, created_at,
          completed_at, cancelled_at, cancellation_reason, metadata
        ) VALUES (
          \$1, \$2, \$3, \$4, \$5, \$6, \$7, \$8, \$9, \$10, \$11, \$12,
          \$13, \$14, \$15, \$16, \$17, \$18, \$19, \$20, \$21, \$22
        ) ON CONFLICT (id) DO UPDATE SET
          status = EXCLUDED.status,
          completed_at = EXCLUDED.completed_at
        ''',
        parameters: [
          transaction.id,
          transaction.orderId,
          transaction.customerId,
          transaction.vendorId,
          transaction.affiliateId,
          transaction.productId,
          transaction.productName,
          transaction.category.name,
          transaction.productPrice,
          transaction.quantity,
          transaction.subtotal,
          transaction.tax,
          transaction.shipping,
          transaction.total,
          transaction.discount,
          transaction.couponCode,
          transaction.status.name,
          transaction.createdAt.toIso8601String(),
          transaction.completedAt?.toIso8601String(),
          transaction.cancelledAt?.toIso8601String(),
          transaction.cancellationReason,
          transaction.metadata,
        ],
      );
      debugPrint('✅ Inserted transaction: ${transaction.id}');
    } catch (e) {
      debugPrint('❌ Failed to insert transaction: $e');
    }
  }

  /// Insert commission into PostgreSQL
  Future<void> insertCommission(Commission commission) async {
    if (_connection == null) {
      debugPrint('⚠️ No PostgreSQL connection available');
      return;
    }

    try {
      await _connection!.execute(
        '''
        INSERT INTO commissions (
          id, transaction_id, partner_id, partner_type, category, sale_amount,
          commission_rate, commission_amount, bonus_amount, total_amount,
          app_revenue, status, created_at, approved_at, paid_at, payout_id,
          notes, metadata
        ) VALUES (
          \$1, \$2, \$3, \$4, \$5, \$6, \$7, \$8, \$9, \$10, \$11, \$12,
          \$13, \$14, \$15, \$16, \$17, \$18
        ) ON CONFLICT (id) DO UPDATE SET
          status = EXCLUDED.status,
          paid_at = EXCLUDED.paid_at,
          payout_id = EXCLUDED.payout_id
        ''',
        parameters: [
          commission.id,
          commission.transactionId,
          commission.partnerId,
          commission.partnerType,
          commission.category.name,
          commission.saleAmount,
          commission.commissionRate,
          commission.commissionAmount,
          commission.bonusAmount,
          commission.totalAmount,
          commission.appRevenue,
          commission.status.name,
          commission.createdAt.toIso8601String(),
          commission.approvedAt?.toIso8601String(),
          commission.paidAt?.toIso8601String(),
          commission.payoutId,
          commission.notes,
          commission.metadata is String ? commission.metadata : commission.metadata.toString(),
        ],
      );
      debugPrint('✅ Inserted commission: ${commission.id}');
    } catch (e) {
      debugPrint('❌ Failed to insert commission: $e');
    }
  }

  /// Insert payout into PostgreSQL
  Future<void> insertPayout(Payout payout) async {
    if (_connection == null) {
      debugPrint('⚠️ No PostgreSQL connection available');
      return;
    }

    try {
      await _connection!.execute(
        '''
        INSERT INTO payouts (
          id, partner_id, partner_type, commission_ids, total_amount, fees,
          net_amount, status, payment_method, payment_reference, created_at,
          processed_at, completed_at, failure_reason, payment_details, metadata
        ) VALUES (
          \$1, \$2, \$3, \$4, \$5, \$6, \$7, \$8, \$9, \$10, \$11, \$12,
          \$13, \$14, \$15, \$16
        ) ON CONFLICT (id) DO UPDATE SET
          status = EXCLUDED.status,
          processed_at = EXCLUDED.processed_at,
          completed_at = EXCLUDED.completed_at
        ''',
        parameters: [
          payout.id,
          payout.partnerId,
          payout.partnerType,
          payout.commissionIds,
          payout.totalAmount,
          payout.fees,
          payout.netAmount,
          payout.status.name,
          payout.paymentMethod,
          payout.paymentReference,
          payout.createdAt.toIso8601String(),
          payout.processedAt?.toIso8601String(),
          payout.completedAt?.toIso8601String(),
          payout.failureReason,
          payout.paymentDetails,
          payout.metadata,
        ],
      );
      debugPrint('✅ Inserted payout: ${payout.id}');
    } catch (e) {
      debugPrint('❌ Failed to insert payout: $e');
    }
  }

  /// Sync data to PostgreSQL
  Future<void> syncToPostgreSQL(List<Transaction> transactions, List<Commission> commissions, List<Payout> payouts) async {
    if (_connection == null) {
      debugPrint('⚠️ No PostgreSQL connection available for sync');
      return;
    }

    debugPrint('🔄 Syncing ${transactions.length} transactions, ${commissions.length} commissions, ${payouts.length} payouts to PostgreSQL');

    try {
      // Insert transactions
      for (final transaction in transactions) {
        await insertTransaction(transaction);
      }

      // Insert commissions
      for (final commission in commissions) {
        await insertCommission(commission);
      }

      // Insert payouts
      for (final payout in payouts) {
        await insertPayout(payout);
      }

      debugPrint('✅ Successfully synced data to PostgreSQL');
    } catch (e) {
      debugPrint('❌ Failed to sync data to PostgreSQL: $e');
      rethrow;
    }
  }

  /// Insert payout schedule
  Future<void> insertPayoutSchedule({
    required String id,
    required String partnerId,
    required String partnerType,
    required String scheduleType,
    required DateTime cycleStartDate,
    required DateTime cycleEndDate,
    required DateTime nextPayoutDate,
    required String status,
  }) async {
    if (_connection == null) {
      debugPrint('⚠️ No PostgreSQL connection available');
      return;
    }

    try {
      await _connection!.execute(
        '''
        INSERT INTO payout_schedules (
          id, partner_id, partner_type, schedule_type, cycle_start_date,
          cycle_end_date, next_payout_date, status, created_at, updated_at
        ) VALUES (
          \$1, \$2, \$3, \$4, \$5, \$6, \$7, \$8, \$9, \$10
        ) ON CONFLICT (id) DO UPDATE SET
          next_payout_date = EXCLUDED.next_payout_date,
          updated_at = EXCLUDED.updated_at
        ''',
        parameters: [
          id,
          partnerId,
          partnerType,
          scheduleType,
          cycleStartDate.toIso8601String().split('T')[0],
          cycleEndDate.toIso8601String().split('T')[0],
          nextPayoutDate.toIso8601String().split('T')[0],
          status,
          DateTime.now().toIso8601String(),
          DateTime.now().toIso8601String(),
        ],
      );
      debugPrint('✅ Inserted payout schedule: $id');
    } catch (e) {
      debugPrint('❌ Failed to insert payout schedule: $e');
    }
  }

  /// Get partners due for payout
  Future<List<Map<String, dynamic>>> getPartnersDueForPayout(DateTime date) async {
    if (_connection == null) {
      debugPrint('⚠️ No PostgreSQL connection available');
      return [];
    }

    try {
      final result = await _connection!.execute(
        '''
        SELECT partner_id, partner_type, next_payout_date, pending_amount
        FROM payout_schedules
        WHERE next_payout_date <= \$1 AND status = 'active'
        ORDER BY next_payout_date ASC
        ''',
        parameters: [date.toIso8601String().split('T')[0]],
      );

      return result.map((row) => {
        'partner_id': row[0],
        'partner_type': row[1],
        'next_payout_date': row[2],
        'pending_amount': row[3],
      }).toList();
    } catch (e) {
      debugPrint('❌ Failed to get partners due for payout: $e');
      return [];
    }
  }

  /// Get pending commissions for a partner
  Future<List<Map<String, dynamic>>> getPendingCommissions(String partnerId, String partnerType) async {
    if (_connection == null) {
      debugPrint('⚠️ No PostgreSQL connection available');
      return [];
    }

    try {
      final result = await _connection!.execute(
        '''
        SELECT id, transaction_id, commission_amount, total_amount, created_at
        FROM commissions
        WHERE partner_id = \$1 AND partner_type = \$2 AND status = 'pending'
        ORDER BY created_at ASC
        ''',
        parameters: [partnerId, partnerType],
      );

      return result.map((row) => {
        'id': row[0],
        'transaction_id': row[1],
        'commission_amount': row[2],
        'total_amount': row[3],
        'created_at': row[4],
      }).toList();
    } catch (e) {
      debugPrint('❌ Failed to get pending commissions: $e');
      return [];
    }
  }

  /// Mark commission as paid
  Future<void> markCommissionAsPaid(String commissionId, String payoutId) async {
    if (_connection == null) {
      debugPrint('⚠️ No PostgreSQL connection available');
      return;
    }

    try {
      await _connection!.execute(
        '''
        UPDATE commissions
        SET status = 'paid', paid_at = \$1, payout_id = \$2
        WHERE id = \$3
        ''',
        parameters: [
          DateTime.now().toIso8601String(),
          payoutId,
          commissionId,
        ],
      );
      debugPrint('✅ Marked commission as paid: $commissionId');
    } catch (e) {
      debugPrint('❌ Failed to mark commission as paid: $e');
    }
  }

  /// Mark commission as cancelled
  Future<void> markCommissionAsCancelled(String commissionId, String reason) async {
    if (_connection == null) {
      debugPrint('⚠️ No PostgreSQL connection available');
      return;
    }

    try {
      await _connection!.execute(
        '''
        UPDATE commissions
        SET status = 'cancelled', notes = CONCAT(COALESCE(notes, ''), ' - CANCELLED: ', \$1)
        WHERE id = \$2
        ''',
        parameters: [
          reason,
          commissionId,
        ],
      );
      debugPrint('✅ Marked commission as cancelled: $commissionId');
    } catch (e) {
      debugPrint('❌ Failed to mark commission as cancelled: $e');
    }
  }

  /// Update payout schedule for next cycle
  Future<void> updatePayoutSchedule({
    required String partnerId,
    required DateTime cycleStartDate,
    required DateTime cycleEndDate,
    required DateTime nextPayoutDate,
  }) async {
    if (_connection == null) {
      debugPrint('⚠️ No PostgreSQL connection available');
      return;
    }

    try {
      await _connection!.execute(
        '''
        UPDATE payout_schedules
        SET cycle_start_date = \$1, cycle_end_date = \$2, next_payout_date = \$3, updated_at = \$4
        WHERE partner_id = \$5 AND status = 'active'
        ''',
        parameters: [
          cycleStartDate.toIso8601String().split('T')[0],
          cycleEndDate.toIso8601String().split('T')[0],
          nextPayoutDate.toIso8601String().split('T')[0],
          DateTime.now().toIso8601String(),
          partnerId,
        ],
      );
      debugPrint('✅ Updated payout schedule for partner: $partnerId');
    } catch (e) {
      debugPrint('❌ Failed to update payout schedule: $e');
    }
  }

  /// Get payout schedule for a partner
  Future<Map<String, dynamic>?> getPayoutSchedule(String partnerId) async {
    if (_connection == null) {
      debugPrint('⚠️ No PostgreSQL connection available');
      return null;
    }

    try {
      final result = await _connection!.execute(
        '''
        SELECT id, schedule_type, cycle_start_date, cycle_end_date, next_payout_date, pending_amount, status
        FROM payout_schedules
        WHERE partner_id = \$1 AND status = 'active'
        LIMIT 1
        ''',
        parameters: [partnerId],
      );

      if (result.isNotEmpty) {
        final row = result.first;
        return {
          'id': row[0],
          'schedule_type': row[1],
          'cycle_start_date': row[2],
          'cycle_end_date': row[3],
          'next_payout_date': row[4],
          'pending_amount': row[5],
          'status': row[6],
        };
      }
      return null;
    } catch (e) {
      debugPrint('❌ Failed to get payout schedule: $e');
      return null;
    }
  }

  /// Get payout history for a partner
  Future<List<Map<String, dynamic>>> getPayoutHistory(String partnerId) async {
    if (_connection == null) {
      debugPrint('⚠️ No PostgreSQL connection available');
      return [];
    }

    try {
      final result = await _connection!.execute(
        '''
        SELECT id, total_amount, net_amount, status, payment_method, created_at, completed_at
        FROM payouts
        WHERE partner_id = \$1
        ORDER BY created_at DESC
        LIMIT 50
        ''',
        parameters: [partnerId],
      );

      return result.map((row) => {
        'id': row[0],
        'total_amount': row[1],
        'net_amount': row[2],
        'status': row[3],
        'payment_method': row[4],
        'created_at': row[5],
        'completed_at': row[6],
      }).toList();
    } catch (e) {
      debugPrint('❌ Failed to get payout history: $e');
      return [];
    }
  }

  /// Test connection to PostgreSQL
  Future<bool> testConnection() async {
    try {
      if (_connection == null) {
        await _establishConnection();
      }
      await _connection!.execute('SELECT 1');
      return true;
    } catch (e) {
      debugPrint('❌ PostgreSQL connection test failed: $e');
      return false;
    }
  }

  /// Close PostgreSQL connection
  Future<void> close() async {
    if (_connection != null) {
      await _connection!.close();
      _connection = null;
    }
    _isInitialized = false;
    debugPrint('🔌 PostgreSQL service closed');
  }
}
