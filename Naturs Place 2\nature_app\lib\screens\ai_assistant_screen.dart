import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import '../providers/app_state.dart';
import '../services/holistic_ai_service.dart';
import 'subscription_screen.dart';

class AIAssistantScreen extends StatefulWidget {
  const AIAssistantScreen({super.key});

  @override
  State<AIAssistantScreen> createState() => _AIAssistantScreenState();
}

class _AIAssistantScreenState extends State<AIAssistantScreen> {
  final TextEditingController _messageController = TextEditingController();
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    // Add initial welcome message if chat is empty
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final appState = Provider.of<AppState>(context, listen: false);
      if (appState.chatMessages.isEmpty) {
        appState.addChatMessage(
          ChatMessage(
            id: '1',
            message: "Hi there! I'm your plant and natural remedy assistant. How can I help you today?",
            isUser: false,
            timestamp: DateTime.now(),
          ),
        );
      }
    });
  }

  void _sendMessage() {
    final message = _messageController.text.trim();
    if (message.isEmpty) return;

    final appState = Provider.of<AppState>(context, listen: false);

    // Check AI question limits for free users
    if (!appState.isPremium && !appState.canAskAiThisMonth) {
      _showAiLimitDialog();
      return;
    }

    // Use an AI question for free users
    if (!appState.isPremium) {
      appState.useAiQuestion();
    }

    // Add user message
    appState.addChatMessage(
      ChatMessage(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        message: message,
        isUser: true,
        timestamp: DateTime.now(),
      ),
    );

    _messageController.clear();

    // Generate enhanced holistic AI response
    _generateEnhancedAIResponse(message, appState);
  }

  /// Generate enhanced holistic AI response using the advanced service
  Future<void> _generateEnhancedAIResponse(String message, AppState appState) async {
    try {
      // Show typing indicator
      appState.addChatMessage(
        ChatMessage(
          id: 'typing_${DateTime.now().millisecondsSinceEpoch}',
          message: '🤔 Analyzing your question and preparing a comprehensive holistic response...',
          isUser: false,
          timestamp: DateTime.now(),
        ),
      );

      // Generate response using holistic AI service
      final holisticAI = HolisticAIService();
      final response = await holisticAI.generateHolisticResponse(message);

      // Remove typing indicator
      appState.chatMessages.removeWhere((msg) => msg.id.startsWith('typing_'));

      // Add the comprehensive response
      appState.addChatMessage(
        ChatMessage(
          id: DateTime.now().millisecondsSinceEpoch.toString(),
          message: response,
          isUser: false,
          timestamp: DateTime.now(),
        ),
      );

      // Scroll to bottom
      _scrollToBottom();
    } catch (e) {
      // Remove typing indicator on error
      appState.chatMessages.removeWhere((msg) => msg.id.startsWith('typing_'));

      // Add error response
      appState.addChatMessage(
        ChatMessage(
          id: DateTime.now().millisecondsSinceEpoch.toString(),
          message: _getErrorResponse(),
          isUser: false,
          timestamp: DateTime.now(),
        ),
      );

      _scrollToBottom();
    }
  }

  /// Get error response for AI failures
  String _getErrorResponse() {
    return '''I apologize, but I'm having trouble processing your question right now. Here's some general guidance:

🌿 **For immediate health concerns**: Please consult with a qualified healthcare provider

💡 **Natural health topics I can help with**:
• Medicinal plants and herbs
• Holistic wellness approaches
• Nutritional guidance
• Mind-body practices
• Natural remedies

Please try rephrasing your question, and I'll do my best to provide comprehensive holistic guidance!

💡 **Health Information Notice**
This guidance is for educational purposes. Always consult with a healthcare professional for personalized medical advice.''';
  }

  /// Show AI limit dialog for free users
  void _showAiLimitDialog() {
    final appState = Provider.of<AppState>(context, listen: false);

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.lock, color: Colors.orange),
            SizedBox(width: 8),
            Text('Monthly AI Question Limit Reached'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('You\'ve used all ${appState.freePlanAiQuestionLimit} free AI questions for this month.'),
            const SizedBox(height: 16),
            const Text('Upgrade to Premium for:'),
            const SizedBox(height: 8),
            const Text('✅ Unlimited AI assistant questions'),
            const Text('✅ Advanced holistic health guidance'),
            const Text('✅ Personalized recommendations'),
            const Text('✅ Priority response times'),
            const Text('✅ Access to clinical research'),
            const SizedBox(height: 16),
            const Text('Your questions will reset next month.'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Maybe Later'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const SubscriptionScreen(),
                ),
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF22c55e),
              foregroundColor: Colors.white,
            ),
            child: const Text('Upgrade Now'),
          ),
        ],
      ),
    );
  }

  void _scrollToBottom() {
    if (_scrollController.hasClients) {
      _scrollController.animateTo(
        _scrollController.position.maxScrollExtent,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeOut,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Consumer<AppState>(
          builder: (context, appState, child) {
            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Row(
                  children: [
                    FaIcon(FontAwesomeIcons.robot, size: 20),
                    SizedBox(width: 8),
                    Text('AI Plant Assistant'),
                  ],
                ),
                if (!appState.isPremium)
                  Text(
                    '${appState.aiQuestionsRemainingThisMonth} questions remaining this month',
                    style: const TextStyle(
                      fontSize: 12,
                      color: Colors.grey,
                    ),
                  ),
              ],
            );
          },
        ),
        actions: [
          Consumer<AppState>(
            builder: (context, appState, child) {
              if (!appState.isPremium) {
                return Container(
                  margin: const EdgeInsets.only(right: 8),
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: appState.canAskAiThisMonth ? Colors.blue : Colors.red,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    '${appState.aiQuestionsRemainingThisMonth} left',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                );
              }
              return Container(
                margin: const EdgeInsets.only(right: 8),
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: const Color(0xFF22c55e),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  'Unlimited',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              );
            },
          ),
          PopupMenuButton(
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'clear',
                child: Row(
                  children: [
                    Icon(Icons.clear_all),
                    SizedBox(width: 8),
                    Text('Clear Chat'),
                  ],
                ),
              ),
            ],
            onSelected: (value) {
              if (value == 'clear') {
                Provider.of<AppState>(context, listen: false).clearChat();
              }
            },
          ),
        ],
      ),
      body: Column(
        children: [
          // Chat messages
          Expanded(
            child: Consumer<AppState>(
              builder: (context, appState, child) {
                return ListView.builder(
                  controller: _scrollController,
                  padding: const EdgeInsets.all(16),
                  itemCount: appState.chatMessages.length,
                  itemBuilder: (context, index) {
                    final message = appState.chatMessages[index];
                    return _buildMessageBubble(message);
                  },
                );
              },
            ),
          ),
          
          // Message input
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white,
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.withValues(alpha: 0.1),
                  blurRadius: 4,
                  offset: const Offset(0, -2),
                ),
              ],
            ),
            child: Consumer<AppState>(
              builder: (context, appState, child) {
                final canSend = appState.isPremium || appState.canAskAiThisMonth;

                return Row(
                  children: [
                    Expanded(
                      child: TextField(
                        controller: _messageController,
                        enabled: canSend,
                        decoration: InputDecoration(
                          hintText: canSend
                            ? 'Ask about plants or remedies...'
                            : 'Monthly question limit reached. Upgrade for unlimited access.',
                          border: const OutlineInputBorder(),
                          contentPadding: const EdgeInsets.symmetric(
                            horizontal: 16,
                            vertical: 12,
                          ),
                        ),
                        maxLines: null,
                        textInputAction: TextInputAction.send,
                        onSubmitted: canSend ? (_) => _sendMessage() : null,
                      ),
                    ),
                    const SizedBox(width: 8),
                    IconButton(
                      onPressed: canSend ? _sendMessage : null,
                      icon: const Icon(Icons.send),
                      style: IconButton.styleFrom(
                        backgroundColor: canSend ? const Color(0xFF22c55e) : Colors.grey,
                        foregroundColor: Colors.white,
                      ),
                    ),
                  ],
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMessageBubble(ChatMessage message) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (!message.isUser) ...[
            CircleAvatar(
              radius: 16,
              backgroundColor: const Color(0xFF22c55e).withValues(alpha: 0.1),
              child: const FaIcon(
                FontAwesomeIcons.robot,
                color: Color(0xFF22c55e),
                size: 16,
              ),
            ),
            const SizedBox(width: 8),
          ],
          Expanded(
            child: Column(
              crossAxisAlignment: message.isUser 
                  ? CrossAxisAlignment.end 
                  : CrossAxisAlignment.start,
              children: [
                Text(
                  message.isUser ? 'You' : "Nature's AI",
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 4),
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: message.isUser 
                        ? const Color(0xFF22c55e)
                        : Colors.grey[100],
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    message.message,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: message.isUser ? Colors.white : Colors.black87,
                    ),
                  ),
                ),
              ],
            ),
          ),
          if (message.isUser) ...[
            const SizedBox(width: 8),
            CircleAvatar(
              radius: 16,
              backgroundColor: Colors.grey[200],
              child: const Icon(
                Icons.person,
                color: Colors.grey,
                size: 16,
              ),
            ),
          ],
        ],
      ),
    );
  }

  @override
  void dispose() {
    _messageController.dispose();
    _scrollController.dispose();
    super.dispose();
  }
}
