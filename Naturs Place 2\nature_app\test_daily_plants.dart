import 'dart:io';
import 'lib/services/plant_data_service.dart';
import 'lib/services/daily_plant_update_service.dart';

/// Simple test script to verify daily plant update functionality
void main() async {
  print('🌿 Testing Daily Plant Update System...\n');
  
  try {
    // Initialize services
    print('📱 Initializing services...');
    final plantDataService = PlantDataService();
    final dailyUpdateService = DailyPlantUpdateService();
    
    await plantDataService.initialize();
    await dailyUpdateService.initialize();
    
    print('✅ Services initialized successfully\n');
    
    // Get statistics
    print('📊 Current Statistics:');
    final stats = dailyUpdateService.getStatistics();
    print('   • Service Running: ${stats['isServiceRunning']}');
    print('   • Total Plants Added: ${stats['totalPlantsAdded']}');
    print('   • Today\'s Plants: ${stats['todaysPlants']}');
    print('   • Total Plants: ${stats['totalPlants']}');
    if (stats['lastUpdateDate'] != null) {
      print('   • Last Update: ${stats['lastUpdateDate']}');
    }
    print('');
    
    // Get today's featured plants
    print('🌟 Today\'s Featured Plants:');
    final featuredPlants = dailyUpdateService.getTodaysFeaturedPlants();
    if (featuredPlants.isNotEmpty) {
      for (int i = 0; i < featuredPlants.length; i++) {
        final plant = featuredPlants[i];
        print('   ${i + 1}. ${plant['name']} (${plant['scientificName']})');
        print('      Category: ${plant['category']}');
        print('      Benefits: ${plant['benefits'].join(', ')}');
        print('');
      }
    } else {
      print('   No featured plants for today\n');
    }
    
    // Get plant of the day
    print('⭐ Plant of the Day:');
    final plantOfTheDay = dailyUpdateService.getPlantOfTheDay();
    if (plantOfTheDay != null) {
      print('   Name: ${plantOfTheDay['name']}');
      print('   Scientific Name: ${plantOfTheDay['scientificName']}');
      print('   Category: ${plantOfTheDay['category']}');
      print('   Key Benefit: ${plantOfTheDay['keyBenefit']}');
      print('   Fun Fact: ${plantOfTheDay['funFact']}');
      print('   Daily Tip: ${plantOfTheDay['dailyTip']}');
      print('');
    } else {
      print('   No plant of the day available\n');
    }
    
    // Test force update
    print('🔄 Testing Force Update...');
    await dailyUpdateService.forceUpdate();
    print('✅ Force update completed\n');
    
    // Get updated statistics
    print('📊 Updated Statistics:');
    final updatedStats = dailyUpdateService.getStatistics();
    print('   • Service Running: ${updatedStats['isServiceRunning']}');
    print('   • Total Plants Added: ${updatedStats['totalPlantsAdded']}');
    print('   • Today\'s Plants: ${updatedStats['todaysPlants']}');
    print('   • Total Plants: ${updatedStats['totalPlants']}');
    if (updatedStats['lastUpdateDate'] != null) {
      print('   • Last Update: ${updatedStats['lastUpdateDate']}');
    }
    print('');
    
    // Check for notifications
    print('🔔 Checking Notifications:');
    final notification = await dailyUpdateService.getNewPlantsNotification();
    if (notification != null) {
      print('   Message: ${notification['message']}');
      print('   Count: ${notification['count']}');
      print('   Date: ${notification['date']}');
      print('');
    } else {
      print('   No notifications available\n');
    }
    
    // Test all plants access
    print('🌱 Testing Plant Data Access:');
    final allPlants = plantDataService.getAllPlants();
    print('   • Total plants accessible: ${allPlants.length}');
    
    if (allPlants.isNotEmpty) {
      print('   • Sample plants:');
      for (int i = 0; i < 3 && i < allPlants.length; i++) {
        final plant = allPlants[i];
        print('     - ${plant.name} (${plant.scientificName})');
      }
    }
    print('');
    
    print('🎉 Daily Plant Update System Test Completed Successfully!');
    print('');
    print('Summary:');
    print('✅ Services initialize correctly');
    print('✅ Daily plants are generated and stored');
    print('✅ Statistics are tracked properly');
    print('✅ Featured plants and plant of the day work');
    print('✅ Force update functionality works');
    print('✅ Notifications system works');
    print('✅ Plant data is accessible');
    print('');
    print('🌿 Your daily healing plant update system is working perfectly!');
    
  } catch (e, stackTrace) {
    print('❌ Error during testing: $e');
    print('Stack trace: $stackTrace');
    exit(1);
  }
}
