import 'dart:async';
import 'package:flutter/material.dart';
import '../scripts/ai_growth_script.dart';
import '../services/ai_learning_engine.dart';
import '../services/holistic_ai_service.dart';

/// Example integration of AI Growth Script with the main application
/// This demonstrates how to implement continuous learning in your app
class AIGrowthIntegrationExample {
  
  /// Initialize AI growth system when app starts
  static Future<void> initializeAIGrowth() async {
    debugPrint('🚀 Initializing AI Growth System...');
    
    try {
      // Initialize the growth script
      final growthScript = AIGrowthScript();
      await growthScript.initialize();
      
      // Start the continuous learning process
      await growthScript.startGrowthProcess(
        interval: const Duration(hours: 6), // Learn every 6 hours
        enableAutoLearning: true,
        enablePatternAnalysis: true,
        enablePerformanceOptimization: true,
      );
      
      debugPrint('✅ AI Growth System initialized successfully');
    } catch (e) {
      debugPrint('❌ Error initializing AI Growth System: $e');
    }
  }

  /// Enhanced AI assistant query processing with learning
  static Future<String> processQueryWithLearning(String userQuery) async {
    try {
      // Use the learning engine for enhanced analysis
      final learningEngine = AILearningEngine();
      final analysis = await learningEngine.analyzeAndLearn(userQuery, null);
      
      // Generate response using holistic AI service
      final holisticAI = HolisticAIService();
      final response = await holisticAI.generateHolisticResponse(userQuery);
      
      // Log learning insights
      _logLearningInsights(analysis);
      
      return response;
    } catch (e) {
      debugPrint('❌ Error processing query with learning: $e');
      // Fallback to basic holistic AI
      final holisticAI = HolisticAIService();
      return await holisticAI.generateHolisticResponse(userQuery);
    }
  }

  /// Log learning insights for monitoring
  static void _logLearningInsights(dynamic analysis) {
    debugPrint('🧠 Learning Insights:');
    debugPrint('   Detected Conditions: ${analysis.detectedConditions.length}');
    debugPrint('   Detected Symptoms: ${analysis.detectedSymptoms.length}');
    debugPrint('   Identified Plants: ${analysis.identifiedPlants.length}');
    debugPrint('   Urgency Level: ${analysis.urgencyLevel}');
    debugPrint('   Overall Confidence: ${analysis.confidenceScores['overall']?.toStringAsFixed(2)}');
  }

  /// Daily learning report generation
  static Future<void> generateDailyLearningReport() async {
    final growthScript = AIGrowthScript();
    final stats = growthScript.getLearningStatistics();
    
    debugPrint('📊 === DAILY AI LEARNING REPORT ===');
    debugPrint('Date: ${DateTime.now().toLocal().toString().split(' ')[0]}');
    debugPrint('');
    debugPrint('🎯 QUERY ANALYSIS GROWTH:');
    debugPrint('   • Total Queries Processed: ${stats['totalQueries']}');
    debugPrint('   • Successful Analyses: ${stats['successfulAnalyses']}');
    debugPrint('   • Success Rate: ${_calculateSuccessRate(stats)}%');
    debugPrint('   • Average Confidence: ${stats['averageConfidence']?.toStringAsFixed(2)}');
    debugPrint('');
    debugPrint('🧠 KNOWLEDGE BASE EXPANSION:');
    debugPrint('   • Condition Patterns Learned: ${stats['conditionPatternsCount']}');
    debugPrint('   • Symptom Patterns Learned: ${stats['symptomPatternsCount']}');
    debugPrint('   • Plant Patterns Learned: ${stats['plantPatternsCount']}');
    debugPrint('');
    debugPrint('⚡ PERFORMANCE METRICS:');
    debugPrint('   • Emergency Queries Handled: ${stats['emergencyQueries']}');
    debugPrint('   • High Urgency Queries: ${stats['highUrgencyQueries']}');
    debugPrint('');
    debugPrint('🏆 TOP LEARNING AREAS:');
    
    final topConditions = stats['topConditions'] as List<dynamic>;
    if (topConditions.isNotEmpty) {
      debugPrint('   Most Detected Conditions:');
      for (int i = 0; i < topConditions.length && i < 5; i++) {
        final entry = topConditions[i];
        debugPrint('     ${i + 1}. ${entry.key}: ${entry.value} detections');
      }
    }
    
    final topSymptoms = stats['topSymptoms'] as List<dynamic>;
    if (topSymptoms.isNotEmpty) {
      debugPrint('   Most Recognized Symptoms:');
      for (int i = 0; i < topSymptoms.length && i < 5; i++) {
        final entry = topSymptoms[i];
        debugPrint('     ${i + 1}. ${entry.key}: ${entry.value} recognitions');
      }
    }
    
    final topPlants = stats['topPlants'] as List<dynamic>;
    if (topPlants.isNotEmpty) {
      debugPrint('   Most Mentioned Plants:');
      for (int i = 0; i < topPlants.length && i < 5; i++) {
        final entry = topPlants[i];
        debugPrint('     ${i + 1}. ${entry.key}: ${entry.value} mentions');
      }
    }
    
    debugPrint('=====================================');
  }

  /// Calculate success rate percentage
  static String _calculateSuccessRate(Map<String, dynamic> stats) {
    final total = stats['totalQueries'] as int;
    final successful = stats['successfulAnalyses'] as int;
    
    if (total == 0) return '0.0';
    return ((successful / total) * 100).toStringAsFixed(1);
  }

  /// Weekly learning optimization
  static Future<void> performWeeklyOptimization() async {
    debugPrint('🔧 Performing weekly AI optimization...');
    
    final growthScript = AIGrowthScript();
    final stats = growthScript.getLearningStatistics();
    
    // Check if additional training is needed
    final avgConfidence = stats['averageConfidence'] as double;
    if (avgConfidence < 0.75) {
      debugPrint('📚 Average confidence below threshold, performing additional training...');
      
      // Restart growth process with more intensive training
      growthScript.stopGrowthProcess();
      await growthScript.startGrowthProcess(
        interval: const Duration(hours: 2), // More frequent training
        enableAutoLearning: true,
        enablePatternAnalysis: true,
        enablePerformanceOptimization: true,
      );
    }
    
    debugPrint('✅ Weekly optimization completed');
  }

  /// Export learning data for backup
  static Future<String> backupLearningData() async {
    final growthScript = AIGrowthScript();
    final backupData = await growthScript.exportLearningData();
    
    debugPrint('💾 Learning data backup created');
    debugPrint('   Backup size: ${backupData.length} characters');
    
    return backupData;
  }

  /// Restore learning data from backup
  static Future<void> restoreLearningData(String backupData) async {
    try {
      final growthScript = AIGrowthScript();
      await growthScript.importLearningData(backupData);
      
      debugPrint('✅ Learning data restored successfully');
    } catch (e) {
      debugPrint('❌ Error restoring learning data: $e');
    }
  }

  /// Monitor AI performance and trigger improvements
  static Future<void> monitorAndImprove() async {
    final growthScript = AIGrowthScript();
    final stats = growthScript.getLearningStatistics();
    
    // Performance thresholds
    const minConditionPatterns = 20;
    const minSymptomPatterns = 30;
    const minPlantPatterns = 50;
    const minConfidence = 0.70;
    
    bool needsImprovement = false;
    
    // Check condition detection capability
    if (stats['conditionPatternsCount'] < minConditionPatterns) {
      debugPrint('⚠️ Condition detection needs improvement');
      needsImprovement = true;
    }
    
    // Check symptom recognition capability
    if (stats['symptomPatternsCount'] < minSymptomPatterns) {
      debugPrint('⚠️ Symptom recognition needs improvement');
      needsImprovement = true;
    }
    
    // Check plant identification capability
    if (stats['plantPatternsCount'] < minPlantPatterns) {
      debugPrint('⚠️ Plant identification needs improvement');
      needsImprovement = true;
    }
    
    // Check overall confidence
    if (stats['averageConfidence'] < minConfidence) {
      debugPrint('⚠️ Overall confidence needs improvement');
      needsImprovement = true;
    }
    
    if (needsImprovement) {
      debugPrint('🔄 Triggering improvement cycle...');
      
      // Restart with more intensive learning
      growthScript.stopGrowthProcess();
      await growthScript.startGrowthProcess(
        interval: const Duration(minutes: 30), // Very frequent training
        enableAutoLearning: true,
        enablePatternAnalysis: true,
        enablePerformanceOptimization: true,
      );
      
      // Schedule return to normal after improvement period
      Timer(const Duration(hours: 4), () {
        growthScript.stopGrowthProcess();
        growthScript.startGrowthProcess(
          interval: const Duration(hours: 6), // Back to normal
          enableAutoLearning: true,
          enablePatternAnalysis: true,
          enablePerformanceOptimization: true,
        );
        debugPrint('🎯 Returned to normal learning schedule');
      });
    } else {
      debugPrint('✅ AI performance is within acceptable thresholds');
    }
  }

  /// Get real-time learning statistics for UI display
  static Map<String, dynamic> getRealTimeLearningStats() {
    final growthScript = AIGrowthScript();
    final stats = growthScript.getLearningStatistics();
    
    return {
      'isLearning': growthScript.isRunning,
      'totalQueries': stats['totalQueries'],
      'successRate': _calculateSuccessRate(stats),
      'averageConfidence': stats['averageConfidence'],
      'knowledgeAreas': {
        'conditions': stats['conditionPatternsCount'],
        'symptoms': stats['symptomPatternsCount'],
        'plants': stats['plantPatternsCount'],
      },
      'urgencyHandling': {
        'emergency': stats['emergencyQueries'],
        'high': stats['highUrgencyQueries'],
      },
      'lastUpdate': DateTime.now().toIso8601String(),
    };
  }

  /// Initialize learning from user feedback
  static Future<void> learnFromUserFeedback({
    required String queryId,
    required String originalQuery,
    required String responseId,
    required int rating, // 1-5 stars
    String? comment,
    Map<String, dynamic>? corrections,
  }) async {
    // This would integrate with the UserFeedback system
    // to improve AI responses based on user satisfaction
    
    debugPrint('📝 Learning from user feedback:');
    debugPrint('   Query: $originalQuery');
    debugPrint('   Rating: $rating/5');
    debugPrint('   Comment: ${comment ?? 'None'}');
    
    // Low ratings trigger additional learning
    if (rating <= 2) {
      debugPrint('⚠️ Low rating detected - triggering improvement learning');
      
      final learningEngine = AILearningEngine();
      await learningEngine.analyzeAndLearn(originalQuery, null);
    }
  }

  /// Cleanup old learning data to maintain performance
  static Future<void> cleanupOldLearningData() async {
    debugPrint('🧹 Cleaning up old learning data...');
    
    // This would implement logic to:
    // - Remove patterns older than X months
    // - Consolidate similar patterns
    // - Remove low-confidence patterns
    // - Optimize storage usage
    
    debugPrint('✅ Learning data cleanup completed');
  }
}
