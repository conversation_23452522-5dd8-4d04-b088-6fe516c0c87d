/* Mobile-First Layout Preservation for Web */
/* This CSS ensures the web version maintains the exact same layout as mobile */

/* Reset and base styles to match mobile */
* {
  box-sizing: border-box;
  -webkit-tap-highlight-color: transparent;
}

body {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  overflow-x: hidden;
}

/* Flutter app container - mobile-first */
flt-glass-pane {
  /* Ensure mobile-like scrolling behavior */
  -webkit-overflow-scrolling: touch;
  overflow-scrolling: touch;
}

/* Mobile-first responsive breakpoints */
/* Keep mobile layout for all screen sizes to maintain consistency */

/* Small screens (mobile) - default behavior */
@media screen and (max-width: 767px) {
  body {
    /* Mobile-optimized settings */
    touch-action: manipulation;
  }
}

/* Medium screens (tablets) - maintain mobile layout */
@media screen and (min-width: 768px) and (max-width: 1023px) {
  body {
    /* Keep mobile layout even on tablets */
    max-width: 414px; /* iPhone Pro Max width */
    margin: 0 auto;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
  }
}

/* Large screens (desktop) - mobile layout in center */
@media screen and (min-width: 1024px) {
  body {
    /* Center mobile layout on desktop */
    max-width: 414px; /* iPhone Pro Max width */
    margin: 0 auto;
    box-shadow: 0 0 30px rgba(0, 0, 0, 0.15);
    min-height: 100vh;
    background: #f5f5f5;
  }
  
  /* Add mobile device frame effect */
  body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    z-index: -1;
  }
}

/* Preserve mobile touch interactions */
button, 
[role="button"],
.clickable {
  cursor: pointer;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0.1);
  tap-highlight-color: rgba(0, 0, 0, 0.1);
}

/* Mobile-like scrolling */
.scrollable {
  -webkit-overflow-scrolling: touch;
  overflow-scrolling: touch;
}

/* Preserve mobile form styling */
input, 
textarea, 
select {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  border-radius: 8px;
  font-size: 16px; /* Prevent zoom on iOS */
}

/* Mobile-first navigation */
.bottom-navigation {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 1000;
}

/* Ensure mobile-like status bar behavior */
.status-bar-safe-area {
  padding-top: env(safe-area-inset-top);
  padding-bottom: env(safe-area-inset-bottom);
  padding-left: env(safe-area-inset-left);
  padding-right: env(safe-area-inset-right);
}

/* Mobile-optimized animations */
@media (prefers-reduced-motion: no-preference) {
  * {
    transition-duration: 0.2s;
    transition-timing-function: cubic-bezier(0.4, 0.0, 0.2, 1);
  }
}

/* Respect user's motion preferences */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Dark mode support (mobile-first) */
@media (prefers-color-scheme: dark) {
  body {
    background-color: #121212;
    color: #ffffff;
  }
}

/* High contrast support */
@media (prefers-contrast: high) {
  * {
    border-width: 2px !important;
  }
}

/* Mobile-first print styles */
@media print {
  body {
    max-width: none !important;
    margin: 0 !important;
    box-shadow: none !important;
    background: white !important;
  }
}

/* Accessibility improvements while maintaining mobile layout */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Focus indicators for keyboard navigation */
*:focus {
  outline: 2px solid #22c55e;
  outline-offset: 2px;
}

/* Mobile-optimized loading states */
.loading {
  pointer-events: none;
  opacity: 0.6;
}

/* Preserve mobile gesture areas */
.gesture-area {
  touch-action: pan-x pan-y;
}

/* Mobile-first error states */
.error-boundary {
  padding: 20px;
  text-align: center;
  background: #fee2e2;
  border: 1px solid #fecaca;
  border-radius: 8px;
  margin: 16px;
}

/* Ensure mobile-like performance */
.performance-optimized {
  will-change: transform;
  transform: translateZ(0);
}

/* Mobile-first vendor dashboard adjustments */
.vendor-dashboard,
.partner-dashboard,
.affiliate-dashboard {
  /* Ensure these maintain mobile layout */
  width: 100%;
  max-width: none;
  margin: 0;
  padding: 16px;
}

/* Mobile-first table responsiveness */
.responsive-table {
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
}

.responsive-table table {
  min-width: 100%;
  white-space: nowrap;
}

/* Mobile-first modal behavior */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
}

/* Preserve mobile swipe gestures */
.swipeable {
  touch-action: pan-y;
}

/* Mobile-first card layouts */
.card {
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin: 8px 16px;
}

/* Ensure mobile-like spacing */
.mobile-spacing {
  padding: 16px;
  margin: 8px 0;
}

/* Mobile-first button sizing */
.mobile-button {
  min-height: 44px; /* iOS minimum touch target */
  min-width: 44px;
  padding: 12px 24px;
  border-radius: 8px;
}

/* Mobile-first typography */
.mobile-text {
  font-size: 16px;
  line-height: 1.5;
  letter-spacing: -0.01em;
}

/* Preserve mobile safe areas */
.safe-area-top {
  padding-top: max(20px, env(safe-area-inset-top));
}

.safe-area-bottom {
  padding-bottom: max(20px, env(safe-area-inset-bottom));
}

/* Mobile-first image optimization */
img {
  max-width: 100%;
  height: auto;
  display: block;
}

/* Ensure mobile-like video behavior */
video {
  width: 100%;
  height: auto;
  object-fit: cover;
}

/* Mobile-first form layouts */
.form-group {
  margin-bottom: 16px;
}

.form-input {
  width: 100%;
  padding: 12px 16px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 16px;
}

/* Mobile-first navigation spacing */
.nav-item {
  padding: 12px 16px;
  min-height: 44px;
  display: flex;
  align-items: center;
}

/* Preserve mobile pull-to-refresh */
.pull-to-refresh {
  overscroll-behavior-y: contain;
}
