@echo off
echo 🚀 Ultimate Memory Fix for Nature's Place App 🚀
echo.
echo This script will apply all memory optimizations and build the app
echo.

echo ========================================
echo Phase 1: System Cleanup
echo ========================================

echo Killing all Java processes...
taskkill /f /im java.exe 2>nul
taskkill /f /im javaw.exe 2>nul
echo ✅ Java processes terminated
echo.

echo Stopping Gradle daemons...
cd android
call gradlew --stop 2>nul
cd ..
echo ✅ Gradle daemons stopped
echo.

echo ========================================
echo Phase 2: Cache Cleanup
echo ========================================

echo Clearing all build caches...
flutter clean
if exist "build" rmdir /s /q "build"
if exist "android\app\build" rmdir /s /q "android\app\build"
if exist "android\.gradle" rmdir /s /q "android\.gradle"
if exist "%USERPROFILE%\.gradle\caches" rmdir /s /q "%USERPROFILE%\.gradle\caches"
if exist "%USERPROFILE%\.gradle\daemon" rmdir /s /q "%USERPROFILE%\.gradle\daemon"
echo ✅ All caches cleared
echo.

echo ========================================
echo Phase 3: Memory Configuration
echo ========================================

echo Enabling low-memory mode...
call enable_low_memory_mode.bat
echo.

echo Setting environment variables...
set GRADLE_OPTS=-Xmx512m -XX:MaxMetaspaceSize=256m -Dfile.encoding=UTF-8
set JAVA_OPTS=-Xmx512m
set FLUTTER_BUILD_MODE=debug
echo ✅ Memory limits configured
echo.

echo ========================================
echo Phase 4: Dependencies
echo ========================================

echo Getting Flutter dependencies...
flutter pub get
if %errorlevel% neq 0 (
    echo ❌ Failed to get dependencies
    pause
    exit /b 1
)
echo ✅ Dependencies updated
echo.

echo ========================================
echo Phase 5: Build Process
echo ========================================

echo Building with maximum memory optimization...
echo This may take longer but should avoid memory errors...
echo.

cd android
echo Attempting build with minimal resources...
call gradlew assembleDebug --no-daemon --max-workers=1 --no-parallel --no-build-cache

if %errorlevel% equ 0 (
    echo ✅ Build successful!
    cd ..
    goto success
) else (
    echo ❌ Build failed. Trying even more aggressive optimization...
    echo.
    
    echo Cleaning again...
    call gradlew clean --no-daemon
    
    echo Building with absolute minimal settings...
    call gradlew assembleDebug --no-daemon --max-workers=1 --no-parallel --no-build-cache --offline
    
    if %errorlevel% equ 0 (
        echo ✅ Build successful with aggressive optimization!
        cd ..
        goto success
    ) else (
        echo ❌ Build still failed
        cd ..
        goto failure
    )
)

:success
echo.
echo ========================================
echo 🎉 SUCCESS! 🎉
echo ========================================
echo.
echo Your Nature's Place app has been built successfully!
echo The APK is located at: android\app\build\outputs\flutter-apk\app-debug.apk
echo.
echo You can now run: flutter run
echo.
goto end

:failure
echo.
echo ========================================
echo ❌ BUILD FAILED ❌
echo ========================================
echo.
echo The build failed even with maximum memory optimization.
echo.
echo Possible solutions:
echo 1. Restart your computer to free up memory
echo 2. Close all other applications
echo 3. Try building with Android Studio instead
echo 4. Consider upgrading your system RAM
echo 5. Use a different computer with more memory
echo.
echo System requirements:
echo - Minimum 8GB RAM recommended
echo - At least 4GB free disk space
echo - Close browser and other memory-intensive apps
echo.

:end
pause
