import 'package:flutter/material.dart';
import '../services/commission_monitoring_service.dart';
import '../services/payout_scheduler_service.dart';

/// Partner dashboard showing real-time commission tracking and conversion rates
class PartnerDashboard extends StatefulWidget {
  final String partnerId;

  const PartnerDashboard({
    Key? key,
    required this.partnerId,
  }) : super(key: key);

  @override
  State<PartnerDashboard> createState() => _PartnerDashboardState();
}

class _PartnerDashboardState extends State<PartnerDashboard> {
  final CommissionMonitoringService _monitoringService = CommissionMonitoringService();
  final PayoutSchedulerService _payoutScheduler = PayoutSchedulerService();
  
  Map<String, dynamic> _performanceData = {};
  Map<String, dynamic> _payoutSchedule = {};
  double _pendingEarnings = 0.0;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadDashboardData();
  }

  Future<void> _loadDashboardData() async {
    setState(() => _isLoading = true);

    try {
      final performance = await _monitoringService.getPartnerPerformanceReport(widget.partnerId);
      final schedule = await _payoutScheduler.getPartnerPayoutSchedule(widget.partnerId);
      final pending = await _payoutScheduler.calculatePendingCommissions(widget.partnerId);

      setState(() {
        _performanceData = performance;
        _payoutSchedule = schedule ?? {};
        _pendingEarnings = pending;
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Failed to load dashboard: $e')),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Scaffold(
        body: Center(child: CircularProgressIndicator()),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('Partner Dashboard'),
        backgroundColor: Colors.green[700],
        foregroundColor: Colors.white,
      ),
      body: RefreshIndicator(
        onRefresh: _loadDashboardData,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildEarningsCard(),
              const SizedBox(height: 16),
              _buildPayoutScheduleCard(),
              const SizedBox(height: 16),
              _buildPerformanceCard(),
              const SizedBox(height: 16),
              _buildConversionTipsCard(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildEarningsCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.account_balance_wallet, color: Colors.green[700]),
                const SizedBox(width: 8),
                const Text(
                  'Current Earnings',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text('Pending Earnings', style: TextStyle(color: Colors.grey)),
                    Text(
                      '\$${_pendingEarnings.toStringAsFixed(2)}',
                      style: const TextStyle(fontSize: 24, fontWeight: FontWeight.bold, color: Colors.green),
                    ),
                  ],
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    const Text('Total Earned', style: TextStyle(color: Colors.grey)),
                    Text(
                      '\$${(_performanceData['total_earnings'] ?? 0.0).toStringAsFixed(2)}',
                      style: const TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
                    ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.blue[50],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.blue[200]!),
              ),
              child: Row(
                children: [
                  Icon(Icons.info, color: Colors.blue[700], size: 20),
                  const SizedBox(width: 8),
                  const Expanded(
                    child: Text(
                      'You only earn commission when your referrals complete purchases. Cancelled orders don\'t count.',
                      style: TextStyle(fontSize: 12),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPayoutScheduleCard() {
    final nextPayoutDate = _payoutSchedule['next_payout_date'] ?? 'Not scheduled';
    final daysUntilPayout = _calculateDaysUntilPayout(nextPayoutDate);

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.schedule, color: Colors.orange[700]),
                const SizedBox(width: 8),
                const Text(
                  'Payout Schedule',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text('Next Payout', style: TextStyle(color: Colors.grey)),
                    Text(
                      nextPayoutDate.toString(),
                      style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                    ),
                  ],
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    const Text('Days Remaining', style: TextStyle(color: Colors.grey)),
                    Text(
                      '$daysUntilPayout days',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: daysUntilPayout <= 3 ? Colors.green : Colors.orange,
                      ),
                    ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 12),
            LinearProgressIndicator(
              value: daysUntilPayout > 0 ? (30 - daysUntilPayout) / 30 : 1.0,
              backgroundColor: Colors.grey[300],
              valueColor: AlwaysStoppedAnimation<Color>(Colors.green[700]!),
            ),
            const SizedBox(height: 8),
            const Text(
              'Payouts are processed every 30 days via Stripe',
              style: TextStyle(fontSize: 12, color: Colors.grey),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPerformanceCard() {
    final conversionRate = (_performanceData['conversion_rate'] ?? 0.0) * 100;
    final totalReferrals = _performanceData['total_referrals'] ?? 0;
    final completedPurchases = _performanceData['completed_purchases'] ?? 0;
    final performanceGrade = _performanceData['performance_grade'] ?? 'N/A';

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.trending_up, color: Colors.purple[700]),
                const SizedBox(width: 8),
                const Text(
                  'Performance Metrics',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildMetricItem('Conversion Rate', '${conversionRate.toStringAsFixed(1)}%', _getConversionColor(conversionRate)),
                ),
                Expanded(
                  child: _buildMetricItem('Total Referrals', '$totalReferrals', Colors.blue),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: _buildMetricItem('Completed Purchases', '$completedPurchases', Colors.green),
                ),
                Expanded(
                  child: _buildMetricItem('Performance Grade', performanceGrade, _getGradeColor(performanceGrade)),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMetricItem(String label, String value, Color color) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(label, style: const TextStyle(color: Colors.grey, fontSize: 12)),
        Text(
          value,
          style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold, color: color),
        ),
      ],
    );
  }

  Widget _buildConversionTipsCard() {
    final conversionRate = (_performanceData['conversion_rate'] ?? 0.0) * 100;
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.lightbulb, color: Colors.amber[700]),
                const SizedBox(width: 8),
                const Text(
                  'Conversion Tips',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            if (conversionRate < 5) ...[
              _buildTip('🎯', 'Target health-conscious audiences who are likely to purchase'),
              _buildTip('📱', 'Share product links on social media with personal testimonials'),
              _buildTip('💡', 'Focus on high-quality, research-backed products'),
            ] else if (conversionRate < 10) ...[
              _buildTip('🚀', 'Great progress! Try promoting seasonal health products'),
              _buildTip('📊', 'Analyze which products convert best and focus on similar items'),
              _buildTip('🤝', 'Build trust with your audience through educational content'),
            ] else ...[
              _buildTip('🏆', 'Excellent conversion rate! You\'re a top performer'),
              _buildTip('📈', 'Consider expanding to new product categories'),
              _buildTip('👥', 'Share your success strategies with other partners'),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildTip(String emoji, String tip) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Text(emoji, style: const TextStyle(fontSize: 16)),
          const SizedBox(width: 8),
          Expanded(child: Text(tip, style: const TextStyle(fontSize: 14))),
        ],
      ),
    );
  }

  Color _getConversionColor(double rate) {
    if (rate >= 10) return Colors.green;
    if (rate >= 5) return Colors.orange;
    return Colors.red;
  }

  Color _getGradeColor(String grade) {
    switch (grade) {
      case 'A': return Colors.green;
      case 'B': return Colors.blue;
      case 'C': return Colors.orange;
      case 'D': return Colors.red;
      default: return Colors.grey;
    }
  }

  int _calculateDaysUntilPayout(dynamic nextPayoutDate) {
    if (nextPayoutDate == null || nextPayoutDate == 'Not scheduled') return 30;
    
    try {
      final payoutDate = DateTime.parse(nextPayoutDate.toString());
      final now = DateTime.now();
      final difference = payoutDate.difference(now).inDays;
      return difference > 0 ? difference : 0;
    } catch (e) {
      return 30;
    }
  }
}
