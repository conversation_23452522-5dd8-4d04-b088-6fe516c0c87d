import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import '../services/affiliate_marketplace_service.dart';
import '../services/in_app_purchase_service.dart';
import '../widgets/enhanced_product_card.dart';
import 'marketing_opportunities_screen.dart';
import 'product_upload_screen.dart';
import 'vendor_notifications_screen.dart';
import 'admin_control_panel.dart';
import 'subscription_screen.dart';

class BrandDashboardScreen extends StatefulWidget {
  const BrandDashboardScreen({super.key});

  @override
  State<BrandDashboardScreen> createState() => _BrandDashboardScreenState();
}

class _BrandDashboardScreenState extends State<BrandDashboardScreen> with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final _affiliateService = PartnerMarketplaceService();

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 5, vsync: this);
    _affiliateService.initialize();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Brand Dashboard'),
        backgroundColor: const Color(0xFF22c55e),
        foregroundColor: Colors.white,
        actions: [
          // Notifications Button
          Consumer<PartnerMarketplaceService>(
            builder: (context, service, child) {
              // Get first partner for demo (in real app, use current user's vendor ID)
              final firstPartner = service.partners.isNotEmpty ? service.partners.first : null;
              final unreadCount = firstPartner != null
                ? service.getUnreadNotificationCount(firstPartner.id)
                : 0;

              return Directionality(
                textDirection: TextDirection.ltr,
                child: Stack(
                  children: [
                  IconButton(
                    onPressed: firstPartner != null
                      ? () => Navigator.of(context).push(
                          MaterialPageRoute(
                            builder: (context) => ChangeNotifierProvider.value(
                              value: service,
                              child: VendorNotificationsScreen(vendorId: firstPartner.id),
                            ),
                          ),
                        )
                      : null,
                    icon: const Icon(Icons.notifications),
                  ),
                  if (unreadCount > 0)
                    Positioned(
                      right: 8,
                      top: 8,
                      child: Container(
                        padding: const EdgeInsets.all(2),
                        decoration: BoxDecoration(
                          color: Colors.red,
                          borderRadius: BorderRadius.circular(10),
                        ),
                        constraints: const BoxConstraints(
                          minWidth: 16,
                          minHeight: 16,
                        ),
                        child: Text(
                          unreadCount > 99 ? '99+' : unreadCount.toString(),
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 10,
                            fontWeight: FontWeight.bold,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ),
                ],
                ),
              );
            },
          ),

          // Admin Panel Button (hidden button - long press to access)
          GestureDetector(
            onLongPress: () => Navigator.of(context).push(
              MaterialPageRoute(
                builder: (context) => ChangeNotifierProvider.value(
                  value: _affiliateService,
                  child: const AdminControlPanel(),
                ),
              ),
            ),
            child: const SizedBox(width: 48, height: 48), // Invisible button
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          isScrollable: true,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          indicatorColor: Colors.white,
          tabs: const [
            Tab(icon: FaIcon(FontAwesomeIcons.chartLine, size: 20), text: 'Analytics'),
            Tab(icon: FaIcon(FontAwesomeIcons.handshake, size: 20), text: 'Partners'),
            Tab(icon: FaIcon(FontAwesomeIcons.box, size: 20), text: 'Products'),
            Tab(icon: FaIcon(FontAwesomeIcons.dollarSign, size: 20), text: 'Sales'),
            Tab(icon: FaIcon(FontAwesomeIcons.bullhorn, size: 20), text: 'Marketing'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildAnalyticsTab(),
          _buildPartnersTab(),
          _buildProductsTab(),
          _buildSalesTab(),
          _buildMarketingTab(),
        ],
      ),
    );
  }

  Widget _buildAnalyticsTab() {
    return ChangeNotifierProvider.value(
      value: _affiliateService,
      child: Consumer<PartnerMarketplaceService>(
        builder: (context, service, child) {
          final analytics = service.getBrandAnalytics();
          
          return SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Subscription Status Card
                Consumer<InAppPurchaseService>(
                  builder: (context, purchaseService, child) {
                    return _buildSubscriptionStatusCard(context, purchaseService);
                  },
                ),
                const SizedBox(height: 24),

                // Key metrics cards
                Row(
                  children: [
                    Expanded(
                      child: _buildMetricCard(
                        'Total Revenue',
                        '\$${analytics.totalRevenue.toStringAsFixed(2)}',
                        Icons.attach_money,
                        Colors.green,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: _buildMetricCard(
                        'App Earnings',
                        '\$${analytics.totalCommissions.toStringAsFixed(2)}',
                        Icons.trending_up,
                        Colors.blue,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: _buildMetricCard(
                        'Total Sales',
                        analytics.totalSales.toString(),
                        Icons.shopping_cart,
                        Colors.orange,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: _buildMetricCard(
                        'Conversion Rate',
                        '${(analytics.conversionRate * 100).toStringAsFixed(1)}%',
                        Icons.percent,
                        Colors.purple,
                      ),
                    ),
                  ],
                ),
                
                const SizedBox(height: 24),
                
                // Revenue by network
                Text(
                  'Revenue by Network',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 16),
                Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      children: analytics.revenueByNetwork.entries.map((entry) =>
                        Padding(
                          padding: const EdgeInsets.symmetric(vertical: 8),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(entry.key),
                              Text(
                                '\$${entry.value.toStringAsFixed(2)}',
                                style: const TextStyle(
                                  fontWeight: FontWeight.bold,
                                  color: Color(0xFF22c55e),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ).toList(),
                    ),
                  ),
                ),
                
                const SizedBox(height: 24),
                
                // Sales by category
                Text(
                  'Sales by Category',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 16),
                Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      children: analytics.salesByCategory.entries.map((entry) =>
                        Padding(
                          padding: const EdgeInsets.symmetric(vertical: 8),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(entry.key),
                              Text(
                                '${entry.value} sales',
                                style: const TextStyle(fontWeight: FontWeight.bold),
                              ),
                            ],
                          ),
                        ),
                      ).toList(),
                    ),
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildPartnersTab() {
    return ChangeNotifierProvider.value(
      value: _affiliateService,
      child: Consumer<PartnerMarketplaceService>(
        builder: (context, service, child) {
          final partners = service.getTopPartners();
          
          return ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: partners.length,
            itemBuilder: (context, index) {
              final partner = partners[index];
              return Card(
                margin: const EdgeInsets.only(bottom: 16),
                child: ListTile(
                  leading: CircleAvatar(
                    backgroundImage: NetworkImage(partner.logoUrl),
                  ),
                  title: Text(partner.name),
                  subtitle: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text('${partner.totalProducts} products'),
                      Text('${(partner.commissionRate * 100).toInt()}% app commission rate'),
                    ],
                  ),
                  trailing: Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Text(
                        '\$${partner.totalEarnings.toStringAsFixed(2)}',
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          color: Colors.green,
                        ),
                      ),
                      Text('${partner.totalSales} sales'),
                    ],
                  ),
                  onTap: () {
                    _showPartnerDetails(context, partner);
                  },
                ),
              );
            },
          );
        },
      ),
    );
  }

  Widget _buildProductsTab() {
    return ChangeNotifierProvider.value(
      value: _affiliateService,
      child: Consumer<PartnerMarketplaceService>(
        builder: (context, service, child) {
          final products = service.products;
          final partnersWithStatus = service.getPartnersWithUploadStatus();

          return SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Upload Quota Overview
                Text(
                  'Upload Quota Overview',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 16),

                // Partner Upload Status Cards
                ...partnersWithStatus.map((partnerData) {
                  final partner = partnerData['partner'] as Partner;
                  final quota = partnerData['uploadQuota'] as Map<String, dynamic>;

                  return Card(
                    margin: const EdgeInsets.only(bottom: 16),
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              CircleAvatar(
                                backgroundImage: NetworkImage(partner.logoUrl),
                                radius: 20,
                              ),
                              const SizedBox(width: 12),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      partner.name,
                                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                    Text(
                                      '${quota['tier']} Tier',
                                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                        color: _getTierColor(partner.tier),
                                        fontWeight: FontWeight.w600,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              Container(
                                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                                decoration: BoxDecoration(
                                  color: _getQuotaStatusColor(quota['uploadProgressPercentage']).withValues(alpha: 0.1),
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                child: Text(
                                  '${quota['uploadedCount']}/${quota['uploadLimit']}',
                                  style: TextStyle(
                                    color: _getQuotaStatusColor(quota['uploadProgressPercentage']),
                                    fontWeight: FontWeight.bold,
                                    fontSize: 12,
                                  ),
                                ),
                              ),
                            ],
                          ),

                          const SizedBox(height: 12),
                          // Progress bar (now shown for all tiers)
                          LinearProgressIndicator(
                            value: quota['uploadProgressPercentage'] / 100,
                            backgroundColor: Colors.grey[300],
                            valueColor: AlwaysStoppedAnimation<Color>(
                              _getQuotaStatusColor(quota['uploadProgressPercentage']),
                            ),
                          ),
                          const SizedBox(height: 8),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                '${quota['remainingUploads']} uploads remaining',
                                style: Theme.of(context).textTheme.bodySmall,
                              ),
                              Text(
                                '${quota['uploadProgressPercentage'].toStringAsFixed(1)}% used',
                                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                  color: _getQuotaStatusColor(quota['uploadProgressPercentage']),
                                ),
                              ),
                            ],
                          ),

                          // Upgrade suggestion if near limit
                          if (!quota['canUploadMore'] || quota['uploadProgressPercentage'] > 80) ...[
                            const SizedBox(height: 12),
                            Container(
                              padding: const EdgeInsets.all(12),
                              decoration: BoxDecoration(
                                color: Colors.orange.withValues(alpha: 0.1),
                                borderRadius: BorderRadius.circular(8),
                                border: Border.all(color: Colors.orange.withValues(alpha: 0.3)),
                              ),
                              child: Row(
                                children: [
                                  const Icon(Icons.warning_amber, color: Colors.orange, size: 20),
                                  const SizedBox(width: 8),
                                  Expanded(
                                    child: Text(
                                      quota['canUploadMore']
                                        ? 'Approaching upload limit. Consider upgrading for more capacity.'
                                        : 'Upload limit reached. Upgrade to continue uploading products.',
                                      style: const TextStyle(
                                        color: Colors.orange,
                                        fontSize: 12,
                                        fontWeight: FontWeight.w500,
                                      ),
                                    ),
                                  ),
                                  TextButton(
                                    onPressed: () => _showUpgradeDialog(context, partner),
                                    style: TextButton.styleFrom(
                                      foregroundColor: Colors.orange,
                                      padding: const EdgeInsets.symmetric(horizontal: 8),
                                    ),
                                    child: const Text('Upgrade', style: TextStyle(fontSize: 12)),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ],
                      ),
                    ),
                  );
                }),

                const SizedBox(height: 24),

                // Products Grid Header with Upload Button
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Your Products',
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    ElevatedButton.icon(
                      onPressed: () {
                        // Navigate to upload screen with first partner's ID (demo)
                        final firstPartner = partnersWithStatus.isNotEmpty
                          ? partnersWithStatus.first['partner'] as Partner
                          : null;

                        if (firstPartner != null) {
                          Navigator.of(context).push(
                            MaterialPageRoute(
                              builder: (context) => ChangeNotifierProvider.value(
                                value: service,
                                child: ProductUploadScreen(partnerId: firstPartner.id),
                              ),
                            ),
                          );
                        }
                      },
                      icon: const Icon(Icons.add, size: 18),
                      label: const Text('Upload Product'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: const Color(0xFF22c55e),
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),

                GridView.builder(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: 2,
                    childAspectRatio: 0.65,
                    crossAxisSpacing: 16,
                    mainAxisSpacing: 16,
                  ),
                  itemCount: products.length,
                  itemBuilder: (context, index) {
                    final product = products[index];
                    return ChangeNotifierProvider.value(
                      value: service,
                      child: EnhancedProductCard(product: product),
                    );
                  },
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildSalesTab() {
    return ChangeNotifierProvider.value(
      value: _affiliateService,
      child: Consumer<PartnerMarketplaceService>(
        builder: (context, service, child) {
          final sales = service.getRecentSales();
          
          return ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: sales.length,
            itemBuilder: (context, index) {
              final sale = sales[index];
              return Card(
                margin: const EdgeInsets.only(bottom: 8),
                child: ListTile(
                  leading: CircleAvatar(
                    backgroundColor: _getSaleStatusColor(sale.status),
                    child: Icon(
                      _getSaleStatusIcon(sale.status),
                      color: Colors.white,
                      size: 20,
                    ),
                  ),
                  title: Text(sale.productName),
                  subtitle: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text('via ${sale.affiliateNetwork}'),
                      Text(_formatDate(sale.saleDate)),
                    ],
                  ),
                  trailing: Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Text(
                        '\$${sale.commissionAmount.toStringAsFixed(2)}',
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          color: Colors.green,
                        ),
                      ),
                      Text(
                        _formatSaleStatus(sale.status),
                        style: TextStyle(
                          color: _getSaleStatusColor(sale.status),
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ),
                ),
              );
            },
          );
        },
      ),
    );
  }

  Widget _buildMetricCard(String title, String value, IconData icon, Color color) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Icon(icon, color: color, size: 32),
            const SizedBox(height: 8),
            Text(
              value,
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            Text(
              title,
              style: Theme.of(context).textTheme.bodySmall,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  void _showPartnerDetails(BuildContext context, Partner partner) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(partner.name),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(partner.description),
            const SizedBox(height: 16),
            Text('App Commission Rate: ${(partner.commissionRate * 100).toInt()}%'),
            Text('Total Products: ${partner.totalProducts}'),
            Text('Total Earnings: \$${partner.totalEarnings.toStringAsFixed(2)}'),
            Text('Total Sales: ${partner.totalSales}'),
            Text('Joined: ${_formatDate(partner.joinedDate)}'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  String _formatSaleStatus(String status) {
    switch (status) {
      case 'pending':
        return 'Pending';
      case 'confirmed':
        return 'Confirmed';
      case 'paid':
        return 'Paid';
      default:
        return status;
    }
  }

  Color _getSaleStatusColor(String status) {
    switch (status) {
      case 'pending':
        return Colors.orange;
      case 'confirmed':
        return Colors.blue;
      case 'paid':
        return Colors.green;
      default:
        return Colors.grey;
    }
  }

  IconData _getSaleStatusIcon(String status) {
    switch (status) {
      case 'pending':
        return Icons.hourglass_empty;
      case 'confirmed':
        return Icons.check_circle;
      case 'paid':
        return Icons.payment;
      default:
        return Icons.help;
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  Widget _buildMarketingTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Marketing Overview Card
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      const FaIcon(
                        FontAwesomeIcons.bullhorn,
                        color: Color(0xFF22c55e),
                        size: 24,
                      ),
                      const SizedBox(width: 12),
                      Text(
                        'Marketing Opportunities',
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Boost your affiliate sales with our comprehensive marketing tools and promotional opportunities.',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Colors.grey[600],
                    ),
                  ),
                  const SizedBox(height: 16),
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: () {
                        Navigator.of(context).push(
                          MaterialPageRoute(
                            builder: (context) => const MarketingOpportunitiesScreen(),
                          ),
                        );
                      },
                      child: const Text('Explore Marketing Opportunities'),
                    ),
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(height: 24),

          // Quick Stats
          Text(
            'Marketing Performance',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),

          Row(
            children: [
              Expanded(
                child: _buildMetricCard(
                  'Active Campaigns',
                  '3',
                  Icons.campaign,
                  Colors.blue,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildMetricCard(
                  'Marketing ROI',
                  '340%',
                  Icons.trending_up,
                  Colors.green,
                ),
              ),
            ],
          ),

          const SizedBox(height: 24),

          // Fee Structure Overview
          Text(
            'Subscription & Fees',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),

          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  _buildFeeRow('Monthly Subscription', '\$299.99', Colors.blue),
                  const Divider(),
                  _buildFeeRow('Platform Fee Rate', '10%', Colors.orange),
                  const Divider(),
                  _buildFeeRow('Marketing Spend', '\$649.97', Colors.purple),
                  const Divider(),
                  _buildFeeRow('Total Monthly Cost', '\$949.96', Colors.red),
                ],
              ),
            ),
          ),

          const SizedBox(height: 24),

          // Available Tools
          Text(
            'Available Marketing Tools',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),

          _buildToolCard(
            'Featured Product Placement',
            'Get your products featured on category pages',
            Icons.star,
            Colors.orange,
            '\$199.99/month',
          ),
          const SizedBox(height: 12),

          _buildToolCard(
            'Email Marketing Campaign',
            'Feature in newsletter to 50K+ subscribers',
            Icons.email,
            Colors.blue,
            '\$299.99/campaign',
          ),
          const SizedBox(height: 12),

          _buildToolCard(
            'Social Media Boost',
            'Promote across our social media channels',
            Icons.share,
            Colors.pink,
            '\$149.99/2 weeks',
          ),
        ],
      ),
    );
  }

  Widget _buildFeeRow(String label, String amount, Color color) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: Theme.of(context).textTheme.titleMedium,
          ),
          Text(
            amount,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: color,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildToolCard(String title, String description, IconData icon, Color color, String price) {
    return Card(
      child: ListTile(
        leading: Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: color.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(icon, color: color),
        ),
        title: Text(title),
        subtitle: Text(description),
        trailing: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            Text(
              price,
              style: TextStyle(
                color: color,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
        onTap: () {
          Navigator.of(context).push(
            MaterialPageRoute(
              builder: (context) => const MarketingOpportunitiesScreen(),
            ),
          );
        },
      ),
    );
  }

  // Helper methods for upload quota management

  Color _getTierColor(PartnerTier tier) {
    switch (tier) {
      case PartnerTier.basic:
        return Colors.grey[600]!;
      case PartnerTier.premium:
        return Colors.blue;
      case PartnerTier.enterprise:
        return Colors.purple;
    }
  }

  Color _getQuotaStatusColor(double percentage) {
    if (percentage >= 90) return Colors.red;
    if (percentage >= 80) return Colors.orange;
    if (percentage >= 60) return Colors.yellow[700]!;
    return Colors.green;
  }

  void _showUpgradeDialog(BuildContext context, Partner partner) {
    final upgradeInfo = _affiliateService.getTierUpgradeInfo(partner.tier);

    if (upgradeInfo['nextTier'] == null) {
      // Already at highest tier
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('Maximum Tier Reached'),
          content: const Text('You are already on the Enterprise tier with up to 500 product uploads!'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('OK'),
            ),
          ],
        ),
      );
      return;
    }

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Upgrade to ${upgradeInfo['nextTier']}'),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Unlock more features and increase your upload capacity!',
                style: Theme.of(context).textTheme.bodyMedium,
              ),
              const SizedBox(height: 16),

              // Current vs Next tier comparison
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          'Current: ${partner.tierDisplayName}',
                          style: const TextStyle(fontWeight: FontWeight.bold),
                        ),
                        Text(
                          partner.hasUnlimitedUploads
                            ? 'Unlimited uploads'
                            : '${partner.uploadLimit} uploads',
                          style: TextStyle(color: Colors.grey[600]),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          'Upgrade to: ${upgradeInfo['nextTier']}',
                          style: const TextStyle(
                            fontWeight: FontWeight.bold,
                            color: Colors.green,
                          ),
                        ),
                        Text(
                          '${upgradeInfo['nextTierUploadLimit']} uploads',
                          style: const TextStyle(color: Colors.green),
                        ),
                      ],
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 16),

              Text(
                'New Features:',
                style: Theme.of(context).textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),

              ...((upgradeInfo['nextTierFeatures'] as List<String>).map((feature) =>
                Padding(
                  padding: const EdgeInsets.symmetric(vertical: 2),
                  child: Row(
                    children: [
                      const Icon(Icons.check, color: Colors.green, size: 16),
                      const SizedBox(width: 8),
                      Expanded(child: Text(feature, style: const TextStyle(fontSize: 13))),
                    ],
                  ),
                ),
              )),

              const SizedBox(height: 16),

              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.green.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.green.withValues(alpha: 0.3)),
                ),
                child: Row(
                  children: [
                    const Icon(Icons.attach_money, color: Colors.green, size: 20),
                    const SizedBox(width: 8),
                    Text(
                      'Monthly Cost: \$${upgradeInfo['nextTierPrice']}',
                      style: const TextStyle(
                        color: Colors.green,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _showUpgradeSuccess(context, upgradeInfo['nextTier']);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.green,
              foregroundColor: Colors.white,
            ),
            child: Text('Upgrade to ${upgradeInfo['nextTier']}'),
          ),
        ],
      ),
    );
  }

  void _showUpgradeSuccess(BuildContext context, String newTier) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            const Icon(Icons.check_circle, color: Colors.green),
            const SizedBox(width: 8),
            const Text('Upgrade Successful!'),
          ],
        ),
        content: Text('You have successfully upgraded to $newTier tier. Your new upload limits are now active!'),
        actions: [
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.green,
              foregroundColor: Colors.white,
            ),
            child: const Text('Great!'),
          ),
        ],
      ),
    );
  }

  Widget _buildSubscriptionStatusCard(BuildContext context, InAppPurchaseService purchaseService) {
    final currentTier = purchaseService.getCurrentSubscriptionTier();
    final hasSubscription = purchaseService.hasActiveSubscription();

    return Card(
      color: hasSubscription ? Colors.green[50] : Colors.orange[50],
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  hasSubscription ? Icons.workspace_premium : Icons.warning_amber,
                  color: hasSubscription ? Colors.green : Colors.orange,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        hasSubscription
                          ? 'Current Plan: ${currentTier?.name ?? 'Unknown'}'
                          : 'No Active Subscription',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: hasSubscription ? Colors.green[700] : Colors.orange[700],
                          fontSize: 16,
                        ),
                      ),
                      Text(
                        hasSubscription
                          ? 'Your subscription is active and ready to use!'
                          : 'Subscribe to start uploading products (app earns commissions)',
                        style: TextStyle(
                          color: hasSubscription ? Colors.green[600] : Colors.orange[600],
                        ),
                      ),
                    ],
                  ),
                ),
                ElevatedButton(
                  onPressed: () {
                    Navigator.of(context).push(
                      MaterialPageRoute(
                        builder: (context) => ChangeNotifierProvider.value(
                          value: purchaseService,
                          child: const SubscriptionScreen(),
                        ),
                      ),
                    );
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: hasSubscription ? Colors.blue : const Color(0xFF22c55e),
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  ),
                  child: Text(hasSubscription ? 'Manage' : 'Subscribe'),
                ),
              ],
            ),

            if (hasSubscription && currentTier != null) ...[
              const SizedBox(height: 12),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.green[200]!),
                ),
                child: Row(
                  children: [
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Product Limit',
                            style: TextStyle(
                              color: Colors.grey[600],
                              fontSize: 12,
                            ),
                          ),
                          Text(
                            '${currentTier.productLimit} products',
                            style: const TextStyle(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                    ),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Platform Fee',
                            style: TextStyle(
                              color: Colors.grey[600],
                              fontSize: 12,
                            ),
                          ),
                          Text(
                            '${(currentTier.platformFeeRate * 100).toInt()}%',
                            style: const TextStyle(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                    ),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Monthly Cost',
                            style: TextStyle(
                              color: Colors.grey[600],
                              fontSize: 12,
                            ),
                          ),
                          Text(
                            '\$${currentTier.price.toStringAsFixed(2)}',
                            style: const TextStyle(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}
