import 'package:flutter/material.dart';

/// Utility class for handling text overflow and layout issues
class TextUtils {
  
  /// Creates a safe text widget that handles overflow properly
  static Widget safeText(
    String text, {
    TextStyle? style,
    TextAlign? textAlign,
    int? maxLines,
    TextOverflow? overflow,
    bool softWrap = true,
  }) {
    return Text(
      text,
      style: style,
      textAlign: textAlign,
      maxLines: maxLines,
      overflow: overflow ?? TextOverflow.ellipsis,
      softWrap: softWrap,
    );
  }

  /// Creates a safe text widget for long content that can wrap
  static Widget safeContentText(
    String text, {
    TextStyle? style,
    TextAlign? textAlign,
  }) {
    return Text(
      text,
      style: style,
      textAlign: textAlign,
      overflow: TextOverflow.visible,
      softWrap: true,
    );
  }

  /// Creates a safe text widget for titles that should ellipsize
  static Widget safeTitleText(
    String text, {
    TextStyle? style,
    TextAlign? textAlign,
    int maxLines = 2,
  }) {
    return Text(
      text,
      style: style,
      textAlign: textAlign,
      maxLines: maxLines,
      overflow: TextOverflow.ellipsis,
      softWrap: true,
    );
  }

  /// Creates a safe text widget for single line content
  static Widget safeSingleLineText(
    String text, {
    TextStyle? style,
    TextAlign? textAlign,
  }) {
    return Text(
      text,
      style: style,
      textAlign: textAlign,
      maxLines: 1,
      overflow: TextOverflow.ellipsis,
      softWrap: false,
    );
  }

  /// Creates a flexible text widget that adapts to available space
  static Widget flexibleText(
    String text, {
    TextStyle? style,
    TextAlign? textAlign,
    int? maxLines,
    int flex = 1,
  }) {
    return Flexible(
      flex: flex,
      child: Text(
        text,
        style: style,
        textAlign: textAlign,
        maxLines: maxLines,
        overflow: TextOverflow.ellipsis,
        softWrap: true,
      ),
    );
  }

  /// Creates an expanded text widget for use in rows/columns
  static Widget expandedText(
    String text, {
    TextStyle? style,
    TextAlign? textAlign,
    int? maxLines,
    int flex = 1,
  }) {
    return Expanded(
      flex: flex,
      child: Text(
        text,
        style: style,
        textAlign: textAlign,
        maxLines: maxLines,
        overflow: TextOverflow.ellipsis,
        softWrap: true,
      ),
    );
  }

  /// Creates a constrained text widget with specific width
  static Widget constrainedText(
    String text, {
    required double maxWidth,
    TextStyle? style,
    TextAlign? textAlign,
    int? maxLines,
  }) {
    return ConstrainedBox(
      constraints: BoxConstraints(maxWidth: maxWidth),
      child: Text(
        text,
        style: style,
        textAlign: textAlign,
        maxLines: maxLines,
        overflow: TextOverflow.ellipsis,
        softWrap: true,
      ),
    );
  }

  /// Creates a safe row with text that handles overflow
  static Widget safeTextRow({
    required List<Widget> children,
    MainAxisAlignment mainAxisAlignment = MainAxisAlignment.start,
    CrossAxisAlignment crossAxisAlignment = CrossAxisAlignment.center,
    MainAxisSize mainAxisSize = MainAxisSize.max,
  }) {
    return Row(
      mainAxisAlignment: mainAxisAlignment,
      crossAxisAlignment: crossAxisAlignment,
      mainAxisSize: mainAxisSize,
      children: children,
    );
  }

  /// Creates a safe column with text that handles overflow
  static Widget safeTextColumn({
    required List<Widget> children,
    MainAxisAlignment mainAxisAlignment = MainAxisAlignment.start,
    CrossAxisAlignment crossAxisAlignment = CrossAxisAlignment.start,
    MainAxisSize mainAxisSize = MainAxisSize.max,
  }) {
    return Column(
      mainAxisAlignment: mainAxisAlignment,
      crossAxisAlignment: crossAxisAlignment,
      mainAxisSize: mainAxisSize,
      children: children,
    );
  }

  /// Creates a safe list tile with proper text overflow handling
  static Widget safeListTile({
    Widget? leading,
    required String title,
    String? subtitle,
    Widget? trailing,
    VoidCallback? onTap,
    TextStyle? titleStyle,
    TextStyle? subtitleStyle,
  }) {
    return ListTile(
      leading: leading,
      title: safeTitleText(title, style: titleStyle),
      subtitle: subtitle != null ? safeText(subtitle, style: subtitleStyle) : null,
      trailing: trailing,
      onTap: onTap,
    );
  }

  /// Creates a safe card with text content
  static Widget safeTextCard({
    required String title,
    String? content,
    Widget? child,
    EdgeInsetsGeometry? padding,
    VoidCallback? onTap,
  }) {
    return Card(
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: padding ?? const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              safeTitleText(
                title,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              if (content != null) ...[
                const SizedBox(height: 8),
                safeContentText(content),
              ],
              if (child != null) ...[
                const SizedBox(height: 8),
                child,
              ],
            ],
          ),
        ),
      ),
    );
  }

  /// Creates a safe text field with proper constraints
  static Widget safeTextField({
    required TextEditingController controller,
    required String labelText,
    String? hintText,
    Widget? prefixIcon,
    Widget? suffixIcon,
    int? maxLines,
    int? maxLength,
    TextInputType? keyboardType,
    bool obscureText = false,
    String? Function(String?)? validator,
    void Function(String)? onChanged,
  }) {
    return TextField(
      controller: controller,
      decoration: InputDecoration(
        labelText: labelText,
        hintText: hintText,
        prefixIcon: prefixIcon,
        suffixIcon: suffixIcon,
        border: const OutlineInputBorder(),
        counterText: maxLength != null ? null : '',
      ),
      maxLines: maxLines ?? 1,
      maxLength: maxLength,
      keyboardType: keyboardType,
      obscureText: obscureText,
      onChanged: onChanged,
      textCapitalization: TextCapitalization.sentences,
    );
  }

  /// Truncates text to a specific length with ellipsis
  static String truncateText(String text, int maxLength) {
    if (text.length <= maxLength) return text;
    return '${text.substring(0, maxLength)}...';
  }

  /// Formats text for display with proper line breaks
  static String formatDisplayText(String text) {
    return text
        .replaceAll(RegExp(r'\s+'), ' ') // Replace multiple spaces with single space
        .trim(); // Remove leading/trailing whitespace
  }

  /// Checks if text will overflow in given constraints
  static bool willTextOverflow(
    String text,
    TextStyle style,
    double maxWidth, {
    int? maxLines,
  }) {
    final textPainter = TextPainter(
      text: TextSpan(text: text, style: style),
      maxLines: maxLines,
      textDirection: TextDirection.ltr,
    );
    textPainter.layout(maxWidth: maxWidth);
    return textPainter.didExceedMaxLines;
  }

  /// Gets the optimal font size for text to fit in constraints
  static double getOptimalFontSize(
    String text,
    double maxWidth,
    double maxHeight, {
    double minFontSize = 8.0,
    double maxFontSize = 24.0,
    int? maxLines,
  }) {
    double fontSize = maxFontSize;
    
    while (fontSize >= minFontSize) {
      final textPainter = TextPainter(
        text: TextSpan(
          text: text,
          style: TextStyle(fontSize: fontSize),
        ),
        maxLines: maxLines,
        textDirection: TextDirection.ltr,
      );
      textPainter.layout(maxWidth: maxWidth);
      
      if (textPainter.size.height <= maxHeight && !textPainter.didExceedMaxLines) {
        return fontSize;
      }
      
      fontSize -= 1.0;
    }
    
    return minFontSize;
  }
}
