import 'package:flutter/material.dart';
import '../providers/app_state.dart';
import '../features/visual_enhancements.dart';
import 'enhanced_learn_more_button.dart';

class EnhancedPlantCard extends StatelessWidget {
  final Plant plant;
  final VoidCallback? onTap;
  final bool showDetailedInfo;
  
  const EnhancedPlantCard({
    super.key,
    required this.plant,
    this.onTap,
    this.showDetailedInfo = false,
  });

  @override
  Widget build(BuildContext context) {
    final colorScheme = VisualEnhancementService.getPlantColorScheme(plant);
    final rarityBadge = VisualEnhancementService.getRarityBadge(plant);
    final plantIcon = VisualEnhancementService.getPlantIcon(plant);
    
    return Card(
      elevation: 6,
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(16),
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16),
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                colorScheme.background,
                colorScheme.background.withValues(alpha: 0.8),
              ],
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header with image and basic info
              _buildHeader(colorScheme, rarityBadge, plantIcon),
              
              // Plant details
              Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Name and scientific name
                    _buildNameSection(colorScheme),
                    
                    const SizedBox(height: 8),
                    
                    // Origin and category
                    _buildOriginCategory(colorScheme),
                    
                    const SizedBox(height: 12),
                    
                    // Benefits chips
                    _buildBenefitsSection(),
                    
                    if (showDetailedInfo) ...[
                      const SizedBox(height: 12),
                      _buildDetailedInfo(colorScheme),
                    ],
                    
                    const SizedBox(height: 12),
                    
                    // Rating and reviews
                    _buildRatingSection(colorScheme),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader(PlantColorScheme colorScheme, RarityBadge rarityBadge, IconData plantIcon) {
    return Container(
      height: 200,
      decoration: BoxDecoration(
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(16),
          topRight: Radius.circular(16),
        ),
        image: DecorationImage(
          image: NetworkImage(plant.imageUrl),
          fit: BoxFit.cover,
        ),
      ),
      child: Stack(
        children: [
          // Gradient overlay
          Container(
            decoration: BoxDecoration(
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(16),
                topRight: Radius.circular(16),
              ),
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  Colors.transparent,
                  Colors.black.withValues(alpha: 0.3),
                ],
              ),
            ),
          ),
          
          // Rarity badge
          Positioned(
            top: 12,
            right: 12,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: rarityBadge.color,
                borderRadius: BorderRadius.circular(12),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    rarityBadge.icon,
                    size: 14,
                    color: Colors.white,
                  ),
                  const SizedBox(width: 3),
                  Flexible(
                    child: Text(
                      rarityBadge.label,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 10,
                        fontWeight: FontWeight.bold,
                      ),
                      overflow: TextOverflow.ellipsis,
                      maxLines: 1,
                    ),
                  ),
                ],
              ),
            ),
          ),
          
          // Plant category icon
          Positioned(
            top: 12,
            left: 12,
            child: Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: colorScheme.primary.withValues(alpha: 0.9),
                borderRadius: BorderRadius.circular(20),
              ),
              child: Icon(
                plantIcon,
                color: Colors.white,
                size: 24,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNameSection(PlantColorScheme colorScheme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          plant.name,
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: colorScheme.text,
          ),
        ),
        Text(
          plant.scientificName,
          style: TextStyle(
            fontSize: 14,
            fontStyle: FontStyle.italic,
            color: colorScheme.textSecondary,
          ),
        ),
      ],
    );
  }

  Widget _buildOriginCategory(PlantColorScheme colorScheme) {
    return Row(
      children: [
        Icon(
          Icons.public,
          size: 16,
          color: colorScheme.primary,
        ),
        const SizedBox(width: 4),
        Expanded(
          flex: 2,
          child: Text(
            plant.origin,
            style: TextStyle(
              fontSize: 12,
              color: colorScheme.textSecondary,
            ),
            overflow: TextOverflow.ellipsis,
            maxLines: 1,
          ),
        ),
        const SizedBox(width: 8),
        Flexible(
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
            decoration: BoxDecoration(
              color: colorScheme.accent,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Text(
              plant.category,
              style: TextStyle(
                fontSize: 9,
                fontWeight: FontWeight.w500,
                color: colorScheme.text,
              ),
              overflow: TextOverflow.ellipsis,
              maxLines: 1,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildBenefitsSection() {
    return Wrap(
      spacing: 6,
      runSpacing: 4,
      children: plant.benefits.take(4).map((benefit) {
        final icon = VisualEnhancementService.getBenefitIcon(benefit);
        return Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: Colors.green.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.green.withValues(alpha: 0.3)),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                icon,
                size: 12,
                color: Colors.green,
              ),
              const SizedBox(width: 4),
              Text(
                benefit,
                style: const TextStyle(
                  fontSize: 10,
                  color: Colors.green,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        );
      }).toList(),
    );
  }

  Widget _buildDetailedInfo(PlantColorScheme colorScheme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Active compounds
        if (plant.activeCompounds.isNotEmpty) ...[
          Text(
            'Key Compounds:',
            style: TextStyle(
              fontWeight: FontWeight.w600,
              color: colorScheme.text,
              fontSize: 12,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            plant.activeCompounds.take(3).join(', '),
            style: TextStyle(
              fontSize: 11,
              color: colorScheme.textSecondary,
            ),
          ),
          const SizedBox(height: 8),
        ],
        
        // Traditional use
        Text(
          'Traditional Use:',
          style: TextStyle(
            fontWeight: FontWeight.w600,
            color: colorScheme.text,
            fontSize: 12,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          plant.traditionalUse.length > 100 
              ? '${plant.traditionalUse.substring(0, 100)}...'
              : plant.traditionalUse,
          style: TextStyle(
            fontSize: 11,
            color: colorScheme.textSecondary,
          ),
        ),
      ],
    );
  }

  Widget _buildRatingSection(PlantColorScheme colorScheme) {
    return Column(
      children: [
        Row(
          children: [
            // Rating stars
            Row(
              mainAxisSize: MainAxisSize.min,
              children: List.generate(5, (index) {
                return Icon(
                  index < plant.rating.floor()
                      ? Icons.star
                      : index < plant.rating
                          ? Icons.star_half
                          : Icons.star_border,
                  color: Colors.amber,
                  size: 14,
                );
              }),
            ),
            const SizedBox(width: 6),
            Text(
              plant.rating.toStringAsFixed(1),
              style: TextStyle(
                fontWeight: FontWeight.w600,
                color: colorScheme.text,
                fontSize: 12,
              ),
            ),
            const SizedBox(width: 4),
            Expanded(
              child: Text(
                '(${plant.reviewCount} reviews)',
                style: TextStyle(
                  fontSize: 11,
                  color: colorScheme.textSecondary,
                ),
                overflow: TextOverflow.ellipsis,
                maxLines: 1,
              ),
            ),

            // Conservation status indicator
            if (plant.isEndangered == true)
              Icon(
                Icons.warning,
                color: Colors.red,
                size: 16,
              ),
          ],
        ),
        const SizedBox(height: 8),
        // Enhanced Learn More Button
        Row(
          children: [
            EnhancedLearnMoreButton(
              plant: plant,
              showQuickInfo: true,
              onTap: onTap,
            ),
            const Spacer(),
            Text(
              'Tap for details',
              style: TextStyle(
                fontSize: 10,
                color: colorScheme.textSecondary,
                fontStyle: FontStyle.italic,
              ),
            ),
          ],
        ),
      ],
    );
  }
}
