import 'lib/services/commission_service.dart';
import 'lib/models/commission_models.dart';
import 'lib/models/vendor_models.dart';

/// Test that partners ONLY get paid when referrals actually buy things
/// Scenarios:
/// 1. ✅ Referral link used + Purchase completed = Partner gets paid
/// 2. ❌ Referral link used + Purchase cancelled = Partner gets nothing
/// 3. ❌ No referral link + Purchase completed = Partner gets nothing
/// 4. ❌ Referral link used + Purchase pending = Partner gets nothing (yet)
void main() async {
  print('🛒 Testing: Partners Only Get Paid When Referrals Buy Things\n');

  try {
    // Initialize commission service
    print('🚀 Initializing commission service...');
    final commissionService = CommissionService();
    await commissionService.initialize();
    print('✅ Commission service initialized!\n');

    final partnerId = 'partner_wellness_guru';
    final vendorId = 'vendor_healing_herbs';

    // Test Scenario 1: ✅ Referral link used + Purchase completed = Partner gets paid
    print('📋 Scenario 1: Referral Purchase Completed');
    print('   Expected: Partner earns commission ✅\n');

    final completedTransaction = Transaction(
      id: 'txn_completed_referral',
      orderId: 'order_001',
      customerId: 'customer_001',
      vendorId: vendorId,
      affiliateId: partnerId, // Referral link used
      productId: 'product_vitamin_c',
      productName: 'Vitamin C - 90 Capsules',
      category: ProductCategory.vitamins,
      productPrice: 100.0,
      quantity: 1,
      subtotal: 100.0,
      tax: 0.0,
      shipping: 0.0,
      total: 100.0,
      status: TransactionStatus.completed, // Purchase completed
      createdAt: DateTime.now(),
      completedAt: DateTime.now(),
      metadata: {"referral_source": "partner_link"},
    );

    await commissionService.processTransaction(completedTransaction);
    
    final completedCommissions = commissionService.getCommissions()
        .where((c) => c.transactionId == completedTransaction.id && c.partnerType == 'affiliate')
        .toList();

    if (completedCommissions.isNotEmpty) {
      final commission = completedCommissions.first;
      print('✅ PASS: Partner commission created');
      print('   Partner earns: \$${commission.totalAmount.toStringAsFixed(2)}');
      print('   Commission rate: ${commission.commissionRate}%');
      print('   Status: ${commission.status.name}');
      print('   Notes: ${commission.notes}\n');
    } else {
      print('❌ FAIL: No partner commission created for completed purchase\n');
    }

    // Test Scenario 2: ❌ Referral link used + Purchase cancelled = Partner gets nothing
    print('📋 Scenario 2: Referral Purchase Cancelled');
    print('   Expected: No partner commission ❌\n');

    final cancelledTransaction = Transaction(
      id: 'txn_cancelled_referral',
      orderId: 'order_002',
      customerId: 'customer_002',
      vendorId: vendorId,
      affiliateId: partnerId, // Referral link used
      productId: 'product_omega_3',
      productName: 'Omega 3 Fish Oil',
      category: ProductCategory.supplements,
      productPrice: 50.0,
      quantity: 1,
      subtotal: 50.0,
      tax: 0.0,
      shipping: 0.0,
      total: 50.0,
      status: TransactionStatus.cancelled, // Purchase cancelled
      createdAt: DateTime.now(),
      cancelledAt: DateTime.now(),
      cancellationReason: 'Customer changed mind',
      metadata: {"referral_source": "partner_link"},
    );

    await commissionService.processTransaction(cancelledTransaction);
    
    final cancelledCommissions = commissionService.getCommissions()
        .where((c) => c.transactionId == cancelledTransaction.id && c.partnerType == 'affiliate')
        .toList();

    if (cancelledCommissions.isEmpty) {
      print('✅ PASS: No partner commission created for cancelled purchase');
      print('   Reason: Transaction status = ${cancelledTransaction.status.name}\n');
    } else {
      print('❌ FAIL: Partner commission incorrectly created for cancelled purchase\n');
    }

    // Test Scenario 3: ❌ No referral link + Purchase completed = Partner gets nothing
    print('📋 Scenario 3: Direct Purchase (No Referral)');
    print('   Expected: No partner commission ❌\n');

    final directTransaction = Transaction(
      id: 'txn_direct_purchase',
      orderId: 'order_003',
      customerId: 'customer_003',
      vendorId: vendorId,
      affiliateId: null, // No referral link
      productId: 'product_probiotics',
      productName: 'Probiotics - 30 Capsules',
      category: ProductCategory.supplements,
      productPrice: 75.0,
      quantity: 1,
      subtotal: 75.0,
      tax: 0.0,
      shipping: 0.0,
      total: 75.0,
      status: TransactionStatus.completed, // Purchase completed
      createdAt: DateTime.now(),
      completedAt: DateTime.now(),
      metadata: {"source": "direct_visit"},
    );

    await commissionService.processTransaction(directTransaction);
    
    final directCommissions = commissionService.getCommissions()
        .where((c) => c.transactionId == directTransaction.id && c.partnerType == 'affiliate')
        .toList();

    if (directCommissions.isEmpty) {
      print('✅ PASS: No partner commission created for direct purchase');
      print('   Reason: No referral link used (affiliateId = null)\n');
    } else {
      print('❌ FAIL: Partner commission incorrectly created for direct purchase\n');
    }

    // Test Scenario 4: ❌ Referral link used + Purchase pending = Partner gets nothing (yet)
    print('📋 Scenario 4: Referral Purchase Pending');
    print('   Expected: No partner commission yet ❌\n');

    final pendingTransaction = Transaction(
      id: 'txn_pending_referral',
      orderId: 'order_004',
      customerId: 'customer_004',
      vendorId: vendorId,
      affiliateId: partnerId, // Referral link used
      productId: 'product_turmeric',
      productName: 'Turmeric Extract',
      category: ProductCategory.herbs,
      productPrice: 40.0,
      quantity: 1,
      subtotal: 40.0,
      tax: 0.0,
      shipping: 0.0,
      total: 40.0,
      status: TransactionStatus.pending, // Purchase pending
      createdAt: DateTime.now(),
      metadata: {"referral_source": "partner_link"},
    );

    await commissionService.processTransaction(pendingTransaction);
    
    final pendingCommissions = commissionService.getCommissions()
        .where((c) => c.transactionId == pendingTransaction.id && c.partnerType == 'affiliate')
        .toList();

    if (pendingCommissions.isEmpty) {
      print('✅ PASS: No partner commission created for pending purchase');
      print('   Reason: Transaction status = ${pendingTransaction.status.name}');
      print('   Note: Commission will be created when purchase completes\n');
    } else {
      print('❌ FAIL: Partner commission incorrectly created for pending purchase\n');
    }

    // Test Scenario 5: Transaction completion after pending
    print('📋 Scenario 5: Pending Transaction Later Completed');
    print('   Expected: Partner commission created when completed ✅\n');

    // Simulate transaction completion
    final completedPendingTransaction = Transaction(
      id: pendingTransaction.id,
      orderId: pendingTransaction.orderId,
      customerId: pendingTransaction.customerId,
      vendorId: pendingTransaction.vendorId,
      affiliateId: pendingTransaction.affiliateId,
      productId: pendingTransaction.productId,
      productName: pendingTransaction.productName,
      category: pendingTransaction.category,
      productPrice: pendingTransaction.productPrice,
      quantity: pendingTransaction.quantity,
      subtotal: pendingTransaction.subtotal,
      tax: pendingTransaction.tax,
      shipping: pendingTransaction.shipping,
      total: pendingTransaction.total,
      status: TransactionStatus.completed, // Now completed
      createdAt: pendingTransaction.createdAt,
      completedAt: DateTime.now(),
      metadata: pendingTransaction.metadata,
    );

    await commissionService.processTransaction(completedPendingTransaction);
    
    final completedPendingCommissions = commissionService.getCommissions()
        .where((c) => c.transactionId == completedPendingTransaction.id && c.partnerType == 'affiliate')
        .toList();

    if (completedPendingCommissions.isNotEmpty) {
      final commission = completedPendingCommissions.first;
      print('✅ PASS: Partner commission created after completion');
      print('   Partner earns: \$${commission.totalAmount.toStringAsFixed(2)}');
      print('   Transaction updated from pending → completed\n');
    } else {
      print('❌ FAIL: No partner commission created after completion\n');
    }

    // Summary
    print('📊 Test Summary:');
    final allCommissions = commissionService.getCommissions()
        .where((c) => c.partnerType == 'affiliate')
        .toList();
    
    print('   Total partner commissions created: ${allCommissions.length}');
    print('   Expected: 2 (only for completed purchases)');
    
    final totalPartnerEarnings = allCommissions.fold<double>(0.0, (sum, c) => sum + c.totalAmount);
    print('   Total partner earnings: \$${totalPartnerEarnings.toStringAsFixed(2)}');
    
    // Expected: $5 (from $100 sale) + $2 (from $40 sale) = $7 total
    final expectedEarnings = (100.0 * 0.05) + (40.0 * 0.05); // 5% commission rate
    print('   Expected earnings: \$${expectedEarnings.toStringAsFixed(2)}');
    
    final earningsMatch = (totalPartnerEarnings - expectedEarnings).abs() < 0.01;
    print('   Earnings match: ${earningsMatch ? "✅ PASS" : "❌ FAIL"}');

    if (allCommissions.length == 2 && earningsMatch) {
      print('\n🎉 ALL TESTS PASSED!');
      print('✅ Partners only get paid when referrals actually buy things');
      print('✅ No commission for cancelled, pending, or direct purchases');
      print('✅ Commission created only for completed referral purchases');
    } else {
      print('\n❌ SOME TESTS FAILED!');
      print('🔧 Review commission creation logic');
    }

  } catch (e, stackTrace) {
    print('❌ Test failed with error: $e');
    print('📋 Stack trace: $stackTrace');
  }
}
