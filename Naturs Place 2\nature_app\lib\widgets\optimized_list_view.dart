import 'package:flutter/material.dart';

/// High-performance optimized list view with advanced features
class OptimizedListView<T> extends StatefulWidget {
  final List<T> items;
  final Widget Function(BuildContext context, T item, int index) itemBuilder;
  final ScrollController? controller;
  final EdgeInsets? padding;
  final bool shrinkWrap;
  final ScrollPhysics? physics;
  final bool enableLazyLoading;
  final int preloadThreshold;
  final VoidCallback? onLoadMore;
  final bool enableVirtualization;
  final double? itemExtent;
  final Widget? separator;
  final bool enableCaching;
  final String? cacheKey;

  const OptimizedListView({
    super.key,
    required this.items,
    required this.itemBuilder,
    this.controller,
    this.padding,
    this.shrinkWrap = false,
    this.physics,
    this.enableLazyLoading = true,
    this.preloadThreshold = 5,
    this.onLoadMore,
    this.enableVirtualization = true,
    this.itemExtent,
    this.separator,
    this.enableCaching = true,
    this.cacheKey,
  });

  @override
  State<OptimizedListView<T>> createState() => _OptimizedListViewState<T>();
}

class _OptimizedListViewState<T> extends State<OptimizedListView<T>>
    with AutomaticKeepAliveClientMixin {
  
  late ScrollController _scrollController;
  final Map<int, Widget> _cachedWidgets = {};
  bool _isLoadingMore = false;
  
  @override
  bool get wantKeepAlive => widget.enableCaching;

  @override
  void initState() {
    super.initState();
    _scrollController = widget.controller ?? ScrollController();
    
    if (widget.enableLazyLoading && widget.onLoadMore != null) {
      _scrollController.addListener(_onScroll);
    }
  }

  @override
  void dispose() {
    if (widget.controller == null) {
      _scrollController.dispose();
    }
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent - 200) {
      _loadMore();
    }
  }

  void _loadMore() {
    if (!_isLoadingMore && widget.onLoadMore != null) {
      setState(() {
        _isLoadingMore = true;
      });
      
      widget.onLoadMore!();
      
      // Reset loading state after a delay
      Future.delayed(const Duration(milliseconds: 500), () {
        if (mounted) {
          setState(() {
            _isLoadingMore = false;
          });
        }
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);

    if (widget.enableVirtualization && widget.itemExtent != null) {
      return _buildVirtualizedList();
    } else {
      return _buildStandardList();
    }
  }

  Widget _buildVirtualizedList() {
    return ListView.builder(
      controller: _scrollController,
      padding: widget.padding,
      shrinkWrap: widget.shrinkWrap,
      physics: widget.physics,
      itemExtent: widget.itemExtent,
      itemCount: widget.items.length + (_isLoadingMore ? 1 : 0),
      itemBuilder: (context, index) {
        if (index >= widget.items.length) {
          return _buildLoadingIndicator();
        }

        return _buildCachedItem(index);
      },
    );
  }

  Widget _buildStandardList() {
    if (widget.separator != null) {
      return ListView.separated(
        controller: _scrollController,
        padding: widget.padding,
        shrinkWrap: widget.shrinkWrap,
        physics: widget.physics,
        itemCount: widget.items.length,
        separatorBuilder: (context, index) => widget.separator!,
        itemBuilder: (context, index) => _buildCachedItem(index),
      );
    } else {
      return ListView.builder(
        controller: _scrollController,
        padding: widget.padding,
        shrinkWrap: widget.shrinkWrap,
        physics: widget.physics,
        itemCount: widget.items.length + (_isLoadingMore ? 1 : 0),
        itemBuilder: (context, index) {
          if (index >= widget.items.length) {
            return _buildLoadingIndicator();
          }

          return _buildCachedItem(index);
        },
      );
    }
  }

  Widget _buildCachedItem(int index) {
    if (widget.enableCaching && _cachedWidgets.containsKey(index)) {
      return _cachedWidgets[index]!;
    }

    final item = widget.items[index];
    final builtWidget = widget.itemBuilder(context, item, index);

    if (widget.enableCaching) {
      _cachedWidgets[index] = builtWidget;

      // Limit cache size to prevent memory issues
      if (_cachedWidgets.length > 100) {
        final oldestKey = _cachedWidgets.keys.first;
        _cachedWidgets.remove(oldestKey);
      }
    }

    return builtWidget;
  }

  Widget _buildLoadingIndicator() {
    return Container(
      padding: const EdgeInsets.all(16),
      alignment: Alignment.center,
      child: const SizedBox(
        width: 24,
        height: 24,
        child: CircularProgressIndicator(
          strokeWidth: 2,
          valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF22c55e)),
        ),
      ),
    );
  }
}

/// Optimized grid view for better performance
class OptimizedGridView<T> extends StatefulWidget {
  final List<T> items;
  final Widget Function(BuildContext context, T item, int index) itemBuilder;
  final int crossAxisCount;
  final double mainAxisSpacing;
  final double crossAxisSpacing;
  final double childAspectRatio;
  final EdgeInsets? padding;
  final ScrollController? controller;
  final bool shrinkWrap;
  final ScrollPhysics? physics;
  final bool enableLazyLoading;
  final VoidCallback? onLoadMore;

  const OptimizedGridView({
    super.key,
    required this.items,
    required this.itemBuilder,
    this.crossAxisCount = 2,
    this.mainAxisSpacing = 8.0,
    this.crossAxisSpacing = 8.0,
    this.childAspectRatio = 1.0,
    this.padding,
    this.controller,
    this.shrinkWrap = false,
    this.physics,
    this.enableLazyLoading = true,
    this.onLoadMore,
  });

  @override
  State<OptimizedGridView<T>> createState() => _OptimizedGridViewState<T>();
}

class _OptimizedGridViewState<T> extends State<OptimizedGridView<T>> {
  late ScrollController _scrollController;
  bool _isLoadingMore = false;

  @override
  void initState() {
    super.initState();
    _scrollController = widget.controller ?? ScrollController();
    
    if (widget.enableLazyLoading && widget.onLoadMore != null) {
      _scrollController.addListener(_onScroll);
    }
  }

  @override
  void dispose() {
    if (widget.controller == null) {
      _scrollController.dispose();
    }
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent - 200) {
      _loadMore();
    }
  }

  void _loadMore() {
    if (!_isLoadingMore && widget.onLoadMore != null) {
      setState(() {
        _isLoadingMore = true;
      });
      
      widget.onLoadMore!();
      
      Future.delayed(const Duration(milliseconds: 500), () {
        if (mounted) {
          setState(() {
            _isLoadingMore = false;
          });
        }
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return GridView.builder(
      controller: _scrollController,
      padding: widget.padding,
      shrinkWrap: widget.shrinkWrap,
      physics: widget.physics,
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: widget.crossAxisCount,
        mainAxisSpacing: widget.mainAxisSpacing,
        crossAxisSpacing: widget.crossAxisSpacing,
        childAspectRatio: widget.childAspectRatio,
      ),
      itemCount: widget.items.length + (_isLoadingMore ? 1 : 0),
      itemBuilder: (context, index) {
        if (index >= widget.items.length) {
          return _buildLoadingIndicator();
        }

        final item = widget.items[index];
        return widget.itemBuilder(context, item, index);
      },
    );
  }

  Widget _buildLoadingIndicator() {
    return Container(
      alignment: Alignment.center,
      child: const SizedBox(
        width: 24,
        height: 24,
        child: CircularProgressIndicator(
          strokeWidth: 2,
          valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF22c55e)),
        ),
      ),
    );
  }
}

/// Optimized sliver list for custom scroll views
class OptimizedSliverList<T> extends StatelessWidget {
  final List<T> items;
  final Widget Function(BuildContext context, T item, int index) itemBuilder;
  final bool enableCaching;

  const OptimizedSliverList({
    super.key,
    required this.items,
    required this.itemBuilder,
    this.enableCaching = true,
  });

  @override
  Widget build(BuildContext context) {
    return SliverList(
      delegate: SliverChildBuilderDelegate(
        (context, index) {
          final item = items[index];
          return itemBuilder(context, item, index);
        },
        childCount: items.length,
        addAutomaticKeepAlives: enableCaching,
        addRepaintBoundaries: true,
        addSemanticIndexes: true,
      ),
    );
  }
}

/// Performance-optimized page view
class OptimizedPageView<T> extends StatefulWidget {
  final List<T> items;
  final Widget Function(BuildContext context, T item, int index) itemBuilder;
  final PageController? controller;
  final ValueChanged<int>? onPageChanged;
  final bool enableCaching;

  const OptimizedPageView({
    super.key,
    required this.items,
    required this.itemBuilder,
    this.controller,
    this.onPageChanged,
    this.enableCaching = true,
  });

  @override
  State<OptimizedPageView<T>> createState() => _OptimizedPageViewState<T>();
}

class _OptimizedPageViewState<T> extends State<OptimizedPageView<T>>
    with AutomaticKeepAliveClientMixin {
  
  @override
  bool get wantKeepAlive => widget.enableCaching;

  @override
  Widget build(BuildContext context) {
    super.build(context);

    return PageView.builder(
      controller: widget.controller,
      onPageChanged: widget.onPageChanged,
      itemCount: widget.items.length,
      itemBuilder: (context, index) {
        final item = widget.items[index];
        return widget.itemBuilder(context, item, index);
      },
    );
  }
}
