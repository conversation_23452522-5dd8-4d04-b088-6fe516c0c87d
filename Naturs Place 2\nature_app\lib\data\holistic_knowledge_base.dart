/// Comprehensive holistic and naturopathic knowledge base
class HolisticKnowledgeBase {
  
  /// Medicinal plant properties and uses
  static const Map<String, Map<String, dynamic>> medicinalPlants = {
    'turmeric': {
      'scientificName': 'Curcuma longa',
      'primaryConstituents': ['curcumin', 'turmerone', 'zingiberene'],
      'therapeuticActions': ['anti-inflammatory', 'antioxidant', 'hepatoprotective', 'antimicrobial'],
      'traditionalUses': ['arthritis', 'digestive disorders', 'wound healing', 'liver support'],
      'modernApplications': ['chronic inflammation', 'cardiovascular health', 'cognitive support', 'cancer prevention'],
      'dosage': {
        'powder': '1-3g daily',
        'extract': '300-600mg curcumin daily',
        'tea': '1 tsp powder in warm milk with black pepper'
      },
      'contraindications': ['gallstones', 'blood thinning medications', 'pregnancy (high doses)'],
      'interactions': ['warfarin', 'diabetes medications', 'chemotherapy drugs'],
      'preparation': {
        'golden_milk': 'Mix 1 tsp turmeric powder with warm milk, pinch of black pepper, honey',
        'paste': 'Mix powder with water for topical application',
        'tea': 'Simmer fresh root or powder in water for 10-15 minutes'
      }
    },
    
    'ashwagandha': {
      'scientificName': 'Withania somnifera',
      'primaryConstituents': ['withanolides', 'alkaloids', 'saponins'],
      'therapeuticActions': ['adaptogenic', 'nervine', 'immunomodulating', 'anti-stress'],
      'traditionalUses': ['stress management', 'insomnia', 'fatigue', 'immune weakness'],
      'modernApplications': ['anxiety disorders', 'chronic fatigue', 'thyroid support', 'athletic performance'],
      'dosage': {
        'powder': '1-6g daily',
        'extract': '300-500mg standardized extract twice daily',
        'tea': '1-2 tsp powder in warm water or milk'
      },
      'contraindications': ['autoimmune diseases', 'hyperthyroidism', 'pregnancy', 'breastfeeding'],
      'interactions': ['immunosuppressants', 'thyroid medications', 'sedatives'],
      'preparation': {
        'moon_milk': 'Mix powder with warm milk, ghee, and honey before bed',
        'vitamins_minerals': 'Standardized extract in vitamins & minerals form',
        'tincture': '1:5 ratio in 40% alcohol, 2-4ml three times daily'
      }
    },
    
    'echinacea': {
      'scientificName': 'Echinacea purpurea',
      'primaryConstituents': ['alkamides', 'polysaccharides', 'phenolic compounds'],
      'therapeuticActions': ['immunostimulant', 'anti-inflammatory', 'antimicrobial', 'wound healing'],
      'traditionalUses': ['infections', 'wound healing', 'snake bites', 'immune support'],
      'modernApplications': ['cold and flu prevention', 'upper respiratory infections', 'immune enhancement'],
      'dosage': {
        'tincture': '2-4ml three times daily',
        'tea': '1-2 tsp dried herb per cup, 3 times daily',
        'vitamins_minerals': '300-400mg standardized extract three times daily'
      },
      'contraindications': ['autoimmune diseases', 'allergies to Asteraceae family'],
      'interactions': ['immunosuppressants', 'hepatotoxic drugs'],
      'preparation': {
        'immune_tea': 'Combine with elderberry and ginger for enhanced effect',
        'tincture': 'Fresh plant tincture 1:2 ratio in 95% alcohol',
        'decoction': 'Simmer roots for 15-20 minutes'
      }
    },
    
    'ginger': {
      'scientificName': 'Zingiber officinale',
      'primaryConstituents': ['gingerols', 'shogaols', 'zingiberene'],
      'therapeuticActions': ['carminative', 'anti-nausea', 'anti-inflammatory', 'circulatory stimulant'],
      'traditionalUses': ['nausea', 'digestive disorders', 'motion sickness', 'cold conditions'],
      'modernApplications': ['chemotherapy-induced nausea', 'osteoarthritis', 'migraine prevention'],
      'dosage': {
        'fresh': '1-4g daily',
        'powder': '250mg-1g daily',
        'tea': '1-2 slices fresh root per cup'
      },
      'contraindications': ['gallstones', 'bleeding disorders', 'high doses in pregnancy'],
      'interactions': ['anticoagulants', 'diabetes medications'],
      'preparation': {
        'tea': 'Steep fresh slices in hot water for 5-10 minutes',
        'juice': 'Fresh ginger juice with honey for acute nausea',
        'compress': 'Grated ginger in cloth for topical application'
      }
    },
    
    'chamomile': {
      'scientificName': 'Matricaria chamomilla',
      'primaryConstituents': ['apigenin', 'bisabolol', 'chamazulene'],
      'therapeuticActions': ['nervine', 'anti-inflammatory', 'antispasmodic', 'mild sedative'],
      'traditionalUses': ['insomnia', 'anxiety', 'digestive upset', 'skin conditions'],
      'modernApplications': ['generalized anxiety disorder', 'sleep disorders', 'eczema', 'IBS'],
      'dosage': {
        'tea': '1-2 tsp dried flowers per cup, 3-4 times daily',
        'extract': '400-1600mg daily',
        'topical': '3-10% cream or ointment'
      },
      'contraindications': ['allergy to Asteraceae family', 'pregnancy (large amounts)'],
      'interactions': ['sedatives', 'anticoagulants'],
      'preparation': {
        'bedtime_tea': 'Steep flowers for 10-15 minutes, add honey',
        'compress': 'Strong tea applied to inflamed skin',
        'steam': 'Inhale steam from hot chamomile tea for respiratory issues'
      }
    }
  };

  /// Holistic treatment protocols by condition
  static const Map<String, Map<String, dynamic>> treatmentProtocols = {
    'digestive_health': {
      'primaryHerbs': ['ginger', 'peppermint', 'fennel', 'chamomile', 'licorice'],
      'supportiveNutrients': ['probiotics', 'digestive enzymes', 'L-glutamine', 'zinc'],
      'dietaryRecommendations': [
        'Eat mindfully and chew thoroughly',
        'Include fermented foods daily',
        'Avoid eating when stressed',
        'Stay hydrated between meals'
      ],
      'lifestyleFactors': [
        'Regular meal times',
        'Stress management',
        'Adequate sleep',
        'Regular exercise'
      ],
      'contraindications': ['active ulcers', 'severe IBD', 'gallbladder disease']
    },
    
    'immune_support': {
      'primaryHerbs': ['echinacea', 'elderberry', 'astragalus', 'reishi', 'garlic'],
      'supportiveNutrients': ['vitamin C', 'vitamin D', 'zinc', 'selenium', 'beta-glucans'],
      'dietaryRecommendations': [
        'Colorful fruits and vegetables',
        'Adequate protein intake',
        'Limit sugar and processed foods',
        'Include medicinal mushrooms'
      ],
      'lifestyleFactors': [
        '7-9 hours quality sleep',
        'Regular moderate exercise',
        'Stress management',
        'Social connections'
      ],
      'contraindications': ['autoimmune diseases', 'organ transplant recipients']
    },
    
    'stress_anxiety': {
      'primaryHerbs': ['ashwagandha', 'lemon balm', 'passionflower', 'lavender', 'holy basil'],
      'supportiveNutrients': ['magnesium', 'B-complex', 'L-theanine', 'omega-3s', 'GABA'],
      'dietaryRecommendations': [
        'Limit caffeine and alcohol',
        'Regular meals to stabilize blood sugar',
        'Include magnesium-rich foods',
        'Avoid excessive sugar'
      ],
      'lifestyleFactors': [
        'Daily meditation or mindfulness',
        'Regular sleep schedule',
        'Deep breathing exercises',
        'Time in nature'
      ],
      'contraindications': ['severe depression', 'bipolar disorder', 'pregnancy (some herbs)']
    },
    
    'sleep_disorders': {
      'primaryHerbs': ['valerian', 'chamomile', 'passionflower', 'lemon balm', 'lavender'],
      'supportiveNutrients': ['melatonin', 'magnesium', 'L-theanine', 'glycine', 'tart cherry'],
      'dietaryRecommendations': [
        'Avoid caffeine after 2 PM',
        'Light dinner 3 hours before bed',
        'Include tryptophan-rich foods',
        'Limit alcohol consumption'
      ],
      'lifestyleFactors': [
        'Consistent sleep schedule',
        'Cool, dark bedroom',
        'No screens 1 hour before bed',
        'Relaxing bedtime routine'
      ],
      'contraindications': ['sleep apnea', 'severe insomnia requiring medical attention']
    },
    
    'pain_inflammation': {
      'primaryHerbs': ['turmeric', 'ginger', 'boswellia', 'willow bark', 'devil\'s claw'],
      'supportiveNutrients': ['omega-3s', 'vitamin D', 'MSM', 'glucosamine', 'chondroitin'],
      'dietaryRecommendations': [
        'Anti-inflammatory diet',
        'Fatty fish 2-3 times weekly',
        'Colorful fruits and vegetables',
        'Avoid processed foods and trans fats'
      ],
      'lifestyleFactors': [
        'Regular gentle exercise',
        'Stress management',
        'Adequate sleep',
        'Heat/cold therapy'
      ],
      'contraindications': ['bleeding disorders', 'upcoming surgery', 'certain medications']
    }
  };

  /// Mind-body practices and their applications
  static const Map<String, Map<String, dynamic>> mindBodyPractices = {
    'meditation': {
      'types': ['mindfulness', 'loving-kindness', 'body scan', 'transcendental'],
      'benefits': ['stress reduction', 'improved focus', 'emotional regulation', 'pain management'],
      'techniques': {
        'mindfulness': 'Focus on breath, observe thoughts without judgment',
        'body_scan': 'Progressive awareness of body sensations',
        'loving_kindness': 'Cultivate compassion for self and others'
      },
      'duration': '5-45 minutes daily',
      'contraindications': ['severe PTSD', 'psychosis', 'severe depression without supervision']
    },
    
    'yoga': {
      'types': ['hatha', 'vinyasa', 'restorative', 'yin', 'kundalini'],
      'benefits': ['flexibility', 'strength', 'stress relief', 'pain management', 'sleep improvement'],
      'techniques': {
        'sun_salutation': 'Energizing morning sequence',
        'child_pose': 'Restorative pose for stress relief',
        'warrior_poses': 'Strength and confidence building'
      },
      'duration': '15-90 minutes, 3-7 times weekly',
      'contraindications': ['severe injuries', 'uncontrolled hypertension', 'recent surgery']
    },
    
    'breathwork': {
      'types': ['4-7-8 breathing', 'box breathing', 'alternate nostril', 'belly breathing'],
      'benefits': ['anxiety relief', 'improved sleep', 'stress management', 'energy regulation'],
      'techniques': {
        '4_7_8': 'Inhale 4, hold 7, exhale 8 counts',
        'box_breathing': 'Equal counts for inhale, hold, exhale, hold',
        'belly_breathing': 'Deep diaphragmatic breathing'
      },
      'duration': '5-20 minutes as needed',
      'contraindications': ['severe respiratory conditions', 'panic disorder (initially)']
    }
  };

  /// Nutritional therapy guidelines
  static const Map<String, Map<String, dynamic>> nutritionalTherapy = {
    'anti_inflammatory_diet': {
      'include': [
        'Fatty fish (salmon, sardines, mackerel)',
        'Leafy greens (spinach, kale, arugula)',
        'Berries (blueberries, strawberries, cherries)',
        'Nuts and seeds (walnuts, flaxseeds, chia)',
        'Olive oil and avocados',
        'Turmeric and ginger'
      ],
      'avoid': [
        'Processed foods and refined sugars',
        'Trans fats and fried foods',
        'Excessive omega-6 oils',
        'High-glycemic foods',
        'Excessive alcohol'
      ],
      'meal_timing': 'Regular meals, avoid late-night eating',
      'hydration': '8-10 glasses pure water daily'
    },
    
    'gut_healing_protocol': {
      'include': [
        'Bone broth and collagen',
        'Fermented foods (kefir, sauerkraut, kimchi)',
        'Prebiotic foods (garlic, onions, asparagus)',
        'Healing herbs (slippery elm, marshmallow root)',
        'Anti-inflammatory spices'
      ],
      'avoid': [
        'Food sensitivities and allergens',
        'NSAIDs and unnecessary antibiotics',
        'Excessive alcohol and caffeine',
        'Processed foods and additives'
      ],
      'supplements': ['L-glutamine', 'probiotics', 'digestive enzymes', 'zinc'],
      'duration': '3-6 months for significant healing'
    }
  };

  /// Safety guidelines and contraindications
  static const Map<String, List<String>> safetyGuidelines = {
    'pregnancy_avoid': [
      'High doses of turmeric',
      'Ashwagandha',
      'Angelica',
      'Blue cohosh',
      'Pennyroyal',
      'Tansy'
    ],
    'autoimmune_caution': [
      'Echinacea',
      'Astragalus',
      'Immune-stimulating mushrooms',
      'High-dose vitamin C',
      'Spirulina'
    ],
    'blood_thinning_interactions': [
      'Ginkgo',
      'Garlic (high doses)',
      'Ginger (high doses)',
      'Turmeric (high doses)',
      'Willow bark'
    ],
    'liver_disease_caution': [
      'Kava',
      'Comfrey',
      'Chaparral',
      'Germander',
      'High-dose niacin'
    ]
  };

  /// Emergency situations requiring immediate medical attention
  static const List<String> emergencySymptoms = [
    'Chest pain or pressure',
    'Difficulty breathing or shortness of breath',
    'Severe allergic reactions',
    'Signs of stroke (FAST)',
    'Severe bleeding',
    'Loss of consciousness',
    'Severe abdominal pain',
    'High fever with confusion',
    'Suicidal thoughts',
    'Severe dehydration'
  ];
}
