import 'package:flutter/foundation.dart';
import '../models/vendor_models.dart';
import '../models/commission_models.dart';
import '../data/product_dataset.dart';
import '../providers/app_state.dart';
import 'vendor_management_service.dart';
import 'commission_service.dart';

/// Service for integrating marketplace with vendor commission system
class MarketplaceIntegrationService {
  static final MarketplaceIntegrationService _instance = MarketplaceIntegrationService._internal();
  factory MarketplaceIntegrationService() => _instance;
  MarketplaceIntegrationService._internal();

  final VendorManagementService _vendorService = VendorManagementService();
  final CommissionService _commissionService = CommissionService();

  /// Initialize service
  Future<void> initialize() async {
    await _vendorService.initialize();
    await _commissionService.initialize();
    await _enhanceProductsWithVendorInfo();
    debugPrint('✅ Marketplace Integration Service initialized');
  }

  /// Process a product purchase and handle commissions
  Future<String> processPurchase({
    required String customerId,
    required String productId,
    required int quantity,
    String? affiliateTrackingCode,
    String? couponCode,
  }) async {
    try {
      // Get product details
      final product = ProductDataset.getAllProducts()
          .firstWhere((p) => p.id == productId);

      // Find vendor for this product
      final vendorId = _getVendorForProduct(productId);
      if (vendorId == null) {
        throw Exception('No vendor found for product: $productId');
      }

      // Find affiliate if tracking code provided
      String? affiliateId;
      if (affiliateTrackingCode != null) {
        final affiliate = _vendorService.getAffiliateByTrackingCode(affiliateTrackingCode);
        affiliateId = affiliate?.id;
        
        // Track referral
        if (affiliate != null) {
          await _trackAffiliateReferral(affiliate.id);
        }
      }

      // Calculate pricing
      final subtotal = product.price * quantity;
      final discount = _calculateDiscount(subtotal, couponCode);
      final tax = _calculateTax(subtotal - discount);
      final shipping = _calculateShipping(product, quantity);
      final total = subtotal - discount + tax + shipping;

      // Create transaction
      final transaction = Transaction(
        id: 'txn_${DateTime.now().millisecondsSinceEpoch}',
        orderId: 'order_${DateTime.now().millisecondsSinceEpoch}',
        customerId: customerId,
        vendorId: vendorId,
        affiliateId: affiliateId,
        productId: productId,
        productName: product.name,
        category: _getProductCategory(product),
        productPrice: product.price,
        quantity: quantity,
        subtotal: subtotal,
        tax: tax,
        shipping: shipping,
        total: total,
        discount: discount,
        couponCode: couponCode,
        status: TransactionStatus.pending,
        createdAt: DateTime.now(),
        metadata: const {},
      );

      // Process transaction
      final transactionId = await _commissionService.processTransaction(transaction);

      // Complete transaction (simulate payment processing)
      await _completeTransaction(transactionId);

      debugPrint('✅ Processed purchase: $transactionId');
      return transactionId;

    } catch (e) {
      debugPrint('❌ Error processing purchase: $e');
      rethrow;
    }
  }

  /// Complete a transaction
  Future<void> _completeTransaction(String transactionId) async {
    final transactions = _commissionService.transactions;
    final transactionIndex = transactions.indexWhere((t) => t.id == transactionId);
    
    if (transactionIndex != -1) {
      final transaction = transactions[transactionIndex];
      final completedTransaction = transaction.copyWith(
        status: TransactionStatus.completed,
        completedAt: DateTime.now(),
      );

      // Update transaction in commission service
      await _commissionService.processTransaction(completedTransaction);
    }
  }

  /// Track affiliate referral
  Future<void> _trackAffiliateReferral(String affiliateId) async {
    final affiliate = _vendorService.getAffiliateById(affiliateId);
    if (affiliate != null) {
      final updatedAffiliate = affiliate.copyWith(
        totalReferrals: affiliate.totalReferrals + 1,
      );
      await _vendorService.updateAffiliate(affiliateId, updatedAffiliate);
    }
  }

  /// Get vendor for a product
  String? _getVendorForProduct(String productId) {
    // In a real implementation, this would be stored in the product data
    // For demo purposes, we'll assign products to vendors based on category
    final product = ProductDataset.getAllProducts()
        .firstWhere((p) => p.id == productId);
    
    final category = _getProductCategory(product);
    final vendors = _vendorService.verifiedVendors
        .where((v) => v.categories.contains(category))
        .toList();
    
    if (vendors.isNotEmpty) {
      return vendors.first.id;
    }
    
    return null;
  }

  /// Get product category based on product type
  ProductCategory _getProductCategory(Product product) {
    // Categorize products based on their properties
    if (product.name.toLowerCase().contains('supplement') ||
        product.name.toLowerCase().contains('vitamin') ||
        product.name.toLowerCase().contains('capsule')) {
      return ProductCategory.supplements;
    } else if (product.name.toLowerCase().contains('oil') ||
               product.name.toLowerCase().contains('essential')) {
      return ProductCategory.essentialOils;
    } else if (product.name.toLowerCase().contains('tea') ||
               product.name.toLowerCase().contains('herbal')) {
      return ProductCategory.herbalTeas;
    } else if (product.name.toLowerCase().contains('cream') ||
               product.name.toLowerCase().contains('lotion') ||
               product.name.toLowerCase().contains('serum')) {
      return ProductCategory.skincare;
    } else if (product.name.toLowerCase().contains('book') ||
               product.name.toLowerCase().contains('guide')) {
      return ProductCategory.books;
    } else {
      return ProductCategory.equipment;
    }
  }

  /// Calculate discount based on coupon code
  double _calculateDiscount(double subtotal, String? couponCode) {
    if (couponCode == null) return 0.0;
    
    switch (couponCode.toUpperCase()) {
      case 'WELCOME10':
        return subtotal * 0.10; // 10% discount
      case 'HEALTH20':
        return subtotal * 0.20; // 20% discount
      case 'NEWUSER':
        return 15.0; // $15 flat discount
      default:
        return 0.0;
    }
  }

  /// Calculate tax (simplified)
  double _calculateTax(double taxableAmount) {
    return taxableAmount * 0.08; // 8% tax rate
  }

  /// Calculate shipping based on product and quantity
  double _calculateShipping(Product product, int quantity) {
    // Free shipping for orders over $50
    final subtotal = product.price * quantity;
    if (subtotal >= 50.0) return 0.0;
    
    // Standard shipping rates
    if (quantity == 1) return 5.99;
    if (quantity <= 3) return 7.99;
    return 9.99;
  }

  /// Get commission rate for vendor and category
  double getVendorCommissionRate(String vendorId, ProductCategory category) {
    final vendor = _vendorService.getVendorById(vendorId);
    if (vendor == null) return 0.0;
    return vendor.commissionRates[category] ?? 0.0;
  }

  /// Get commission rate for affiliate and category
  double getAffiliateCommissionRate(String affiliateId, ProductCategory category) {
    final affiliate = _vendorService.getAffiliateById(affiliateId);
    if (affiliate == null) return 0.0;
    return affiliate.commissionRates[category] ?? 0.0;
  }

  /// Get products by vendor
  List<Product> getProductsByVendor(String vendorId) {
    final vendor = _vendorService.getVendorById(vendorId);
    if (vendor == null) return [];
    
    return ProductDataset.getAllProducts().where((product) {
      final category = _getProductCategory(product);
      return vendor.categories.contains(category);
    }).toList();
  }

  /// Get featured vendor products
  List<Product> getFeaturedVendorProducts() {
    final featuredVendors = _vendorService.featuredVendors;
    final allProducts = ProductDataset.getAllProducts();
    
    return allProducts.where((product) {
      final category = _getProductCategory(product);
      return featuredVendors.any((vendor) => vendor.categories.contains(category));
    }).take(10).toList();
  }

  /// Get vendor info for a product
  Vendor? getVendorForProduct(String productId) {
    final vendorId = _getVendorForProduct(productId);
    if (vendorId == null) return null;
    return _vendorService.getVendorById(vendorId);
  }

  /// Calculate potential earnings for affiliate
  Map<String, double> calculateAffiliateEarnings(String affiliateId, double saleAmount) {
    final affiliate = _vendorService.getAffiliateById(affiliateId);
    if (affiliate == null) return {};
    
    final earnings = <String, double>{};
    
    for (final category in ProductCategory.values) {
      final commission = affiliate.calculateCommission(category, saleAmount);
      earnings[category.name] = commission;
    }
    
    return earnings;
  }

  /// Get marketplace analytics
  Map<String, dynamic> getMarketplaceAnalytics() {
    final transactions = _commissionService.transactions;
    final completedTransactions = transactions.where((t) => t.status == TransactionStatus.completed);
    
    final totalRevenue = completedTransactions.fold<double>(0.0, (sum, t) => sum + t.total);
    final totalOrders = completedTransactions.length;
    final averageOrderValue = totalOrders > 0 ? totalRevenue / totalOrders : 0.0;
    
    // Category breakdown
    final categoryBreakdown = <String, Map<String, dynamic>>{};
    for (final category in ProductCategory.values) {
      final categoryTransactions = completedTransactions.where((t) => t.category == category);
      final categoryRevenue = categoryTransactions.fold<double>(0.0, (sum, t) => sum + t.total);
      final categoryOrders = categoryTransactions.length;
      
      categoryBreakdown[category.name] = {
        'revenue': categoryRevenue,
        'orders': categoryOrders,
        'averageOrderValue': categoryOrders > 0 ? categoryRevenue / categoryOrders : 0.0,
      };
    }
    
    return {
      'totalRevenue': totalRevenue,
      'totalOrders': totalOrders,
      'averageOrderValue': averageOrderValue,
      'categoryBreakdown': categoryBreakdown,
      'activeVendors': _vendorService.verifiedVendors.length,
      'activeAffiliates': _vendorService.activeAffiliates.length,
      'featuredVendors': _vendorService.featuredVendors.length,
    };
  }

  /// Enhance products with vendor information
  Future<void> _enhanceProductsWithVendorInfo() async {
    // This would typically update product data with vendor information
    // For now, we'll just log that products have been enhanced
    debugPrint('📦 Enhanced ${ProductDataset.getAllProducts().length} products with vendor info');
  }

  /// Apply affiliate tracking to product URL
  String applyAffiliateTracking(String productUrl, String trackingCode) {
    final uri = Uri.parse(productUrl);
    final queryParams = Map<String, String>.from(uri.queryParameters);
    queryParams['ref'] = trackingCode;
    
    return uri.replace(queryParameters: queryParams).toString();
  }

  /// Validate affiliate tracking code
  bool validateTrackingCode(String trackingCode) {
    final affiliate = _vendorService.getAffiliateByTrackingCode(trackingCode);
    return affiliate != null && affiliate.isActive;
  }
}
