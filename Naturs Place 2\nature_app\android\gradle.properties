# Gradle JVM settings - Reduced memory allocation to prevent out of memory
org.gradle.jvmargs=-Xmx2G -XX:MaxMetaspaceSize=1G -XX:ReservedCodeCacheSize=256m -XX:+UseG1GC -Dfile.encoding=UTF-8

# Android settings
android.useAndroidX=true
android.enableJetifier=true

# Performance settings - Disabled parallel builds to reduce memory usage
org.gradle.parallel=false
org.gradle.caching=false
org.gradle.configureondemand=false

# Daemon settings
org.gradle.daemon=true
