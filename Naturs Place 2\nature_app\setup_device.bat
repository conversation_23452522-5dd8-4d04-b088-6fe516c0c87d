@echo off
echo 📱 Device Setup for Nature's Place App 📱
echo.

echo Checking available devices...
flutter devices
echo.

echo Checking available emulators...
flutter emulators
echo.

echo If no devices are available, you can:
echo 1. Connect a physical Android device with USB debugging enabled
echo 2. Start an Android emulator from Android Studio
echo 3. Use the command: flutter emulators --launch [emulator_id]
echo.

echo Current ADB devices:
adb devices
echo.

echo 🔧 Troubleshooting tips:
echo - Make sure Android Studio is installed
echo - Enable Developer Options on your Android device
echo - Enable USB Debugging in Developer Options
echo - Accept the USB debugging prompt on your device
echo - Try running 'adb kill-server' then 'adb start-server' if needed
echo.

pause
