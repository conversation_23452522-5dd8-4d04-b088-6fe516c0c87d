import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'screens/ai_assistant_screen.dart';
import 'package:provider/provider.dart';
import 'screens/main_screen.dart';
import 'screens/profile_screen.dart';
import 'screens/settings_screen.dart';
import 'screens/vendor_login_screen.dart';
import 'screens/affiliate_login_screen.dart';
import 'providers/app_state.dart';
import 'services/auth_service.dart';
import 'services/camera_service.dart';
import 'services/affiliate_marketplace_service.dart';
import 'services/in_app_purchase_service.dart';
import 'services/admin_service.dart';
import 'services/daily_plant_update_service.dart';
import 'services/performance_optimization_service.dart';
// Web services are conditionally imported
// import 'services/web_enhancement_service.dart';
// import 'services/web_security_service.dart';
import 'services/crypto_payment_service.dart';
import 'widgets/optimized_image.dart';
import 'theme/app_theme.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize performance optimizations first
  await PerformanceOptimizationService.initialize();

  // Web-specific enhancements are handled separately for web builds
  // This avoids dart:html import issues on mobile platforms
  debugPrint('Platform: ${kIsWeb ? "Web" : "Mobile"}');

  // Set preferred orientations for better performance (mobile only)
  try {
    if (!kIsWeb) {
      await SystemChrome.setPreferredOrientations([
        DeviceOrientation.portraitUp,
        DeviceOrientation.portraitDown,
      ]);
    }
  } catch (e) {
    // Orientation setting may not be supported on web
    debugPrint('Orientation setting not supported: $e');
  }

  // Initialize services
  await CameraService.initializeCameras();
  await AuthService().initialize();
  await AdminService().initialize();

  // Initialize plant services for daily updates
  await DailyPlantUpdateService().initialize();

  // Initialize crypto payment service for future use
  await CryptoPaymentService.initialize();

  // Set global navigator key for image preloading
  NavigationService.navigatorKey;

  runApp(const NatureApp());
}

class NatureApp extends StatelessWidget {
  const NatureApp({super.key});

  @override
  Widget build(BuildContext context) {
    return Directionality(
      textDirection: TextDirection.ltr,
      child: MultiProvider(
        providers: [
          ChangeNotifierProvider(create: (context) => AppState()),
          ChangeNotifierProvider(create: (context) => AuthService()),
          ChangeNotifierProvider(create: (context) => PartnerMarketplaceService()),
          ChangeNotifierProvider(create: (context) => InAppPurchaseService()),
          ChangeNotifierProvider(create: (context) => AdminService()),
        ],
        child: MaterialApp(
            title: "Nature's Place",
            debugShowCheckedModeBanner: false,
            navigatorKey: NavigationService.navigatorKey,
            theme: AppTheme.lightTheme,
            home: const MainScreen(),
            routes: {
              '/profile': (context) => const ProfileScreen(),
              '/settings': (context) => const SettingsScreen(),
              '/vendor-login': (context) => const VendorLoginScreen(),
              '/affiliate-login': (context) => const AffiliateLoginScreen(),
              '/ai-assistant': (context) => const AIAssistantScreen(),
            },
            builder: (context, child) {
              // Apply performance optimizations to the entire app
              return MediaQuery(
                data: MediaQuery.of(context).copyWith(
                  textScaler: TextScaler.linear(
                    MediaQuery.of(context).textScaler.scale(1.0).clamp(0.8, 1.2),
                  ),
                ),
                child: child!,
              );
            },
          ), // Close MaterialApp
        ), // Close MultiProvider
    ); // Close Directionality
  }
}
