import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import '../services/affiliate_marketplace_service.dart';

class ProductUploadScreen extends StatefulWidget {
  final String partnerId;
  
  const ProductUploadScreen({
    super.key,
    required this.partnerId,
  });

  @override
  State<ProductUploadScreen> createState() => _ProductUploadScreenState();
}

class _ProductUploadScreenState extends State<ProductUploadScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _priceController = TextEditingController();
  final _affiliateLinkController = TextEditingController();
  final _affiliateCodeController = TextEditingController();
  final _vendorWebsiteController = TextEditingController();

  String _selectedCategory = 'Supplements';
  String _selectedNetwork = 'iHerb';
  bool _redirectToVendorSite = false;
  bool _isUploading = false;
  
  final List<String> _categories = [
    'Supplements',
    'Essential Oils',
    'Herbs',
    'Teas',
    'Skincare',
    'Vitamins',
    'Superfoods',
    'Aromatherapy',
  ];
  
  final List<String> _networks = [
    'iHerb',
    'Amazon',
    'Thrive Market',
    'Plant Therapy',
    'Vitacost',
    'Mountain Rose Herbs',
  ];

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    _priceController.dispose();
    _affiliateLinkController.dispose();
    _affiliateCodeController.dispose();
    _vendorWebsiteController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final affiliateService = Provider.of<PartnerMarketplaceService>(context);
    final uploadQuota = affiliateService.getUploadQuota(widget.partnerId);
    
    return Scaffold(
      appBar: AppBar(
        title: const Text('Upload Product'),
        backgroundColor: const Color(0xFF22c55e),
        foregroundColor: Colors.white,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Upload Quota Status
            _buildUploadQuotaCard(uploadQuota),
            const SizedBox(height: 24),
            
            // Upload Form
            if (uploadQuota['canUploadMore']) ...[
              _buildUploadForm(),
            ] else ...[
              _buildUploadLimitReached(uploadQuota),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildUploadQuotaCard(Map<String, dynamic> quota) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const FaIcon(
                  FontAwesomeIcons.cloudArrowUp,
                  color: Color(0xFF22c55e),
                  size: 24,
                ),
                const SizedBox(width: 12),
                Text(
                  'Upload Quota - ${quota['tier']} Tier',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Used: ${quota['uploadedCount']}/${quota['uploadLimit']}',
                  style: Theme.of(context).textTheme.titleMedium,
                ),
                Text(
                  '${quota['remainingUploads']} remaining',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    color: quota['canUploadMore'] ? Colors.green : Colors.red,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            LinearProgressIndicator(
              value: quota['uploadProgressPercentage'] / 100,
              backgroundColor: Colors.grey[300],
              valueColor: AlwaysStoppedAnimation<Color>(
                quota['uploadProgressPercentage'] >= 90
                  ? Colors.red
                  : quota['uploadProgressPercentage'] >= 80
                    ? Colors.orange
                    : Colors.green,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildUploadForm() {
    return Form(
      key: _formKey,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Product Information',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          
          // Product Name
          TextFormField(
            controller: _nameController,
            decoration: const InputDecoration(
              labelText: 'Product Name *',
              border: OutlineInputBorder(),
              prefixIcon: Icon(Icons.shopping_bag),
            ),
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Please enter a product name';
              }
              return null;
            },
          ),
          const SizedBox(height: 16),
          
          // Description
          TextFormField(
            controller: _descriptionController,
            decoration: const InputDecoration(
              labelText: 'Description *',
              border: OutlineInputBorder(),
              prefixIcon: Icon(Icons.description),
            ),
            maxLines: 3,
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Please enter a description';
              }
              return null;
            },
          ),
          const SizedBox(height: 16),
          
          // Price and Category Row
          Row(
            children: [
              Expanded(
                child: TextFormField(
                  controller: _priceController,
                  decoration: const InputDecoration(
                    labelText: 'Price *',
                    border: OutlineInputBorder(),
                    prefixIcon: Icon(Icons.attach_money),
                  ),
                  keyboardType: TextInputType.number,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please enter a price';
                    }
                    if (double.tryParse(value) == null) {
                      return 'Please enter a valid price';
                    }
                    return null;
                  },
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: DropdownButtonFormField<String>(
                  value: _selectedCategory,
                  decoration: const InputDecoration(
                    labelText: 'Category',
                    border: OutlineInputBorder(),
                    prefixIcon: Icon(Icons.category),
                  ),
                  items: _categories.map((category) {
                    return DropdownMenuItem(
                      value: category,
                      child: Text(category),
                    );
                  }).toList(),
                  onChanged: (value) {
                    setState(() {
                      _selectedCategory = value!;
                    });
                  },
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          
          // Affiliate Network
          DropdownButtonFormField<String>(
            value: _selectedNetwork,
            decoration: const InputDecoration(
              labelText: 'Affiliate Network',
              border: OutlineInputBorder(),
              prefixIcon: Icon(Icons.link),
            ),
            items: _networks.map((network) {
              return DropdownMenuItem(
                value: network,
                child: Text(network),
              );
            }).toList(),
            onChanged: (value) {
              setState(() {
                _selectedNetwork = value!;
              });
            },
          ),
          const SizedBox(height: 16),
          
          // Affiliate Link
          TextFormField(
            controller: _affiliateLinkController,
            decoration: const InputDecoration(
              labelText: 'Affiliate Link *',
              border: OutlineInputBorder(),
              prefixIcon: Icon(Icons.link),
            ),
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Please enter an affiliate link';
              }
              return null;
            },
          ),
          const SizedBox(height: 16),
          
          // Affiliate Code
          TextFormField(
            controller: _affiliateCodeController,
            decoration: const InputDecoration(
              labelText: 'Affiliate Code *',
              border: OutlineInputBorder(),
              prefixIcon: Icon(Icons.code),
            ),
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Please enter an affiliate code';
              }
              return null;
            },
          ),
          const SizedBox(height: 16),

          // Vendor Website Section
          Card(
            color: Colors.blue[50],
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      const Icon(Icons.web, color: Colors.blue),
                      const SizedBox(width: 8),
                      Text(
                        'Vendor Website Settings',
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: Colors.blue[700],
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),

                  // Redirect Toggle
                  SwitchListTile(
                    title: const Text('Redirect to my website'),
                    subtitle: const Text('Send customers to your website instead of affiliate link'),
                    value: _redirectToVendorSite,
                    onChanged: (value) {
                      setState(() {
                        _redirectToVendorSite = value;
                      });
                    },
                    activeColor: Colors.blue,
                  ),

                  // Vendor Website URL (only if redirect is enabled)
                  if (_redirectToVendorSite) ...[
                    const SizedBox(height: 12),
                    TextFormField(
                      controller: _vendorWebsiteController,
                      decoration: const InputDecoration(
                        labelText: 'Your Website URL *',
                        border: OutlineInputBorder(),
                        prefixIcon: Icon(Icons.link),
                        hintText: 'https://yourwebsite.com/product',
                      ),
                      validator: (value) {
                        if (_redirectToVendorSite && (value == null || value.isEmpty)) {
                          return 'Please enter your website URL';
                        }
                        if (_redirectToVendorSite && value != null && !value.startsWith('http')) {
                          return 'Please enter a valid URL starting with http:// or https://';
                        }
                        return null;
                      },
                    ),
                    const SizedBox(height: 8),
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.green.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(4),
                        border: Border.all(color: Colors.green.withValues(alpha: 0.3)),
                      ),
                      child: const Row(
                        children: [
                          Icon(Icons.info, color: Colors.green, size: 16),
                          SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              'When customers click "Buy", they\'ll be redirected to your website. You\'ll receive a notification when they make a purchase.',
                              style: TextStyle(fontSize: 12, color: Colors.green),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ),
          const SizedBox(height: 24),
          
          // Upload Button
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: _isUploading ? null : _uploadProduct,
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF22c55e),
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
              ),
              child: _isUploading
                ? const Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                        ),
                      ),
                      SizedBox(width: 12),
                      Text('Uploading...'),
                    ],
                  )
                : const Text(
                    'Upload Product',
                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                  ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildUploadLimitReached(Map<String, dynamic> quota) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            const Icon(
              Icons.block,
              color: Colors.red,
              size: 48,
            ),
            const SizedBox(height: 16),
            Text(
              'Upload Limit Reached',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: Colors.red,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'You have reached your upload limit of ${quota['uploadLimit']} products for the ${quota['tier']} tier.',
              textAlign: TextAlign.center,
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () {
                // Show upgrade options
                _showUpgradeOptions();
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.orange,
                foregroundColor: Colors.white,
              ),
              child: const Text('Upgrade Your Plan'),
            ),
          ],
        ),
      ),
    );
  }

  void _uploadProduct() async {
    if (!_formKey.currentState!.validate()) return;
    
    setState(() {
      _isUploading = true;
    });
    
    try {
      final affiliateService = Provider.of<PartnerMarketplaceService>(context, listen: false);
      
      // Create new product
      final product = PartnerProduct(
        id: 'af_${DateTime.now().millisecondsSinceEpoch}',
        name: _nameController.text,
        description: _descriptionController.text,
        imageUrl: _generateProductImageUrl(_nameController.text, _selectedCategory),
        price: double.parse(_priceController.text),
        category: _selectedCategory,
        rating: 4.5, // Default rating
        reviewCount: 0,
        seller: _selectedNetwork,
        partnerNetwork: _selectedNetwork,
        affiliateLink: _affiliateLinkController.text,
        affiliateCode: _affiliateCodeController.text,
        commissionRate: PartnerCommissions.getCommissionRate(_selectedNetwork),
        isVerified: true,
        tags: [_selectedCategory.toLowerCase(), 'natural', 'organic'],
        shortDescription: _descriptionController.text.length > 50
          ? '${_descriptionController.text.substring(0, 50)}...'
          : _descriptionController.text,
        benefits: ['Natural ingredients', 'High quality', 'Verified product'],
        ingredients: ['Natural ingredients'],
        dateAdded: DateTime.now(),
        inStock: true,
        vendorWebsite: _redirectToVendorSite ? _vendorWebsiteController.text : null,
        redirectToVendorSite: _redirectToVendorSite,
        vendorId: widget.partnerId,
      );
      
      // Attempt upload
      final result = affiliateService.uploadProduct(widget.partnerId, product);
      
      if (result['success']) {
        _showSuccessDialog(result);
      } else {
        _showErrorDialog(result);
      }
    } catch (e) {
      _showErrorDialog({'reason': 'An unexpected error occurred: $e'});
    } finally {
      setState(() {
        _isUploading = false;
      });
    }
  }

  void _showSuccessDialog(Map<String, dynamic> result) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.check_circle, color: Colors.green),
            SizedBox(width: 8),
            Text('Upload Successful!'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(result['message']),
            if (result['remainingUploads'] != null && result['remainingUploads'] >= 0) ...[
              const SizedBox(height: 8),
              Text('Remaining uploads: ${result['remainingUploads']}'),
            ],
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _clearForm();
            },
            child: const Text('Upload Another'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              Navigator.of(context).pop(); // Go back to dashboard
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.green,
              foregroundColor: Colors.white,
            ),
            child: const Text('Done'),
          ),
        ],
      ),
    );
  }

  void _showErrorDialog(Map<String, dynamic> result) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.error, color: Colors.red),
            SizedBox(width: 8),
            Text('Upload Failed'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(result['reason'] ?? 'Unknown error occurred'),
            if (result['suggestedAction'] != null) ...[
              const SizedBox(height: 8),
              Text(
                'Suggestion: ${result['suggestedAction']}',
                style: const TextStyle(fontStyle: FontStyle.italic),
              ),
            ],
          ],
        ),
        actions: [
          if (result['errorCode'] == 'UPLOAD_LIMIT_REACHED') ...[
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                _showUpgradeOptions();
              },
              child: const Text('Upgrade Plan'),
            ),
          ],
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  void _showUpgradeOptions() {
    // This would typically navigate to upgrade screen or show upgrade dialog
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Upgrade options would be shown here'),
        backgroundColor: Colors.orange,
      ),
    );
  }

  void _clearForm() {
    _nameController.clear();
    _descriptionController.clear();
    _priceController.clear();
    _affiliateLinkController.clear();
    _affiliateCodeController.clear();
    _vendorWebsiteController.clear();
    setState(() {
      _selectedCategory = 'Supplements';
      _selectedNetwork = 'iHerb';
      _redirectToVendorSite = false;
    });
  }

  /// Generate product image URL based on product name and category
  String _generateProductImageUrl(String productName, String category) {
    // Map of product categories to appropriate images
    final categoryImageMap = {
      'Supplements': {
        'ashwagandha': 'https://cdn.pixabay.com/photo/2019/11/07/21/32/ashwagandha-4611052_1280.jpg',
        'turmeric': 'https://cdn.pixabay.com/photo/2017/05/11/19/44/turmeric-2305199_1280.jpg',
        'ginger': 'https://cdn.pixabay.com/photo/2017/05/11/19/44/ginger-2305199_1280.jpg',
        'echinacea': 'https://cdn.pixabay.com/photo/2018/08/14/19/45/echinacea-3606356_1280.jpg',
        'ginseng': 'https://cdn.pixabay.com/photo/2017/09/05/10/20/ginseng-2717004_1280.jpg',
        'default': 'https://cdn.pixabay.com/photo/2018/03/17/19/36/supplements-3234344_1280.jpg',
      },
      'Essential Oils': {
        'lavender': 'https://cdn.pixabay.com/photo/2015/07/02/20/57/lavender-830922_1280.jpg',
        'tea tree': 'https://cdn.pixabay.com/photo/2017/08/07/14/02/tea-tree-2604616_1280.jpg',
        'eucalyptus': 'https://cdn.pixabay.com/photo/2016/11/29/12/45/eucalyptus-1869227_1280.jpg',
        'frankincense': 'https://cdn.pixabay.com/photo/2019/12/09/16/48/frankincense-4684134_1280.jpg',
        'default': 'https://cdn.pixabay.com/photo/2018/05/15/21/24/essential-oils-3404181_1280.jpg',
      },
      'Herbal Teas': {
        'chamomile': 'https://cdn.pixabay.com/photo/2016/07/26/16/16/chamomile-1543495_1280.jpg',
        'lemon balm': 'https://cdn.pixabay.com/photo/2018/07/01/20/01/lemon-balm-3510072_1280.jpg',
        'tulsi': 'https://cdn.pixabay.com/photo/2018/10/15/19/52/tulsi-3749933_1280.jpg',
        'yerba mate': 'https://cdn.pixabay.com/photo/2018/03/17/19/36/yerba-mate-3234344_1280.jpg',
        'default': 'https://cdn.pixabay.com/photo/2018/05/15/21/24/herbal-tea-3404181_1280.jpg',
      },
      'Skincare': {
        'aloe': 'https://cdn.pixabay.com/photo/2018/04/02/07/42/aloe-3283938_1280.jpg',
        'calendula': 'https://cdn.pixabay.com/photo/2018/05/15/21/24/calendula-3404181_1280.jpg',
        'rosemary': 'https://cdn.pixabay.com/photo/2015/05/30/21/20/rosemary-791120_1280.jpg',
        'default': 'https://cdn.pixabay.com/photo/2019/08/21/15/34/natural-skincare-4421847_1280.jpg',
      },
      'Mushrooms': {
        'reishi': 'https://cdn.pixabay.com/photo/2019/11/07/21/32/reishi-4611052_1280.jpg',
        'shiitake': 'https://cdn.pixabay.com/photo/2017/09/16/19/21/shiitake-2756645_1280.jpg',
        'cordyceps': 'https://cdn.pixabay.com/photo/2019/09/17/18/48/cordyceps-4484363_1280.jpg',
        'chaga': 'https://cdn.pixabay.com/photo/2019/09/17/18/48/chaga-4484363_1280.jpg',
        'default': 'https://cdn.pixabay.com/photo/2019/09/17/18/48/mushroom-4484363_1280.jpg',
      },
    };

    // Get category-specific images
    final categoryImages = categoryImageMap[category] ?? categoryImageMap['Supplements']!;

    // Try to match product name with specific images
    final productNameLower = productName.toLowerCase();
    for (final key in categoryImages.keys) {
      if (productNameLower.contains(key)) {
        return categoryImages[key]!;
      }
    }

    // Return default image for category
    return categoryImages['default']!;
  }
}
