import '../models/user_models.dart';

/// Service for managing role-based access control
class RoleBasedAccessService {
  static final RoleBasedAccessService _instance = RoleBasedAccessService._internal();
  factory RoleBasedAccessService() => _instance;
  RoleBasedAccessService._internal();

  /// Check if user has access to specific feature
  bool hasAccess(UserRole userRole, String feature) {
    switch (userRole) {
      case UserRole.customer:
        return _customerAccess.contains(feature);
      case UserRole.partner:
        return _partnerAccess.contains(feature);
      case UserRole.vendor:
        return _vendorAccess.contains(feature);
      case UserRole.admin:
        return _adminAccess.contains(feature);
    }
  }

  /// Get allowed dashboard for user role
  String getDashboardRoute(UserRole userRole) {
    switch (userRole) {
      case UserRole.customer:
        return '/customer-dashboard';
      case UserRole.partner:
        return '/partner-dashboard';
      case UserRole.vendor:
        return '/vendor-dashboard';
      case UserRole.admin:
        return '/admin-dashboard';
    }
  }

  /// Get restricted features for user role
  List<String> getRestrictedFeatures(UserRole userRole) {
    final allFeatures = _getAllFeatures();
    final allowedFeatures = _getAccessList(userRole);
    return allFeatures.where((feature) => !allowedFeatures.contains(feature)).toList();
  }

  /// Validate user access to route
  bool canAccessRoute(UserRole userRole, String route) {
    switch (userRole) {
      case UserRole.customer:
        return _customerRoutes.contains(route);
      case UserRole.partner:
        return _partnerRoutes.contains(route);
      case UserRole.vendor:
        return _vendorRoutes.contains(route);
      case UserRole.admin:
        return true; // Admin can access all routes
    }
  }

  /// Get vendor tier fee based on tier level
  double getVendorTierFee(VendorTier tier) {
    switch (tier) {
      case VendorTier.basic:
        return 29.99; // $29.99/month
      case VendorTier.standard:
        return 59.99; // $59.99/month
      case VendorTier.premium:
        return 99.99; // $99.99/month
      case VendorTier.enterprise:
        return 199.99; // $199.99/month
    }
  }

  /// Get vendor tier benefits with product limits and marketing options
  Map<String, dynamic> getVendorTierBenefits(VendorTier tier) {
    switch (tier) {
      case VendorTier.basic:
        return {
          'max_products': 50,
          'max_daily_uploads': 5,
          'max_weekly_uploads': 20,
          'commission_rate': 15.0,
          'analytics_access': false,
          'priority_support': false,
          'custom_branding': false,
          'api_access': false,
          'marketing_options': {
            'featured_products_included': 0,
            'marketing_access_level': 'basic', // basic, standard, premium
            'available_marketing_services': [
              'product_badges_basic', // Free basic badges
            ],
          },
          'upload_restrictions': {
            'image_limit_per_product': 3,
            'video_uploads': false,
            'bulk_upload': false,
            'csv_import': false,
          },
        };
      case VendorTier.standard:
        return {
          'max_products': 200,
          'max_daily_uploads': 15,
          'max_weekly_uploads': 75,
          'commission_rate': 12.0,
          'analytics_access': true,
          'priority_support': false,
          'custom_branding': false,
          'api_access': false,
          'marketing_options': {
            'featured_products_included': 1, // 1 free featured product
            'marketing_access_level': 'standard',
            'available_marketing_services': [
              'product_badges_basic',
              'product_badges_enhanced',
              'seo_optimization_basic',
            ],
          },
          'upload_restrictions': {
            'image_limit_per_product': 5,
            'video_uploads': true,
            'bulk_upload': true,
            'csv_import': true,
          },
        };
      case VendorTier.premium:
        return {
          'max_products': 500,
          'max_daily_uploads': 30,
          'max_weekly_uploads': 150,
          'commission_rate': 10.0,
          'analytics_access': true,
          'priority_support': true,
          'custom_branding': true,
          'api_access': false,
          'marketing_options': {
            'featured_products_included': 2, // 2 free featured products
            'marketing_access_level': 'premium',
            'available_marketing_services': [
              'product_badges_basic',
              'product_badges_enhanced',
              'product_badges_premium',
              'seo_optimization_basic',
              'seo_optimization_advanced',
            ],
          },
          'upload_restrictions': {
            'image_limit_per_product': 10,
            'video_uploads': true,
            'bulk_upload': true,
            'csv_import': true,
          },
        };
      case VendorTier.enterprise:
        return {
          'max_products': 2000, // High limit but not unlimited to prevent overload
          'max_daily_uploads': 100,
          'max_weekly_uploads': 500,
          'commission_rate': 8.0,
          'analytics_access': true,
          'priority_support': true,
          'custom_branding': true,
          'api_access': true,
          'marketing_options': {
            'featured_products_included': 3, // 3 free featured products
            'marketing_access_level': 'enterprise',
            'available_marketing_services': [
              'product_badges_basic',
              'product_badges_enhanced',
              'product_badges_premium',
              'product_badges_exclusive',
              'seo_optimization_basic',
              'seo_optimization_advanced',
              'seo_optimization_enterprise',
            ],
          },
          'upload_restrictions': {
            'image_limit_per_product': 20,
            'video_uploads': true,
            'bulk_upload': true,
            'csv_import': true,
          },
        };
    }
  }

  // Customer access permissions
  static const List<String> _customerAccess = [
    'browse_products',
    'make_purchases',
    'view_order_history',
    'plant_scanner',
    'ai_assistant',
    'encyclopedia_basic',
    'profile_management',
    'wishlist',
    'reviews',
  ];

  // Partner access permissions (referral partners)
  static const List<String> _partnerAccess = [
    'partner_dashboard',
    'referral_links',
    'commission_tracking',
    'performance_analytics',
    'marketing_materials',
    'payout_history',
    'conversion_metrics',
    'partner_training',
    'profile_management',
  ];

  // Vendor access permissions
  static const List<String> _vendorAccess = [
    'vendor_dashboard',
    'product_management',
    'inventory_tracking',
    'order_management',
    'sales_analytics',
    'commission_reports',
    'customer_insights',
    'tier_management',
    'billing_management',
    'profile_management',
  ];

  // Admin access permissions (full access)
  static const List<String> _adminAccess = [
    'admin_dashboard',
    'user_management',
    'vendor_management',
    'partner_management',
    'system_monitoring',
    'fraud_detection',
    'commission_management',
    'payout_processing',
    'analytics_full',
    'system_settings',
    'content_management',
    'plant_database_management',
  ];

  // Customer allowed routes
  static const List<String> _customerRoutes = [
    '/',
    '/home',
    '/products',
    '/product-details',
    '/cart',
    '/checkout',
    '/orders',
    '/scanner',
    '/ai-assistant',
    '/encyclopedia',
    '/profile',
    '/wishlist',
    '/reviews',
  ];

  // Partner allowed routes
  static const List<String> _partnerRoutes = [
    '/partner-dashboard',
    '/partner-analytics',
    '/partner-links',
    '/partner-materials',
    '/partner-payouts',
    '/partner-training',
    '/partner-profile',
  ];

  // Vendor allowed routes
  static const List<String> _vendorRoutes = [
    '/vendor-dashboard',
    '/vendor-products',
    '/vendor-inventory',
    '/vendor-orders',
    '/vendor-analytics',
    '/vendor-commissions',
    '/vendor-customers',
    '/vendor-billing',
    '/vendor-profile',
  ];

  List<String> _getAllFeatures() {
    return [
      ..._customerAccess,
      ..._partnerAccess,
      ..._vendorAccess,
      ..._adminAccess,
    ];
  }

  List<String> _getAccessList(UserRole role) {
    switch (role) {
      case UserRole.customer:
        return _customerAccess;
      case UserRole.partner:
        return _partnerAccess;
      case UserRole.vendor:
        return _vendorAccess;
      case UserRole.admin:
        return _adminAccess;
    }
  }
}

// VendorTier enum moved to user_models.dart to avoid conflicts
