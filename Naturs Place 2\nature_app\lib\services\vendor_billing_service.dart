import 'package:flutter/foundation.dart';
import '../models/user_models.dart';
import 'postgres_service.dart';
import 'stripe_integration_service.dart';

/// Service for managing vendor billing and tier fees
class VendorBillingService {
  static final VendorBillingService _instance = VendorBillingService._internal();
  factory VendorBillingService() => _instance;
  VendorBillingService._internal();

  final PostgreSQLService _postgresService = PostgreSQLService();
  final StripeIntegrationService _stripeService = StripeIntegrationService();

  /// Initialize billing service
  Future<void> initialize() async {
    await _postgresService.initialize();
    await _stripeService.initialize();
    debugPrint('✅ Vendor Billing Service initialized');
  }

  /// Process monthly tier fee for vendor
  Future<BillingResult> processMonthlyFee(String vendorId) async {
    try {
      final vendor = await _getVendorById(vendorId);
      if (vendor == null) {
        return BillingResult(
          success: false,
          error: 'Vendor not found',
        );
      }

      // Check if payment is due
      if (!_isPaymentDue(vendor)) {
        return BillingResult(
          success: true,
          message: 'Payment not due yet',
          nextPaymentDate: vendor.nextPaymentDate,
        );
      }

      // Process payment via Stripe
      final paymentResult = await _processStripePayment(vendor);
      
      if (paymentResult.success) {
        // Update vendor payment status
        await _updateVendorPaymentStatus(
          vendorId,
          paymentSuccessful: true,
          transactionId: paymentResult.transactionId,
        );

        return BillingResult(
          success: true,
          message: 'Payment processed successfully',
          amountCharged: vendor.monthlyFee,
          transactionId: paymentResult.transactionId,
          nextPaymentDate: _calculateNextPaymentDate(),
        );
      } else {
        // Handle payment failure
        await _handlePaymentFailure(vendorId, paymentResult.error ?? 'Payment failed');
        
        return BillingResult(
          success: false,
          error: paymentResult.error ?? 'Payment processing failed',
          amountDue: vendor.monthlyFee,
        );
      }
    } catch (e) {
      debugPrint('❌ Failed to process monthly fee: $e');
      return BillingResult(
        success: false,
        error: 'Billing system error',
      );
    }
  }

  /// Upgrade vendor tier
  Future<BillingResult> upgradeVendorTier(String vendorId, VendorTier newTier) async {
    try {
      final vendor = await _getVendorById(vendorId);
      if (vendor == null) {
        return BillingResult(
          success: false,
          error: 'Vendor not found',
        );
      }

      if (newTier.index <= vendor.tier.index) {
        return BillingResult(
          success: false,
          error: 'Can only upgrade to higher tier',
        );
      }

      // Calculate prorated amount
      final proratedAmount = _calculateProratedUpgrade(vendor, newTier);
      
      if (proratedAmount > 0) {
        // Process prorated payment
        final paymentResult = await _processStripePayment(vendor, amount: proratedAmount);
        
        if (!paymentResult.success) {
          return BillingResult(
            success: false,
            error: 'Failed to process upgrade payment: ${paymentResult.error}',
            amountDue: proratedAmount,
          );
        }
      }

      // Update vendor tier
      await _updateVendorTier(vendorId, newTier);

      return BillingResult(
        success: true,
        message: 'Tier upgraded successfully',
        amountCharged: proratedAmount,
        tierUpgraded: newTier,
      );
    } catch (e) {
      debugPrint('❌ Failed to upgrade vendor tier: $e');
      return BillingResult(
        success: false,
        error: 'Tier upgrade failed',
      );
    }
  }

  /// Get vendor billing history
  Future<List<BillingRecord>> getVendorBillingHistory(String vendorId) async {
    try {
      // Query billing history from database
      // Mock implementation - replace with actual database query
      return [];
    } catch (e) {
      debugPrint('❌ Failed to get billing history: $e');
      return [];
    }
  }

  /// Get vendor current billing status
  Future<VendorBillingStatus> getVendorBillingStatus(String vendorId) async {
    try {
      final vendor = await _getVendorById(vendorId);
      if (vendor == null) {
        return VendorBillingStatus(
          vendorId: vendorId,
          isActive: false,
          error: 'Vendor not found',
        );
      }

      return VendorBillingStatus(
        vendorId: vendorId,
        isActive: vendor.status == UserStatus.active,
        currentTier: vendor.tier,
        monthlyFee: vendor.monthlyFee,
        paymentCurrent: vendor.paymentCurrent,
        lastPaymentDate: vendor.lastPaymentDate,
        nextPaymentDate: vendor.nextPaymentDate,
        daysUntilPayment: vendor.daysUntilPayment,
        isOverdue: vendor.isPaymentOverdue,
        tierBenefits: _getTierBenefits(vendor.tier),
      );
    } catch (e) {
      debugPrint('❌ Failed to get billing status: $e');
      return VendorBillingStatus(
        vendorId: vendorId,
        isActive: false,
        error: 'Failed to load billing status',
      );
    }
  }

  /// Process marketing service payment (separate from tier fees)
  Future<BillingResult> processMarketingPayment({
    required String vendorId,
    required String serviceId,
    required double amount,
    required String description,
  }) async {
    try {
      final vendor = await _getVendorById(vendorId);
      if (vendor == null) {
        return BillingResult(
          success: false,
          error: 'Vendor not found',
        );
      }

      // Process marketing payment via Stripe
      final paymentResult = await _processStripePayment(vendor, amount: amount);

      if (paymentResult.success) {
        // Record marketing expense
        await _recordMarketingExpense(
          vendorId: vendorId,
          serviceId: serviceId,
          amount: amount,
          description: description,
          transactionId: paymentResult.transactionId!,
        );

        return BillingResult(
          success: true,
          message: 'Marketing payment processed successfully',
          amountCharged: amount,
          transactionId: paymentResult.transactionId,
        );
      } else {
        return BillingResult(
          success: false,
          error: paymentResult.error ?? 'Marketing payment failed',
          amountDue: amount,
        );
      }
    } catch (e) {
      debugPrint('❌ Failed to process marketing payment: $e');
      return BillingResult(
        success: false,
        error: 'Marketing billing system error',
      );
    }
  }

  /// Get vendor's total marketing spending
  Future<double> getVendorMarketingSpending(String vendorId, {DateTime? startDate, DateTime? endDate}) async {
    try {
      // Mock implementation - replace with actual database query
      return 0.0;
    } catch (e) {
      debugPrint('❌ Failed to get marketing spending: $e');
      return 0.0;
    }
  }

  /// Process all due vendor payments (automated job)
  Future<void> processAllDuePayments() async {
    try {
      final dueVendors = await _getVendorsWithDuePayments();

      for (final vendorId in dueVendors) {
        await processMonthlyFee(vendorId);
        // Add delay to avoid rate limiting
        await Future.delayed(const Duration(seconds: 1));
      }

      debugPrint('✅ Processed ${dueVendors.length} due vendor payments');
    } catch (e) {
      debugPrint('❌ Failed to process due payments: $e');
    }
  }

  /// Suspend vendor for non-payment
  Future<void> suspendVendorForNonPayment(String vendorId) async {
    try {
      // Mock implementation - replace with actual database update
      debugPrint('Mock: Updating vendor status to suspended');
      debugPrint('⚠️ Vendor suspended for non-payment: $vendorId');
    } catch (e) {
      debugPrint('❌ Failed to suspend vendor: $e');
    }
  }

  // Private helper methods

  Future<VendorUser?> _getVendorById(String vendorId) async {
    // Mock implementation - replace with actual database query
    if (vendorId == 'vendor_001') {
      return VendorUser(
        id: vendorId,
        email: '<EMAIL>',
        firstName: 'Jane',
        lastName: 'Vendor',
        status: UserStatus.active,
        createdAt: DateTime.now().subtract(const Duration(days: 60)),
        businessName: 'Healing Herbs Co.',
        businessAddress: '123 Herb Street',
        taxId: 'TAX123456',
        tier: VendorTier.standard,
        monthlyFee: VendorTier.standard.monthlyFee,
        lastPaymentDate: DateTime.now().subtract(const Duration(days: 25)),
        nextPaymentDate: DateTime.now().add(const Duration(days: 5)),
        paymentCurrent: true,
        totalSales: 15000.0,
        totalCommissionOwed: 1800.0,
        productCount: 45,
      );
    }
    return null;
  }

  bool _isPaymentDue(VendorUser vendor) {
    if (vendor.nextPaymentDate == null) return true;
    return DateTime.now().isAfter(vendor.nextPaymentDate!) || !vendor.paymentCurrent;
  }

  Future<StripePaymentResult> _processStripePayment(VendorUser vendor, {double? amount}) async {
    final chargeAmount = amount ?? vendor.monthlyFee;
    
    // Mock Stripe payment - replace with actual Stripe integration
    return StripePaymentResult(
      success: true,
      transactionId: 'txn_${DateTime.now().millisecondsSinceEpoch}',
      amountCharged: chargeAmount,
    );
  }

  Future<void> _updateVendorPaymentStatus(
    String vendorId, {
    required bool paymentSuccessful,
    String? transactionId,
  }) async {
    try {
      // Mock implementation - replace with actual database update
      debugPrint('Mock: Updating payment status for vendor $vendorId: $paymentSuccessful');
    } catch (e) {
      debugPrint('❌ Failed to update payment status: $e');
    }
  }

  Future<void> _handlePaymentFailure(String vendorId, String reason) async {
    try {
      // Mock implementation - replace with actual database record
      debugPrint('Mock: Recording payment failure for vendor $vendorId: $reason');
      
      // Send notification to vendor
      debugPrint('💳 Payment failed for vendor $vendorId: $reason');
    } catch (e) {
      debugPrint('❌ Failed to handle payment failure: $e');
    }
  }

  DateTime _calculateNextPaymentDate() {
    return DateTime.now().add(const Duration(days: 30));
  }

  double _calculateProratedUpgrade(VendorUser vendor, VendorTier newTier) {
    final currentFee = vendor.tier.monthlyFee;
    final newFee = newTier.monthlyFee;
    final difference = newFee - currentFee;
    
    // Calculate prorated amount based on days remaining in current cycle
    final daysRemaining = vendor.daysUntilPayment;
    final proratedAmount = (difference * daysRemaining) / 30;
    
    return proratedAmount > 0 ? proratedAmount : 0;
  }

  Future<void> _updateVendorTier(String vendorId, VendorTier newTier) async {
    try {
      // Mock implementation - replace with actual database update
      debugPrint('Mock: Updating vendor tier for $vendorId to ${newTier.displayName}');
    } catch (e) {
      debugPrint('❌ Failed to update vendor tier: $e');
    }
  }

  Future<List<String>> _getVendorsWithDuePayments() async {
    try {
      // Mock implementation - replace with actual database query
      return [];
    } catch (e) {
      debugPrint('❌ Failed to get vendors with due payments: $e');
      return [];
    }
  }

  Map<String, dynamic> _getTierBenefits(VendorTier tier) {
    return {
      'max_products': tier.maxProducts,
      'commission_rate': tier.commissionRate,
      'analytics_access': tier.index >= VendorTier.standard.index,
      'priority_support': tier.index >= VendorTier.premium.index,
      'custom_branding': tier.index >= VendorTier.premium.index,
      'api_access': tier == VendorTier.enterprise,
    };
  }

  Future<void> _recordMarketingExpense({
    required String vendorId,
    required String serviceId,
    required double amount,
    required String description,
    required String transactionId,
  }) async {
    try {
      // Mock implementation - replace with actual database insertion
      debugPrint('✅ Marketing expense recorded: $vendorId - $serviceId - \$${amount.toStringAsFixed(2)}');
    } catch (e) {
      debugPrint('❌ Failed to record marketing expense: $e');
    }
  }
}

/// Billing operation result
class BillingResult {
  final bool success;
  final String? message;
  final String? error;
  final double? amountCharged;
  final double? amountDue;
  final String? transactionId;
  final DateTime? nextPaymentDate;
  final VendorTier? tierUpgraded;

  BillingResult({
    required this.success,
    this.message,
    this.error,
    this.amountCharged,
    this.amountDue,
    this.transactionId,
    this.nextPaymentDate,
    this.tierUpgraded,
  });
}

/// Stripe payment result
class StripePaymentResult {
  final bool success;
  final String? transactionId;
  final double? amountCharged;
  final String? error;

  StripePaymentResult({
    required this.success,
    this.transactionId,
    this.amountCharged,
    this.error,
  });
}

/// Vendor billing status
class VendorBillingStatus {
  final String vendorId;
  final bool isActive;
  final VendorTier? currentTier;
  final double? monthlyFee;
  final bool? paymentCurrent;
  final DateTime? lastPaymentDate;
  final DateTime? nextPaymentDate;
  final int? daysUntilPayment;
  final bool? isOverdue;
  final Map<String, dynamic>? tierBenefits;
  final String? error;

  VendorBillingStatus({
    required this.vendorId,
    required this.isActive,
    this.currentTier,
    this.monthlyFee,
    this.paymentCurrent,
    this.lastPaymentDate,
    this.nextPaymentDate,
    this.daysUntilPayment,
    this.isOverdue,
    this.tierBenefits,
    this.error,
  });
}

/// Billing record for history
class BillingRecord {
  final String id;
  final String vendorId;
  final DateTime date;
  final double amount;
  final String description;
  final String status;
  final String? transactionId;

  BillingRecord({
    required this.id,
    required this.vendorId,
    required this.date,
    required this.amount,
    required this.description,
    required this.status,
    this.transactionId,
  });
}
