

/// Vendor partnership types
enum VendorType {
  regular,
  affiliate,
  premium,
  exclusive
}

/// Vendor verification status
enum VerificationStatus {
  pending,
  verified,
  rejected,
  suspended,
  active
}

/// Vendor status (alias for VerificationStatus for backward compatibility)
typedef VendorStatus = VerificationStatus;

/// Commission tier levels
enum CommissionTier {
  tier1, // New: 5-8%
  tier2, // Regular: 8-12%
  tier3, // High Volume: 12-18%
  tier4  // VIP: 18-25%
}

/// Product categories with different commission rates
enum ProductCategory {
  supplements,
  essentialOils,
  herbalTeas,
  skincare,
  books,
  equipment,
  consultations,
  vitamins,
  herbs,
  minerals,
  probiotics,
  fitness,
  wellness
}

/// Vendor model for managing partnerships
class Vendor {
  final String id;
  final String name;
  final String email;
  final String phone;
  final String businessName;
  final String businessAddress;
  final String taxId;
  final VendorType type;
  final VerificationStatus status;
  final CommissionTier tier;
  final DateTime joinDate;
  final DateTime? verificationDate;
  final List<ProductCategory> categories;
  final Map<ProductCategory, double> commissionRates;
  final double monthlyListingFee;
  final bool isFeatured;
  final double totalSales;
  final double totalCommissionPaid;
  final int totalOrders;
  final double averageRating;
  final List<String> certifications;
  final String? logoUrl;
  final String? description;
  final Map<String, dynamic> metadata;

  const Vendor({
    required this.id,
    required this.name,
    required this.email,
    required this.phone,
    required this.businessName,
    required this.businessAddress,
    required this.taxId,
    required this.type,
    required this.status,
    required this.tier,
    required this.joinDate,
    this.verificationDate,
    required this.categories,
    required this.commissionRates,
    required this.monthlyListingFee,
    this.isFeatured = false,
    this.totalSales = 0.0,
    this.totalCommissionPaid = 0.0,
    this.totalOrders = 0,
    this.averageRating = 0.0,
    this.certifications = const [],
    this.logoUrl,
    this.description,
    this.metadata = const {},
  });

  /// Check if vendor qualifies for tier upgrade
  bool qualifiesForTierUpgrade() {
    switch (tier) {
      case CommissionTier.tier1:
        return totalSales >= 5000 && totalOrders >= 50;
      case CommissionTier.tier2:
        return totalSales >= 15000 && totalOrders >= 150;
      case CommissionTier.tier3:
        return totalSales >= 50000 && totalOrders >= 500;
      case CommissionTier.tier4:
        return false; // Already at highest tier
    }
  }

  /// Get next tier
  CommissionTier? getNextTier() {
    switch (tier) {
      case CommissionTier.tier1:
        return CommissionTier.tier2;
      case CommissionTier.tier2:
        return CommissionTier.tier3;
      case CommissionTier.tier3:
        return CommissionTier.tier4;
      case CommissionTier.tier4:
        return null;
    }
  }

  /// Calculate vendor commission
  double calculateCommission(ProductCategory category, double saleAmount) {
    final baseRate = commissionRates[category] ?? 0.0;
    return saleAmount * (baseRate / 100);
  }

  Vendor copyWith({
    String? id,
    String? name,
    String? email,
    String? phone,
    String? businessName,
    String? businessAddress,
    String? taxId,
    VendorType? type,
    VerificationStatus? status,
    CommissionTier? tier,
    DateTime? joinDate,
    DateTime? verificationDate,
    List<ProductCategory>? categories,
    Map<ProductCategory, double>? commissionRates,
    double? monthlyListingFee,
    bool? isFeatured,
    double? totalSales,
    double? totalCommissionPaid,
    int? totalOrders,
    double? averageRating,
    List<String>? certifications,
    String? logoUrl,
    String? description,
    Map<String, dynamic>? metadata,
  }) {
    return Vendor(
      id: id ?? this.id,
      name: name ?? this.name,
      email: email ?? this.email,
      phone: phone ?? this.phone,
      businessName: businessName ?? this.businessName,
      businessAddress: businessAddress ?? this.businessAddress,
      taxId: taxId ?? this.taxId,
      type: type ?? this.type,
      status: status ?? this.status,
      tier: tier ?? this.tier,
      joinDate: joinDate ?? this.joinDate,
      verificationDate: verificationDate ?? this.verificationDate,
      categories: categories ?? this.categories,
      commissionRates: commissionRates ?? this.commissionRates,
      monthlyListingFee: monthlyListingFee ?? this.monthlyListingFee,
      isFeatured: isFeatured ?? this.isFeatured,
      totalSales: totalSales ?? this.totalSales,
      totalCommissionPaid: totalCommissionPaid ?? this.totalCommissionPaid,
      totalOrders: totalOrders ?? this.totalOrders,
      averageRating: averageRating ?? this.averageRating,
      certifications: certifications ?? this.certifications,
      logoUrl: logoUrl ?? this.logoUrl,
      description: description ?? this.description,
      metadata: metadata ?? this.metadata,
    );
  }
}

/// Affiliate partner model
class AffiliatePartner {
  final String id;
  final String name;
  final String email;
  final String phone;
  final String website;
  final String socialMedia;
  final CommissionTier tier;
  final DateTime joinDate;
  final String trackingCode;
  final Map<ProductCategory, double> commissionRates;
  final double totalEarnings;
  final int totalReferrals;
  final int totalConversions;
  final double conversionRate;
  final DateTime? lastPayoutDate;
  final double pendingPayout;
  final bool isActive;
  final Map<String, dynamic> metadata;

  const AffiliatePartner({
    required this.id,
    required this.name,
    required this.email,
    required this.phone,
    required this.website,
    required this.socialMedia,
    required this.tier,
    required this.joinDate,
    required this.trackingCode,
    required this.commissionRates,
    this.totalEarnings = 0.0,
    this.totalReferrals = 0,
    this.totalConversions = 0,
    this.conversionRate = 0.0,
    this.lastPayoutDate,
    this.pendingPayout = 0.0,
    this.isActive = true,
    this.metadata = const {},
  });

  /// Calculate affiliate commission
  double calculateCommission(ProductCategory category, double saleAmount) {
    final baseRate = commissionRates[category] ?? 0.0;
    double multiplier = 1.0;

    // Tier-based multipliers
    switch (tier) {
      case CommissionTier.tier1:
        multiplier = 1.0;
        break;
      case CommissionTier.tier2:
        multiplier = 1.15;
        break;
      case CommissionTier.tier3:
        multiplier = 1.3;
        break;
      case CommissionTier.tier4:
        multiplier = 1.5;
        break;
    }

    // Performance bonus for high conversion rates
    if (conversionRate > 0.05) {
      multiplier += 0.1;
    }

    return saleAmount * (baseRate / 100) * multiplier;
  }

  /// Check if affiliate qualifies for tier upgrade
  bool qualifiesForTierUpgrade() {
    switch (tier) {
      case CommissionTier.tier1:
        return totalEarnings >= 1000 && totalConversions >= 25;
      case CommissionTier.tier2:
        return totalEarnings >= 5000 && totalConversions >= 100;
      case CommissionTier.tier3:
        return totalEarnings >= 15000 && totalConversions >= 300;
      case CommissionTier.tier4:
        return false;
    }
  }

  AffiliatePartner copyWith({
    String? id,
    String? name,
    String? email,
    String? phone,
    String? website,
    String? socialMedia,
    CommissionTier? tier,
    DateTime? joinDate,
    String? trackingCode,
    Map<ProductCategory, double>? commissionRates,
    double? totalEarnings,
    int? totalReferrals,
    int? totalConversions,
    double? conversionRate,
    DateTime? lastPayoutDate,
    double? pendingPayout,
    bool? isActive,
    Map<String, dynamic>? metadata,
  }) {
    return AffiliatePartner(
      id: id ?? this.id,
      name: name ?? this.name,
      email: email ?? this.email,
      phone: phone ?? this.phone,
      website: website ?? this.website,
      socialMedia: socialMedia ?? this.socialMedia,
      tier: tier ?? this.tier,
      joinDate: joinDate ?? this.joinDate,
      trackingCode: trackingCode ?? this.trackingCode,
      commissionRates: commissionRates ?? this.commissionRates,
      totalEarnings: totalEarnings ?? this.totalEarnings,
      totalReferrals: totalReferrals ?? this.totalReferrals,
      totalConversions: totalConversions ?? this.totalConversions,
      conversionRate: conversionRate ?? this.conversionRate,
      lastPayoutDate: lastPayoutDate ?? this.lastPayoutDate,
      pendingPayout: pendingPayout ?? this.pendingPayout,
      isActive: isActive ?? this.isActive,
      metadata: metadata ?? this.metadata,
    );
  }
}
