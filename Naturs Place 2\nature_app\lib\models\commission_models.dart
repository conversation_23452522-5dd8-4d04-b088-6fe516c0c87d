import 'vendor_models.dart';

/// Transaction status
enum TransactionStatus {
  pending,
  completed,
  cancelled,
  refunded,
  disputed
}

/// Commission status
enum CommissionStatus {
  pending,
  approved,
  paid,
  disputed,
  cancelled
}

/// Payout status
enum PayoutStatus {
  pending,
  processing,
  completed,
  failed,
  cancelled
}

/// Transaction model for tracking sales
class Transaction {
  final String id;
  final String orderId;
  final String customerId;
  final String vendorId;
  final String? affiliateId;
  final String productId;
  final String productName;
  final ProductCategory category;
  final double productPrice;
  final int quantity;
  final double subtotal;
  final double tax;
  final double shipping;
  final double total;
  final double discount;
  final String? couponCode;
  final TransactionStatus status;
  final DateTime createdAt;
  final DateTime? completedAt;
  final DateTime? cancelledAt;
  final String? cancellationReason;
  final Map<String, dynamic> metadata;

  const Transaction({
    required this.id,
    required this.orderId,
    required this.customerId,
    required this.vendorId,
    this.affiliateId,
    required this.productId,
    required this.productName,
    required this.category,
    required this.productPrice,
    required this.quantity,
    required this.subtotal,
    required this.tax,
    required this.shipping,
    required this.total,
    this.discount = 0.0,
    this.couponCode,
    required this.status,
    required this.createdAt,
    this.completedAt,
    this.cancelledAt,
    this.cancellationReason,
    required this.metadata,
  });

  /// Calculate net amount for commission calculation
  double get netAmount => total - tax - shipping;

  /// Check if transaction is eligible for commission
  bool get isEligibleForCommission {
    return status == TransactionStatus.completed && completedAt != null;
  }

  Transaction copyWith({
    String? id,
    String? orderId,
    String? customerId,
    String? vendorId,
    String? affiliateId,
    String? productId,
    String? productName,
    ProductCategory? category,
    double? productPrice,
    int? quantity,
    double? subtotal,
    double? tax,
    double? shipping,
    double? total,
    double? discount,
    String? couponCode,
    TransactionStatus? status,
    DateTime? createdAt,
    DateTime? completedAt,
    DateTime? cancelledAt,
    String? cancellationReason,
    Map<String, dynamic> metadata,
  }) {
    return Transaction(
      id: id ?? this.id,
      orderId: orderId ?? this.orderId,
      customerId: customerId ?? this.customerId,
      vendorId: vendorId ?? this.vendorId,
      affiliateId: affiliateId ?? this.affiliateId,
      productId: productId ?? this.productId,
      productName: productName ?? this.productName,
      category: category ?? this.category,
      productPrice: productPrice ?? (this.productPrice),
      quantity: quantity ?? this.quantity,
      subtotal: subtotal ?? this.subtotal,
      tax: tax ?? this.tax,
      shipping: shipping ?? this.shipping,
      total: total ?? this.total,
      discount: discount ?? this.discount,
      couponCode: couponCode ?? this.couponCode,
      status: status ?? this.status,
      createdAt: createdAt ?? this.createdAt,
      completedAt: completedAt ?? this.completedAt,
      cancelledAt: cancelledAt ?? this.cancelledAt,
      cancellationReason: cancellationReason ?? this.cancellationReason,
      metadata: metadata ?? this.metadata,
    );
  }
}

/// Commission record for vendors and affiliates
class Commission {
  final String id;
  final String transactionId;
  final String partnerId; // vendor or affiliate ID
  final String partnerType; // 'vendor' or 'affiliate'
  final ProductCategory category;
  final double saleAmount;
  final double commissionRate;
  final double commissionAmount;
  final double bonusAmount;
  final double totalAmount;
  final double appRevenue;
  final CommissionStatus status;
  final DateTime createdAt;
  final DateTime? approvedAt;
  final DateTime? paidAt;
  final String? payoutId;
  final String? notes;
  final required Map<String, dynamic> metadata;

  const Commission({
    required this.id,
    required this.metadata,
    required this.transactionId,
    required this.partnerId,
    required this.partnerType,
    required this.category,
    required this.saleAmount,
    required this.commissionRate,
    required this.commissionAmount,
    this.bonusAmount = 0.0,
    required this.totalAmount,
    required this.appRevenue,
    required this.status,
    required this.createdAt,
    this.approvedAt,
    this.paidAt,
    this.payoutId,
    this.notes,
  });

  /// Check if commission is ready for payout
  bool get isReadyForPayout {
    return status == CommissionStatus.approved && paidAt == null;
  }

  /// Calculate days since creation
  int get daysSinceCreation {
    return DateTime.now().difference(createdAt).inDays;
  }

  Commission copyWith({
    String? id,
    String? transactionId,
    String? partnerId,
    String? partnerType,
    ProductCategory? category,
    double? saleAmount,
    double? commissionRate,
    double? commissionAmount,
    double? bonusAmount,
    double? totalAmount,
    double? appRevenue,
    CommissionStatus? status,
    DateTime? createdAt,
    DateTime? approvedAt,
    DateTime? paidAt,
    String? payoutId,
    String? notes,
    Map<String, dynamic>? metadata,
  }) {
    return Commission(
      id: id ?? this.id,
      transactionId: transactionId ?? this.transactionId,
      partnerId: partnerId ?? this.partnerId,
      partnerType: partnerType ?? this.partnerType,
      category: category ?? this.category,
      saleAmount: saleAmount ?? this.saleAmount,
      commissionRate: commissionRate ?? this.commissionRate,
      commissionAmount: commissionAmount ?? this.commissionAmount,
      bonusAmount: bonusAmount ?? this.bonusAmount,
      totalAmount: totalAmount ?? this.totalAmount,
      appRevenue: appRevenue ?? this.appRevenue,
      status: status ?? this.status,
      createdAt: createdAt ?? this.createdAt,
      approvedAt: approvedAt ?? this.approvedAt,
      paidAt: paidAt ?? this.paidAt,
      payoutId: payoutId ?? this.payoutId,
      notes: notes ?? this.notes,
      metadata: metadata ?? const {},
    );
  }
}

/// Payout record for batch payments
class Payout {
  final String id;
  final String partnerId;
  final String partnerType;
  final List<String> commissionIds;
  final double totalAmount;
  final double fees;
  final double netAmount;
  final PayoutStatus status;
  final String paymentMethod; // 'paypal', 'bank_transfer', 'stripe'
  final String? paymentReference;
  final DateTime createdAt;
  final DateTime? processedAt;
  final DateTime? completedAt;
  final String? failureReason;
  final Map<String, dynamic> paymentDetails;
  final Map<String, dynamic> metadata;

  const Payout({
    required this.id,
    required this.partnerId,
    required this.partnerType,
    required this.commissionIds,
    required this.totalAmount,
    this.fees = 0.0,
    required this.netAmount,
    required this.status,
    required this.paymentMethod,
    this.paymentReference,
    required this.createdAt,
    this.processedAt,
    this.completedAt,
    this.failureReason,
    required this.paymentDetails,
    required this.metadata,
  });

  /// Calculate processing time in hours
  int? get processingTimeHours {
    if (processedAt == null) return null;
    return processedAt?.difference(createdAt).inHours;
  }

  /// Check if payout is overdue
  bool get isOverdue {
    if (status == PayoutStatus.completed) return false;
    return DateTime.now().difference(createdAt).inDays > 7;
  }

  Payout copyWith({
    String? id,
    String? partnerId,
    String? partnerType,
    List<String>? commissionIds,
    double? totalAmount,
    double? fees,
    double? netAmount,
    PayoutStatus? status,
    String? paymentMethod,
    String? paymentReference,
    DateTime? createdAt,
    DateTime? processedAt,
    DateTime? completedAt,
    String? failureReason,
    Map<String, dynamic> paymentDetails,
    Map<String, dynamic> metadata,
  }) {
    return Payout(
      id: id ?? this.id,
      partnerId: partnerId ?? this.partnerId,
      partnerType: partnerType ?? this.partnerType,
      commissionIds: commissionIds ?? this.commissionIds,
      totalAmount: totalAmount ?? this.totalAmount,
      fees: fees ?? this.fees,
      netAmount: netAmount ?? this.netAmount,
      status: status ?? this.status,
      paymentMethod: paymentMethod ?? this.paymentMethod,
      paymentReference: paymentReference ?? this.paymentReference,
      createdAt: createdAt ?? this.createdAt,
      processedAt: processedAt ?? this.processedAt,
      completedAt: completedAt ?? this.completedAt,
      failureReason: failureReason ?? this.failureReason,
      paymentDetails: paymentDetails ?? this.paymentDetails,
      metadata: metadata ?? this.metadata,
    );
  }
}

</file_content>

IMPORTANT: For any future changes to this file, use the final_file_content shown above as your reference. This content reflects the current state of the file, including any auto-formatting (e.g., if you used single quotes but the formatter converted them to double quotes). Always base your SEARCH/REPLACE operations on this final version to ensure accuracy.

New problems detected after saving the file:
lib/services/commission_service.dart
- [dart Error] Line 86: The named parameter 'metadata' isn't defined.
Try correcting the name to an existing named parameter's name, or defining a named parameter with the name 'metadata'.
- [dart Error] Line 139: The named parameter 'metadata' isn't defined.
Try correcting the name to an existing named parameter's name, or defining a named parameter with the name 'metadata'.
- [dart Error] Line 224: The named parameter 'paymentDetails' isn't defined.
Try correcting the name to an existing named parameter's name, or defining a named parameter with the name 'paymentDetails'.
- [dart Error] Line 225: The named parameter 'metadata' isn't defined.
Try correcting the name to an existing named parameter's name, or defining a named parameter with the name 'metadata'.

lib/models/commission_models.dart
- [dart Error] Line 160: The modifier 'required' should be before the modifier 'final'.
Try re-ordering the modifiers.
- [dart Error] Line 160: Can't have modifier 'required' here.
Try removing 'required'.
- [dart Error] Line 252: The modifier 'required' should be before the modifier 'final'.
Try re-ordering the modifiers.
- [dart Error] Line 252: Can't have modifier 'required' here.
Try removing 'required'.
- [dart Error] Line 253: The modifier 'required' should be before the modifier 'final'.
Try re-ordering the modifiers.
- [dart Error] Line 253: Can't have modifier 'required' here.
Try removing 'required'.
- [dart Error] Line 324: The final variable 'failureReason' must be initialized.
Try initializing the variable.
- [dart Error] Line 325: The final variable 'paymentDetails' must be initialized.
Try initializing the variable.
- [dart Error] Line 326: The final variable 'metadata' must be initialized.
Try initializing the variable.
- [dart Error] Line 328: Can't have modifier 'const' here.
Try removing 'const'.
- [dart Error] Line 345: A function body must be provided.
Try adding a function body.
- [dart Error] Line 396: Expected a method, getter, setter or operator declaration.
This appears to be incomplete code. Try removing it or completing it.
- [dart Error] Line 397: Can't have modifier 'required' here.
Try removing 'required'.
- [dart Error] Line 397: 'this' can't be used as an identifier because it's a keyword.
Try renaming this to be an identifier that isn't a keyword.
- [dart Error] Line 397: Functions must have an explicit list of parameters.
Try adding a parameter list.
- [dart Error] Line 397: A function body must be provided.
Try adding a function body.
- [dart Error] Line 397: Expected a method, getter, setter or operator declaration.
This appears to be incomplete code. Try removing it or completing it.
- [dart Error] Line 397: Variables must be declared using the keywords 'const', 'final', 'var' or a type name.
Try adding the name of the type of the variable or the keyword 'var'.
- [dart Error] Line 398: Expected to find ';'.
- [dart Error] Line 398: 'this' can't be used as an identifier because it's a keyword.
Try renaming this to be an identifier that isn't a keyword.
- [dart Error] Line 398: Functions must have an explicit list of parameters.
Try adding a parameter list.
- [dart Error] Line 398: A function body must be provided.
Try adding a function body.
- [dart Error] Line 398: Expected a method, getter, setter or operator declaration.
This appears to be incomplete code. Try removing it or completing it.
- [dart Error] Line 398: Variables must be declared using the keywords 'const', 'final', 'var' or a type name.
Try adding the name of the type of the variable or the keyword 'var'.
- [dart Error] Line 399: Expected to find ';'.
- [dart Error] Line 399: 'this' can't be used as an identifier because it's a keyword.
Try renaming this to be an identifier that isn't a keyword.
- [dart Error] Line 399: Functions must have an explicit list of parameters.
Try adding a parameter list.
- [dart Error] Line 399: A function body must be provided.
Try adding a function body.
- [dart Error] Line 399: Expected a method, getter, setter or operator declaration.
This appears to be incomplete code. Try removing it or completing it.
- [dart Error] Line 399: Variables must be declared using the keywords 'const', 'final', 'var' or a type name.
Try adding the name of the type of the variable or the keyword 'var'.
- [dart Error] Line 400: Expected to find ';'.
- [dart Error] Line 400: 'this' can't be used as an identifier because it's a keyword.
Try renaming this to be an identifier that isn't a keyword.
- [dart Error] Line 400: Functions must have an explicit list of parameters.
Try adding a parameter list.
- [dart Error] Line 400: A function body must be provided.
Try adding a function body.
- [dart Error] Line 400: Expected a method, getter, setter or operator declaration.
This appears to be incomplete code. Try removing it or completing it.
- [dart Error] Line 400: Variables must be declared using the keywords 'const', 'final', 'var' or a type name.
Try adding the name of the type of the variable or the keyword 'var'.
- [dart Error] Line 401: 'this' can't be used as an identifier because it's a keyword.
Try renaming this to be an identifier that isn't a keyword.
- [dart Error] Line 401: Expected to find ';'.
- [dart Error] Line 401: Expected a method, getter, setter or operator declaration.
This appears to be incomplete code. Try removing it or completing it.
- [dart Error] Line 401: Variables must be declared using the keywords 'const', 'final', 'var' or a type name.
Try adding the name of the type of the variable or the keyword 'var'.
- [dart Error] Line 402: Expected to find ';'.
- [dart Error] Line 402: 'this' can't be used as an identifier because it's a keyword.
Try renaming this to be an identifier that isn't a keyword.
- [dart Error] Line 402: Functions must have an explicit list of parameters.
Try adding a parameter list.
- [dart Error] Line 402: A function body must be provided.
Try adding a function body.
- [dart Error] Line 402: Expected a method, getter, setter or operator declaration.
This appears to be incomplete code. Try removing it or completing it.
- [dart Error] Line 402: Variables must be declared using the keywords 'const', 'final', 'var' or a type name.
Try adding the name of the type of the variable or the keyword 'var'.
- [dart Error] Line 403: Expected to find ';'.
- [dart Error] Line 403: 'this' can't be used as an identifier because it's a keyword.
Try renaming this to be an identifier that isn't a keyword.
- [dart Error] Line 403: Functions must have an explicit list of parameters.
Try adding a parameter list.
- [dart Error] Line 403: A function body must be provided.
Try adding a function body.
- [dart Error] Line 403: Expected a method, getter, setter or operator declaration.
This appears to be incomplete code. Try removing it or completing it.
- [dart Error] Line 403: Variables must be declared using the keywords 'const', 'final', 'var' or a type name.
Try adding the name of the type of the variable or the keyword 'var'.
- [dart Error] Line 404: Expected to find ';'.
- [dart Error] Line 404: 'this' can't be used as an identifier because it's a keyword.
Try renaming this to be an identifier that isn't a keyword.
- [dart Error] Line 404: Functions must have an explicit list of parameters.
Try adding a parameter list.
- [dart Error] Line 404: A function body must be provided.
Try adding a function body.
- [dart Error] Line 404: Expected a method, getter, setter or operator declaration.
This appears to be incomplete code. Try removing it or completing it.
- [dart Error] Line 404: Variables must be declared using the keywords 'const', 'final', 'var' or a type name.
Try adding the name of the type of the variable or the keyword 'var'.
- [dart Error] Line 405: 'this' can't be used as an identifier because it's a keyword.
Try renaming this to be an identifier that isn't a keyword.
- [dart Error] Line 405: Expected to find ';'.
- [dart Error] Line 405: Expected a method, getter, setter or operator declaration.
This appears to be incomplete code. Try removing it or completing it.
- [dart Error] Line 405: Variables must be declared using the keywords 'const', 'final', 'var' or a type name.
Try adding the name of the type of the variable or the keyword 'var'.
- [dart Error] Line 406: Expected to find ';'.
- [dart Error] Line 406: 'this' can't be used as an identifier because it's a keyword.
Try renaming this to be an identifier that isn't a keyword.
- [dart Error] Line 406: Functions must have an explicit list of parameters.
Try adding a parameter list.
- [dart Error] Line 406: A function body must be provided.
Try adding a function body.
- [dart Error] Line 406: Expected a method, getter, setter or operator declaration.
This appears to be incomplete code. Try removing it or completing it.
- [dart Error] Line 406: Variables must be declared using the keywords 'const', 'final', 'var' or a type name.
Try adding the name of the type of the variable or the keyword 'var'.
- [dart Error] Line 407: 'this' can't be used as an identifier because it's a keyword.
Try renaming this to be an identifier that isn't a keyword.
- [dart Error] Line 407: Expected to find ';'.
- [dart Error] Line 407: Expected a method, getter, setter or operator declaration.
This appears to be incomplete code. Try removing it or completing it.
- [dart Error] Line 407: Variables must be declared using the keywords 'const', 'final', 'var' or a type name.
Try adding the name of the type of the variable or the keyword 'var'.
- [dart Error] Line 408: 'this' can't be used as an identifier because it's a keyword.
Try renaming this to be an identifier that isn't a keyword.
- [dart Error] Line 408: Expected to find ';'.
- [dart Error] Line 408: Expected a method, getter, setter or operator declaration.
This appears to be incomplete code. Try removing it or completing it.
- [dart Error] Line 408: Variables must be declared using the keywords 'const', 'final', 'var' or a type name.
Try adding the name of the type of the variable or the keyword 'var'.
- [dart Error] Line 409: 'this' can't be used as an identifier because it's a keyword.
Try renaming this to be an identifier that isn't a keyword.
- [dart Error] Line 409: Expected to find ';'.
- [dart Error] Line 409: Expected a method, getter, setter or operator declaration.
This appears to be incomplete code. Try removing it or completing it.
- [dart Error] Line 409: Variables must be declared using the keywords 'const', 'final', 'var' or a type name.
Try adding the name of the type of the variable or the keyword 'var'.
- [dart Error] Line 410: Expected an identifier.
- [dart Error] Line 410: Expected to find ';'.
- [dart Error] Line 410: Expected a method, getter, setter or operator declaration.
This appears to be incomplete code. Try removing it or completing it.
- [dart Error] Line 410: Unexpected text ';'.
Try removing the text.
- [dart Error] Line 461: Expected a method, getter, setter or operator declaration.
This appears to be incomplete code. Try removing it or completing it.
- [dart Error] Line 286: Payout isn't a type.
Try correcting the name to match an existing type.
- [dart Error] Line 359: Payout isn't a type.
Try correcting the name to match an existing type.
- [dart Error] Line 424: Payout isn't a type.
Try correcting the name to match an existing type.
- [dart Error] Line 328: The name 'Payout' is already defined.
Try renaming one of the declarations.
- [dart Error] Line 398: The name 'this' is already defined.
Try renaming one of the declarations.
- [dart Error] Line 399: The name 'required' is already defined.
Try renaming one of the declarations.
- [dart Error] Line 399: The name 'this' is already defined.
Try renaming one of the declarations.
- [dart Error] Line 400: The name 'required' is already defined.
Try renaming one of the declarations.
- [dart Error] Line 400: The name 'this' is already defined.
Try renaming one of the declarations.
- [dart Error] Line 401: The name 'this' is already defined.
Try renaming one of the declarations.
- [dart Error] Line 402: The name 'required' is already defined.
Try renaming one of the declarations.
- [dart Error] Line 402: The name 'this' is already defined.
Try renaming one of the declarations.
- [dart Error] Line 403: The name 'required' is already defined.
Try renaming one of the declarations.
- [dart Error] Line 403: The name 'this' is already defined.
Try renaming one of the declarations.
- [dart Error] Line 404: The name 'required' is already defined.
Try renaming one of the declarations.
- [dart Error] Line 404: The name 'this' is already defined.
Try renaming one of the declarations.
- [dart Error] Line 405: The name 'this' is already defined.
Try renaming one of the declarations.
- [dart Error] Line 406: The name 'required' is already defined.
Try renaming one of the declarations.
- [dart Error] Line 406: The name 'this' is already defined.
Try renaming one of the declarations.
- [dart Error] Line 407: The name 'this' is already defined.
Try renaming one of the declarations.
- [dart Error] Line 408: The name 'this' is already defined.
Try renaming one of the declarations.
- [dart Error] Line 409: The name 'this' is already defined.
Try renaming one of the declarations.
- [dart Error] Line 409: The name 'failureReason' is already defined.
Try renaming one of the declarations.
- [dart Error] Line 413: The name 'processingTimeHours' is already defined.
Try renaming one of the declarations.
- [dart Error] Line 419: The name 'isOverdue' is already defined.
Try renaming one of the declarations.
- [dart Error] Line 424: The name 'copyWith' is already defined.
Try renaming one of the declarations.
- [dart Error] Line 58: The field 'metadata' can't be initialized by multiple parameters in the same constructor.
Try removing one of the parameters, or using different fields.
- [dart Error] Line 79: The field 'metadata' can't be initialized by multiple parameters in the same constructor.
Try removing one of the parameters, or using different fields.
- [dart Error] Line 112: The parameter 'metadata' can't have a value of 'null' because of its type, but the implicit default value is 'null'.
Try adding either an explicit non-'null' default value or the 'required' modifier.
- [dart Error] Line 301: The parameter 'paymentDetails' can't have a value of 'null' because of its type, but the implicit default value is 'null'.
Try adding either an explicit non-'null' default value or the 'required' modifier.
- [dart Error] Line 302: The parameter 'metadata' can't have a value of 'null' because of its type, but the implicit default value is 'null'.
Try adding either an explicit non-'null' default value or the 'required' modifier.

lib/services/marketplace_integration_service.dart
- [dart Error] Line 65: The named parameter 'metadata' is required, but there's no corresponding argument.
Try adding the required argument.
- [dart Error] Line 23: The name 'debugPrint' is defined in the libraries 'package:flutter/src/foundation/print.dart (via package:flutter/foundation.dart)' and 'package:nature_app/services/commission_service.dart'.
Try using 'as prefix' for one of the import directives, or hiding the name from all but one of the imports.
- [dart Error] Line 92: The name 'debugPrint' is defined in the libraries 'package:flutter/src/foundation/print.dart (via package:flutter/foundation.dart)' and 'package:nature_app/services/commission_service.dart'.
Try using 'as prefix' for one of the import directives, or hiding the name from all but one of the imports.
- [dart Error] Line 96: The name 'debugPrint' is defined in the libraries 'package:flutter/src/foundation/print.dart (via package:flutter/foundation.dart)' and 'package:nature_app/services/commission_service.dart'.
Try using 'as prefix' for one of the import directives, or hiding the name from all but one of the imports.
- [dart Error] Line 302: The name 'debugPrint' is defined in the libraries 'package:flutter/src/foundation/print.dart (via package:flutter/foundation.dart)' and 'package:nature_app/services/commission_service.dart'.
Try using 'as prefix' for one of the import directives, or hiding the name from all but one of the imports.

lib/screens/vendor_dashboard_screen.dart
- [dart Error] Line 82: The method 'getCommissionAnalytics' isn't defined for the type 'CommissionService'.
Try correcting the name to the name of an existing method, or defining a method named 'getCommissionAnalytics'.
- [dart Error] Line 460: The method 'getCommissionAnalytics' isn't defined for the type 'CommissionService'.
Try correcting the name to the name of an existing method, or defining a method named 'getCommissionAnalytics'.<environment_details>
# VSCode Visible Files
lib/services/commission_service.dart

# VSCode Open Tabs
../../.gitignore
android/app/src/main/AndroidManifest.xml
lib/screens/home_screen.dart
run_app.bat
lib/main.dart
lib/screens/tools/ingredient_verification_tool_screen.dart
lib/screens/tools/content_optimization_tool_screen.dart
lib/screens/tools/marketing_automation_tool_screen.dart
lib/screens/tools/price_comparison_tool_screen.dart
README.md
c:/Users/<USER>/OneDrive/Documents/Cline/MCP/commission-server/src/index.ts
c:/Users/<USER>/AppData/Roaming/Code/User/globalStorage/saoudrizwan.claude-dev/settings/cline_mcp_settings.json
c:/Users/<USER>/OneDrive/Documents/Cline/MCP/commission-server/tsconfig.json
c:/Users/<USER>/OneDrive/Documents/Cline/MCP/commission-server/lib/routes/commission_routes.dart
mcp_servers/commission-server/pubspec.yaml
mcp_servers/commission-server/lib/controllers/commission_controller.dart
mcp_servers/commission-server/test/commission_test.dart
mcp_servers/commission-server/lib/services/commission_service.dart
mcp_servers/commission-server/lib/models/commission_models.dart
.env
mcp_servers/commission-server/.env
mcp_servers/commission-server/lib/services/database_service.dart
lib/models/vendor_models.dart
lib/models/commission_models.dart
lib/services/commission_service.dart
mcp_servers/commission-server/lib/main.dart
c:/Users/<USER>/AppData/Roaming/Code/User/settings.json
lib/screens/marketing_opportunities_screen.dart
lib/widgets/enhanced_product_card.dart
lib/screens/subscription_screen.dart
lib/services/affiliate_marketplace_service.dart
lib/widgets/advanced_search_widget.dart
lib/widgets/optimized_image.dart
lib/screens/encyclopedia_screen.dart
lib/screens/plant_detail_screen.dart
lib/screens/brand_dashboard_screen.dart
lib/screens/admin_control_panel.dart
c:/Users/<USER>/AppData/Roaming/Code/User/workspaceStorage/6e9ff8f0a1d7b734129d381c22fdbe42/Augment.vscode-augment/Augment-Memories

# Recently Modified Files
These files have been modified since you last accessed them (file was just edited so you may need to re-read it before editing):
lib/services/commission_service.dart

# Current Time
7/13/2025, 10:44:14 AM (America/New_York, UTC-4:00)

# Context Window Usage
363,519 / 1,048.576K tokens used (35%)

# Current Mode
ACT MODE
  final String? failureReason;
  final Map<String, dynamic> paymentDetails;
  final Map<String, dynamic> metadata;

  const Payout({
    required this.id,
    required this.partnerId,
    required this.partnerType,
    required this.commissionIds,
    required this.totalAmount,
    this.fees = 0.0,
    required this.netAmount,
    required this.status,
    required this.paymentMethod,
    this.paymentReference,
    required this.createdAt,
    this.processedAt,
    this.completedAt,
    this.failureReason,
    required this.paymentDetails,
    required this.metadata,
  });

  /// Calculate processing time in hours
  int? get processingTimeHours {
    if (processedAt == null) return null;
    return processedAt?.difference(createdAt).inHours;
  }

  /// Check if payout is overdue
  bool get isOverdue {
    if (status == PayoutStatus.completed) return false;
    return DateTime.now().difference(createdAt).inDays > 7;
  }

  Payout copyWith({
    String? id,
    String? partnerId,
    String? partnerType,
    List<String>? commissionIds,
    double? totalAmount,
    double? fees,
    double? netAmount,
    PayoutStatus? status,
    String? paymentMethod,
    String? paymentReference,
    DateTime? createdAt,
    DateTime? processedAt,
    DateTime? completedAt,
    String? failureReason,
    Map<String, dynamic> paymentDetails,
    Map<String, dynamic> metadata,
  }) {
    return Payout(
      id: id ?? this.id,
      partnerId: partnerId ?? this.partnerId,
      partnerType: partnerType ?? this.partnerType,
      commissionIds: commissionIds ?? this.commissionIds,
      totalAmount: totalAmount ?? this.totalAmount,
      fees: fees ?? this.fees,
      netAmount: netAmount ?? this.netAmount,
      status: status ?? this.status,
      paymentMethod: paymentMethod ?? this.paymentMethod,
      paymentReference: paymentReference ?? this.paymentReference,
      createdAt: createdAt ?? this.createdAt,
      processedAt: processedAt ?? this.processedAt,
      completedAt: completedAt ?? this.completedAt,
      failureReason: failureReason ?? this.failureReason,
      paymentDetails: paymentDetails ?? this.paymentDetails,
      metadata: metadata ?? this.metadata,
    );
  }
}
    required this.partnerId,
    required this.partnerType,
    required this.commissionIds,
    required this.totalAmount,
    this.fees = 0.0,
    required this.netAmount,
    required this.status,
    required this.paymentMethod,
    this.paymentReference,
    required this.createdAt,
    this.processedAt,
    this.completedAt,
    this.failureReason,
  });

  /// Calculate processing time in hours
  int? get processingTimeHours {
    if (processedAt == null) return null;
    return processedAt?.difference(createdAt).inHours;
  }

  /// Check if payout is overdue
  bool get isOverdue {
    if (status == PayoutStatus.completed) return false;
    return DateTime.now().difference(createdAt).inDays > 7;
  }

  Payout copyWith({
    String? id,
    String? partnerId,
    String? partnerType,
    List<String>? commissionIds,
    double? totalAmount,
    double? fees,
    double? netAmount,
    PayoutStatus? status,
    String? paymentMethod,
    String? paymentReference,
    DateTime? createdAt,
    DateTime? processedAt,
    DateTime? completedAt,
    String? failureReason,
    Map<String, dynamic> paymentDetails,
    Map<String, dynamic> metadata,
  }) {
    return Payout(
      id: id ?? this.id,
      partnerId: partnerId ?? this.partnerId,
      partnerType: partnerType ?? this.partnerType,
      commissionIds: commissionIds ?? this.commissionIds,
      totalAmount: totalAmount ?? this.totalAmount,
      fees: fees ?? this.fees,
      netAmount: netAmount ?? this.netAmount,
      status: status ?? this.status,
      paymentMethod: paymentMethod ?? this.paymentMethod,
      paymentReference: paymentReference ?? this.paymentReference,
      createdAt: createdAt ?? this.createdAt,
      processedAt: processedAt ?? this.processedAt,
      completedAt: completedAt ?? this.completedAt,
      failureReason: failureReason ?? this.failureReason,
      paymentDetails: paymentDetails ?? this.paymentDetails,
      metadata: metadata ?? this.metadata,
    );
  }
}
