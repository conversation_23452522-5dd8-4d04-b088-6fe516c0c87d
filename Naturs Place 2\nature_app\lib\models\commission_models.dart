import 'vendor_models.dart';

/// Transaction status
enum TransactionStatus {
  pending,
  completed,
  cancelled,
  refunded,
  disputed
}

/// Commission status
enum CommissionStatus {
  pending,
  approved,
  paid,
  disputed,
  cancelled
}

/// Payout status
enum PayoutStatus {
  pending,
  processing,
  completed,
  failed,
  cancelled
}

/// Simple Transaction model
class Transaction {
  final String id;
  final String orderId;
  final String customerId;
  final String vendorId;
  final String? affiliateId;
  final String productId;
  final String productName;
  final ProductCategory category;
  final double productPrice;
  final int quantity;
  final double subtotal;
  final double tax;
  final double shipping;
  final double total;
  final double discount;
  final String? couponCode;
  final TransactionStatus status;
  final DateTime createdAt;
  final DateTime? completedAt;
  final DateTime? cancelledAt;
  final String? cancellationReason;
  final Map<String, dynamic> metadata;

  const Transaction({
    required this.id,
    required this.orderId,
    required this.customerId,
    required this.vendorId,
    this.affiliateId,
    required this.productId,
    required this.productName,
    required this.category,
    required this.productPrice,
    required this.quantity,
    required this.subtotal,
    required this.tax,
    required this.shipping,
    required this.total,
    this.discount = 0.0,
    this.couponCode,
    required this.status,
    required this.createdAt,
    this.completedAt,
    this.cancelledAt,
    this.cancellationReason,
    this.metadata = const {},
  });

  /// Calculate net amount for commission calculation
  double get netAmount => total - tax - shipping;

  /// Check if transaction is eligible for commission
  bool get isEligibleForCommission {
    return status == TransactionStatus.completed && completedAt != null;
  }

  Transaction copyWith({
    String? id,
    String? orderId,
    String? customerId,
    String? vendorId,
    String? affiliateId,
    String? productId,
    String? productName,
    ProductCategory? category,
    double? productPrice,
    int? quantity,
    double? subtotal,
    double? tax,
    double? shipping,
    double? total,
    double? discount,
    String? couponCode,
    TransactionStatus? status,
    DateTime? createdAt,
    DateTime? completedAt,
    DateTime? cancelledAt,
    String? cancellationReason,
    Map<String, dynamic>? metadata,
  }) {
    return Transaction(
      id: id ?? this.id,
      orderId: orderId ?? this.orderId,
      customerId: customerId ?? this.customerId,
      vendorId: vendorId ?? this.vendorId,
      affiliateId: affiliateId ?? this.affiliateId,
      productId: productId ?? this.productId,
      productName: productName ?? this.productName,
      category: category ?? this.category,
      productPrice: productPrice ?? this.productPrice,
      quantity: quantity ?? this.quantity,
      subtotal: subtotal ?? this.subtotal,
      tax: tax ?? this.tax,
      shipping: shipping ?? this.shipping,
      total: total ?? this.total,
      discount: discount ?? this.discount,
      couponCode: couponCode ?? this.couponCode,
      status: status ?? this.status,
      createdAt: createdAt ?? this.createdAt,
      completedAt: completedAt ?? this.completedAt,
      cancelledAt: cancelledAt ?? this.cancelledAt,
      cancellationReason: cancellationReason ?? this.cancellationReason,
      metadata: metadata ?? this.metadata,
    );
  }
}

/// Simple Commission model
class Commission {
  final String id;
  final String transactionId;
  final String partnerId;
  final String partnerType;
  final ProductCategory category;
  final double saleAmount;
  final double commissionRate;
  final double commissionAmount;
  final double bonusAmount;
  final double totalAmount;
  final double appRevenue;
  final CommissionStatus status;
  final DateTime createdAt;
  final DateTime? approvedAt;
  final DateTime? paidAt;
  final String? payoutId;
  final String? notes;
  final Map<String, dynamic> metadata;

  const Commission({
    required this.id,
    required this.transactionId,
    required this.partnerId,
    required this.partnerType,
    required this.category,
    required this.saleAmount,
    required this.commissionRate,
    required this.commissionAmount,
    this.bonusAmount = 0.0,
    required this.totalAmount,
    required this.appRevenue,
    required this.status,
    required this.createdAt,
    this.approvedAt,
    this.paidAt,
    this.payoutId,
    this.notes,
    this.metadata = const {},
  });

  /// Check if commission is ready for payout
  bool get isReadyForPayout {
    return status == CommissionStatus.approved && paidAt == null;
  }

  /// Calculate days since creation
  int get daysSinceCreation {
    return DateTime.now().difference(createdAt).inDays;
  }

  Commission copyWith({
    String? id,
    String? transactionId,
    String? partnerId,
    String? partnerType,
    ProductCategory? category,
    double? saleAmount,
    double? commissionRate,
    double? commissionAmount,
    double? bonusAmount,
    double? totalAmount,
    double? appRevenue,
    CommissionStatus? status,
    DateTime? createdAt,
    DateTime? approvedAt,
    DateTime? paidAt,
    String? payoutId,
    String? notes,
    Map<String, dynamic>? metadata,
  }) {
    return Commission(
      id: id ?? this.id,
      transactionId: transactionId ?? this.transactionId,
      partnerId: partnerId ?? this.partnerId,
      partnerType: partnerType ?? this.partnerType,
      category: category ?? this.category,
      saleAmount: saleAmount ?? this.saleAmount,
      commissionRate: commissionRate ?? this.commissionRate,
      commissionAmount: commissionAmount ?? this.commissionAmount,
      bonusAmount: bonusAmount ?? this.bonusAmount,
      totalAmount: totalAmount ?? this.totalAmount,
      appRevenue: appRevenue ?? this.appRevenue,
      status: status ?? this.status,
      createdAt: createdAt ?? this.createdAt,
      approvedAt: approvedAt ?? this.approvedAt,
      paidAt: paidAt ?? this.paidAt,
      payoutId: payoutId ?? this.payoutId,
      notes: notes ?? this.notes,
      metadata: metadata ?? this.metadata,
    );
  }
}

/// Simple Payout model
class Payout {
  final String id;
  final String partnerId;
  final String partnerType;
  final List<String> commissionIds;
  final double totalAmount;
  final double fees;
  final double netAmount;
  final PayoutStatus status;
  final String paymentMethod;
  final String? paymentReference;
  final DateTime createdAt;
  final DateTime? processedAt;
  final DateTime? completedAt;
  final String? failureReason;
  final Map<String, dynamic> paymentDetails;
  final Map<String, dynamic> metadata;

  const Payout({
    required this.id,
    required this.partnerId,
    required this.partnerType,
    required this.commissionIds,
    required this.totalAmount,
    this.fees = 0.0,
    required this.netAmount,
    required this.status,
    required this.paymentMethod,
    this.paymentReference,
    required this.createdAt,
    this.processedAt,
    this.completedAt,
    this.failureReason,
    this.paymentDetails = const {},
    this.metadata = const {},
  });

  /// Calculate processing time in hours
  int? get processingTimeHours {
    if (processedAt == null) return null;
    return processedAt?.difference(createdAt).inHours;
  }

  /// Check if payout is overdue
  bool get isOverdue {
    if (status == PayoutStatus.completed) return false;
    return DateTime.now().difference(createdAt).inDays > 7;
  }

  Payout copyWith({
    String? id,
    String? partnerId,
    String? partnerType,
    List<String>? commissionIds,
    double? totalAmount,
    double? fees,
    double? netAmount,
    PayoutStatus? status,
    String? paymentMethod,
    String? paymentReference,
    DateTime? createdAt,
    DateTime? processedAt,
    DateTime? completedAt,
    String? failureReason,
    Map<String, dynamic>? paymentDetails,
    Map<String, dynamic>? metadata,
  }) {
    return Payout(
      id: id ?? this.id,
      partnerId: partnerId ?? this.partnerId,
      partnerType: partnerType ?? this.partnerType,
      commissionIds: commissionIds ?? this.commissionIds,
      totalAmount: totalAmount ?? this.totalAmount,
      fees: fees ?? this.fees,
      netAmount: netAmount ?? this.netAmount,
      status: status ?? this.status,
      paymentMethod: paymentMethod ?? this.paymentMethod,
      paymentReference: paymentReference ?? this.paymentReference,
      createdAt: createdAt ?? this.createdAt,
      processedAt: processedAt ?? this.processedAt,
      completedAt: completedAt ?? this.completedAt,
      failureReason: failureReason ?? this.failureReason,
      paymentDetails: paymentDetails ?? this.paymentDetails,
      metadata: metadata ?? this.metadata,
    );
  }
}
