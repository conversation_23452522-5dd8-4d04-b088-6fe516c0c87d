import 'dart:math';
import 'package:flutter/foundation.dart';
import '../providers/app_state.dart';

/// Plant API Service for fetching healing plants from external sources
class PlantApiService {
  static final PlantApiService _instance = PlantApiService._internal();
  factory PlantApiService() => _instance;
  PlantApiService._internal();

  // API configuration would go here when implementing real API calls

  /// Fetch healing plants from external API
  Future<List<Plant>> fetchHealingPlants({int limit = 10}) async {
    try {
      // For now, we'll simulate API calls and return generated plants
      // In a real implementation, you would call actual plant databases like:
      // - GBIF (Global Biodiversity Information Facility)
      // - iNaturalist API
      // - PlantNet API
      // - Medicinal Plant Database APIs
      
      debugPrint('Fetching $limit healing plants from API...');
      
      // Simulate network delay
      await Future.delayed(const Duration(seconds: 2));
      
      // Generate plants based on external data sources
      return _generateHealingPlantsFromSources(limit);
      
    } catch (e) {
      debugPrint('Error fetching plants from API: $e');
      // Return fallback plants if API fails
      return _getFallbackHealingPlants(limit);
    }
  }

  /// Fetch plant by scientific name
  Future<Plant?> fetchPlantByScientificName(String scientificName) async {
    try {
      debugPrint('Fetching plant: $scientificName');
      
      // Simulate API call
      await Future.delayed(const Duration(seconds: 1));
      
      // In real implementation, query plant database
      return _generatePlantFromScientificName(scientificName);
      
    } catch (e) {
      debugPrint('Error fetching plant by scientific name: $e');
      return null;
    }
  }

  /// Search for healing plants by query
  Future<List<Plant>> searchHealingPlants(String query, {int limit = 20}) async {
    try {
      debugPrint('Searching for healing plants: $query');
      
      // Simulate API call
      await Future.delayed(const Duration(seconds: 1));
      
      // Generate search results
      return _generateSearchResults(query, limit);
      
    } catch (e) {
      debugPrint('Error searching plants: $e');
      return [];
    }
  }

  /// Fetch plants by therapeutic category
  Future<List<Plant>> fetchPlantsByTherapeuticUse(String therapeuticUse, {int limit = 15}) async {
    try {
      debugPrint('Fetching plants for therapeutic use: $therapeuticUse');
      
      // Simulate API call
      await Future.delayed(const Duration(seconds: 1));
      
      return _generatePlantsByTherapeuticUse(therapeuticUse, limit);
      
    } catch (e) {
      debugPrint('Error fetching plants by therapeutic use: $e');
      return [];
    }
  }

  /// Generate healing plants from various sources
  List<Plant> _generateHealingPlantsFromSources(int limit) {
    final plants = <Plant>[];
    final random = Random();
    
    // Healing plant data from various traditional medicine systems
    final healingPlantData = _getHealingPlantDatabase();
    
    for (int i = 0; i < limit && i < healingPlantData.length; i++) {
      final plantData = healingPlantData[i];
      
      plants.add(Plant(
        id: 'api_${DateTime.now().millisecondsSinceEpoch}_$i',
        name: plantData['name']!,
        scientificName: plantData['scientificName']!,
        category: plantData['category']!,
        description: plantData['description']!,
        imageUrl: plantData['imageUrl']!,
        benefits: List<String>.from(plantData['benefits']!),
        rating: 4.0 + random.nextDouble() * 1.0,
        reviewCount: 50 + random.nextInt(500),
        commonNames: List<String>.from(plantData['commonNames'] ?? []),
        origin: plantData['origin'] ?? '',
        habitat: plantData['habitat'] ?? '',
        activeCompounds: List<String>.from(plantData['activeCompounds'] ?? []),
        uses: List<String>.from(plantData['uses'] ?? []),
        preparationMethods: List<String>.from(plantData['preparationMethods'] ?? []),
        precautions: List<String>.from(plantData['precautions'] ?? []),
        dosage: plantData['dosage'] ?? '',
        partUsed: plantData['partUsed'] ?? '',
        traditionalUse: plantData['traditionalUse'] ?? '',
        family: plantData['family'] ?? '',
        energetics: plantData['energetics'] ?? '',
        taste: plantData['taste'] ?? '',
      ));
    }
    
    return plants;
  }

  /// Generate plant from scientific name
  Plant? _generatePlantFromScientificName(String scientificName) {
    final healingPlants = _getHealingPlantDatabase();
    
    // Find plant by scientific name
    final plantData = healingPlants.firstWhere(
      (plant) => plant['scientificName']?.toLowerCase() == scientificName.toLowerCase(),
      orElse: () => <String, dynamic>{},
    );
    
    if (plantData.isEmpty) return null;
    
    final random = Random();
    
    return Plant(
      id: 'api_search_${DateTime.now().millisecondsSinceEpoch}',
      name: plantData['name']!,
      scientificName: plantData['scientificName']!,
      category: plantData['category']!,
      description: plantData['description']!,
      imageUrl: plantData['imageUrl']!,
      benefits: List<String>.from(plantData['benefits']!),
      rating: 4.0 + random.nextDouble() * 1.0,
      reviewCount: 50 + random.nextInt(500),
      commonNames: List<String>.from(plantData['commonNames'] ?? []),
      origin: plantData['origin'] ?? '',
      habitat: plantData['habitat'] ?? '',
      activeCompounds: List<String>.from(plantData['activeCompounds'] ?? []),
      uses: List<String>.from(plantData['uses'] ?? []),
      preparationMethods: List<String>.from(plantData['preparationMethods'] ?? []),
      precautions: List<String>.from(plantData['precautions'] ?? []),
      dosage: plantData['dosage'] ?? '',
      partUsed: plantData['partUsed'] ?? '',
      traditionalUse: plantData['traditionalUse'] ?? '',
      family: plantData['family'] ?? '',
      energetics: plantData['energetics'] ?? '',
      taste: plantData['taste'] ?? '',
    );
  }

  /// Generate search results
  List<Plant> _generateSearchResults(String query, int limit) {
    final healingPlants = _getHealingPlantDatabase();
    final results = <Plant>[];
    final random = Random();
    final lowercaseQuery = query.toLowerCase();
    
    // Filter plants that match the query
    final matchingPlants = healingPlants.where((plant) =>
      plant['name']?.toLowerCase().contains(lowercaseQuery) == true ||
      plant['scientificName']?.toLowerCase().contains(lowercaseQuery) == true ||
      plant['benefits']?.any((benefit) => benefit.toLowerCase().contains(lowercaseQuery)) == true ||
      plant['uses']?.any((use) => use.toLowerCase().contains(lowercaseQuery)) == true
    ).toList();
    
    for (int i = 0; i < limit && i < matchingPlants.length; i++) {
      final plantData = matchingPlants[i];
      
      results.add(Plant(
        id: 'api_search_${DateTime.now().millisecondsSinceEpoch}_$i',
        name: plantData['name']!,
        scientificName: plantData['scientificName']!,
        category: plantData['category']!,
        description: plantData['description']!,
        imageUrl: plantData['imageUrl']!,
        benefits: List<String>.from(plantData['benefits']!),
        rating: 4.0 + random.nextDouble() * 1.0,
        reviewCount: 50 + random.nextInt(500),
        commonNames: List<String>.from(plantData['commonNames'] ?? []),
        origin: plantData['origin'] ?? '',
        habitat: plantData['habitat'] ?? '',
        activeCompounds: List<String>.from(plantData['activeCompounds'] ?? []),
        uses: List<String>.from(plantData['uses'] ?? []),
        preparationMethods: List<String>.from(plantData['preparationMethods'] ?? []),
        precautions: List<String>.from(plantData['precautions'] ?? []),
        dosage: plantData['dosage'] ?? '',
        partUsed: plantData['partUsed'] ?? '',
        traditionalUse: plantData['traditionalUse'] ?? '',
        family: plantData['family'] ?? '',
        energetics: plantData['energetics'] ?? '',
        taste: plantData['taste'] ?? '',
      ));
    }
    
    return results;
  }

  /// Generate plants by therapeutic use
  List<Plant> _generatePlantsByTherapeuticUse(String therapeuticUse, int limit) {
    final healingPlants = _getHealingPlantDatabase();
    final results = <Plant>[];
    final random = Random();
    final lowercaseUse = therapeuticUse.toLowerCase();
    
    // Filter plants by therapeutic use
    final matchingPlants = healingPlants.where((plant) =>
      plant['benefits']?.any((benefit) => benefit.toLowerCase().contains(lowercaseUse)) == true ||
      plant['uses']?.any((use) => use.toLowerCase().contains(lowercaseUse)) == true ||
      plant['category']?.toLowerCase().contains(lowercaseUse) == true
    ).toList();
    
    for (int i = 0; i < limit && i < matchingPlants.length; i++) {
      final plantData = matchingPlants[i];
      
      results.add(Plant(
        id: 'api_therapeutic_${DateTime.now().millisecondsSinceEpoch}_$i',
        name: plantData['name']!,
        scientificName: plantData['scientificName']!,
        category: plantData['category']!,
        description: plantData['description']!,
        imageUrl: plantData['imageUrl']!,
        benefits: List<String>.from(plantData['benefits']!),
        rating: 4.0 + random.nextDouble() * 1.0,
        reviewCount: 50 + random.nextInt(500),
        commonNames: List<String>.from(plantData['commonNames'] ?? []),
        origin: plantData['origin'] ?? '',
        habitat: plantData['habitat'] ?? '',
        activeCompounds: List<String>.from(plantData['activeCompounds'] ?? []),
        uses: List<String>.from(plantData['uses'] ?? []),
        preparationMethods: List<String>.from(plantData['preparationMethods'] ?? []),
        precautions: List<String>.from(plantData['precautions'] ?? []),
        dosage: plantData['dosage'] ?? '',
        partUsed: plantData['partUsed'] ?? '',
        traditionalUse: plantData['traditionalUse'] ?? '',
        family: plantData['family'] ?? '',
        energetics: plantData['energetics'] ?? '',
        taste: plantData['taste'] ?? '',
      ));
    }
    
    return results;
  }

  /// Get fallback healing plants if API fails
  List<Plant> _getFallbackHealingPlants(int limit) {
    // Return a subset of the healing plant database as fallback
    return _generateHealingPlantsFromSources(limit);
  }

  /// Get comprehensive healing plant database
  List<Map<String, dynamic>> _getHealingPlantDatabase() {
    return [
      {
        'name': 'Schisandra Berry',
        'scientificName': 'Schisandra chinensis',
        'category': 'Adaptogenic Berry',
        'description': 'Known as the "Five Flavor Berry," Schisandra is a powerful adaptogen that supports liver health, mental clarity, and physical endurance while balancing all body systems.',
        'imageUrl': 'https://cdn.pixabay.com/photo/2019/09/26/18/25/schisandra-4506027_1280.jpg',
        'benefits': ['Liver Support', 'Mental Clarity', 'Stress Adaptation', 'Endurance', 'Skin Health'],
        'commonNames': ['Five Flavor Berry', 'Wu Wei Zi', 'Magnolia Vine'],
        'origin': 'Northern China, Russia, Korea',
        'habitat': 'Deciduous forests',
        'activeCompounds': ['Schisandrins', 'Lignans', 'Essential oils'],
        'uses': ['Liver detox', 'Mental fatigue', 'Athletic performance', 'Skin aging'],
        'preparationMethods': ['Dried berries', 'Tincture', 'Powder', 'Tea'],
        'dosage': '1-3g dried berries daily',
        'partUsed': 'Berries',
        'traditionalUse': 'Used in TCM for over 2000 years as a superior tonic',
        'family': 'Schisandraceae',
        'energetics': 'Warm, astringent',
        'taste': 'Sweet, sour, bitter, pungent, salty',
      },
      
      {
        'name': 'Cordyceps',
        'scientificName': 'Cordyceps sinensis',
        'category': 'Medicinal Mushroom',
        'description': 'A rare and precious fungus known as "Himalayan Gold," Cordyceps enhances energy, athletic performance, and respiratory function while supporting immune health.',
        'imageUrl': 'https://cdn.pixabay.com/photo/2019/11/07/21/32/cordyceps-4611053_1280.jpg',
        'benefits': ['Energy Enhancement', 'Athletic Performance', 'Respiratory Health', 'Immune Support', 'Kidney Health'],
        'commonNames': ['Caterpillar Fungus', 'Himalayan Gold', 'Winter Worm Summer Grass'],
        'origin': 'Tibetan Plateau, Himalayas',
        'habitat': 'High altitude grasslands',
        'activeCompounds': ['Cordycepin', 'Polysaccharides', 'Adenosine'],
        'uses': ['Athletic performance', 'Chronic fatigue', 'Respiratory issues', 'Kidney support'],
        'preparationMethods': ['Powder', 'Extract', 'Capsules', 'Soup'],
        'dosage': '1-3g daily of extract',
        'partUsed': 'Whole fungus',
        'traditionalUse': 'Prized by Tibetan herders for strength and endurance',
        'family': 'Cordycipitaceae',
        'energetics': 'Warm, sweet',
        'taste': 'Sweet, slightly fishy',
      },
    ];
  }
}
