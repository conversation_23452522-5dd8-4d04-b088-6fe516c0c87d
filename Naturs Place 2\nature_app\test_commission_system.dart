import 'package:flutter/foundation.dart';
import 'lib/services/commission_service.dart';
import 'lib/models/commission_models.dart';
import 'lib/models/vendor_models.dart';

/// Comprehensive test of the commission system with PostgreSQL integration
void main() async {
  print('💰 Testing Nature\'s Place Commission System\n');

  try {
    // Initialize commission service (this will also initialize PostgreSQL)
    print('🚀 Initializing commission service...');
    final commissionService = CommissionService();
    await commissionService.initialize();
    print('✅ Commission service initialized!\n');

    // Create test vendor
    print('👥 Setting up test vendor...');
    final testVendor = Vendor(
      id: 'vendor_healing_herbs',
      name: 'Healing Herbs Co.',
      email: '<EMAIL>',
      phone: '******-0123',
      address: '123 Herb Street, Garden City, GC 12345',
      commissionRate: 15.0,
      status: VendorStatus.active,
      joinedDate: DateTime.now().subtract(const Duration(days: 30)),
      totalSales: 0.0,
      totalCommissions: 0.0,
      paymentMethod: 'bank_transfer',
      bankDetails: {
        'account_name': 'Healing Herbs Co.',
        'account_number': '**********',
        'routing_number': '*********',
      },
    );
    print('✅ Test vendor created: ${testVendor.name}\n');

    // Process test transaction
    print('💳 Processing test transaction...');
    final testTransaction = Transaction(
      id: 'txn_${DateTime.now().millisecondsSinceEpoch}',
      orderId: 'order_healing_001',
      customerId: 'customer_wellness_seeker',
      vendorId: testVendor.id,
      productId: 'product_echinacea_extract',
      productName: 'Organic Echinacea Extract - 60 Capsules',
      category: ProductCategory.herbs,
      productPrice: 34.99,
      quantity: 2,
      subtotal: 69.98,
      tax: 5.60,
      shipping: 8.99,
      total: 84.57,
      status: TransactionStatus.completed,
      createdAt: DateTime.now(),
      completedAt: DateTime.now(),
      metadata: '{"source": "mobile_app", "promotion": "first_time_buyer"}',
    );

    await commissionService.processTransaction(testTransaction);
    print('✅ Transaction processed successfully!');
    print('   Transaction ID: ${testTransaction.id}');
    print('   Total Amount: \$${testTransaction.total}');
    print('   Vendor: ${testVendor.name}\n');

    // Check commission creation
    print('📊 Checking commission creation...');
    final commissions = commissionService.getCommissions();
    final vendorCommissions = commissions.where((c) => c.partnerId == testVendor.id).toList();
    
    if (vendorCommissions.isNotEmpty) {
      final commission = vendorCommissions.first;
      print('✅ Commission created successfully!');
      print('   Commission ID: ${commission.id}');
      print('   Commission Rate: ${commission.commissionRate}%');
      print('   Commission Amount: \$${commission.commissionAmount.toStringAsFixed(2)}');
      print('   App Revenue: \$${commission.appRevenue.toStringAsFixed(2)}');
      print('   Status: ${commission.status.name}\n');
    } else {
      print('⚠️ No commission found for vendor\n');
    }

    // Test analytics
    print('📈 Getting commission analytics...');
    final analytics = await commissionService.getCommissionAnalytics();
    print('✅ Analytics retrieved:');
    print('   Total Commissions: \$${analytics['totalCommissions']}');
    print('   Paid Commissions: \$${analytics['paidCommissions']}');
    print('   Pending Commissions: \$${analytics['pendingCommissions']}');
    print('   Total Transactions: ${analytics['totalTransactions']}');
    print('   Total App Revenue: \$${analytics['totalAppRevenue']}\n');

    // Test payout creation
    print('💸 Testing payout creation...');
    final pendingCommissions = commissions.where((c) => c.status == CommissionStatus.pending).toList();
    
    if (pendingCommissions.isNotEmpty) {
      final payout = await commissionService.createPayout(
        partnerId: testVendor.id,
        partnerType: 'vendor',
        commissionIds: pendingCommissions.map((c) => c.id).toList(),
        paymentMethod: 'bank_transfer',
      );
      
      print('✅ Payout created successfully!');
      print('   Payout ID: ${payout.id}');
      print('   Total Amount: \$${payout.totalAmount.toStringAsFixed(2)}');
      print('   Net Amount: \$${payout.netAmount.toStringAsFixed(2)}');
      print('   Payment Method: ${payout.paymentMethod}');
      print('   Status: ${payout.status.name}\n');
    }

    // Test PostgreSQL sync
    print('🔄 Testing PostgreSQL synchronization...');
    await commissionService.syncToPostgreSQL();
    print('✅ Data synchronized to PostgreSQL successfully!\n');

    // Final summary
    print('🎉 Commission System Test Complete!');
    print('📋 Summary:');
    print('   ✅ Commission service initialized');
    print('   ✅ Transaction processed');
    print('   ✅ Commission calculated and created');
    print('   ✅ Analytics generated');
    print('   ✅ Payout created');
    print('   ✅ Data synced to PostgreSQL');
    print('\n🚀 Your commission system is ready for production!');

  } catch (e, stackTrace) {
    print('❌ Test failed with error: $e');
    print('📋 Stack trace: $stackTrace');
    print('\n🔧 Troubleshooting:');
    print('   • Check PostgreSQL connection');
    print('   • Verify database configuration');
    print('   • Ensure all dependencies are installed');
    print('   • Check network connectivity');
  }
}
