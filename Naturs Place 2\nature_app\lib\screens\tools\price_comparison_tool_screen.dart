import 'package:flutter/material.dart';
import '../../services/affiliate_marketplace_service.dart';

class PriceComparisonToolScreen extends StatefulWidget {
  const PriceComparisonToolScreen({super.key});

  @override
  State<PriceComparisonToolScreen> createState() => _PriceComparisonToolScreenState();
}

class _PriceComparisonToolScreenState extends State<PriceComparisonToolScreen> with TickerProviderStateMixin {
  late TabController _tabController;
  String _selectedCategory = 'All';
  String _sortBy = 'Price Difference';

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Price Comparison Tool'),
        backgroundColor: Colors.blue[600],
        foregroundColor: Colors.white,
        bottom: TabBar(
          controller: _tabController,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          indicatorColor: Colors.white,
          tabs: [
            Tab(icon: Icon(Icons.compare_arrows), text: 'Compare'),
            Tab(icon: Icon(Icons.analytics), text: 'Analytics'),
            Tab(icon: Icon(Icons.lightbulb_outline), text: 'Recommendations'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildCompareTab(),
          _buildAnalyticsTab(),
          _buildRecommendationsTab(),
        ],
      ),
    );
  }

  Widget _buildCompareTab() {
    final priceComparisons = _getMockPriceComparisons();
    
    return Column(
      children: [
        // Filters
        Container(
          padding: const EdgeInsets.all(16),
          color: Colors.grey[50],
          child: Row(
            children: [
              Expanded(
                child: DropdownButtonFormField<String>(
                  value: _selectedCategory,
                  decoration: const InputDecoration(
                    labelText: 'Category',
                    border: OutlineInputBorder(),
                    contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  ),
                  items: ['All', 'Vitamins & Minerals', 'Herbs', 'Essential Oils', 'Supplements']
                      .map((category) => DropdownMenuItem(
                            value: category,
                            child: Text(category),
                          ))
                      .toList(),
                  onChanged: (value) => setState(() => _selectedCategory = value!),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: DropdownButtonFormField<String>(
                  value: _sortBy,
                  decoration: const InputDecoration(
                    labelText: 'Sort By',
                    border: OutlineInputBorder(),
                    contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  ),
                  items: ['Price Difference', 'Our Price', 'Market Average', 'Product Name']
                      .map((sort) => DropdownMenuItem(
                            value: sort,
                            child: Text(sort),
                          ))
                      .toList(),
                  onChanged: (value) => setState(() => _sortBy = value!),
                ),
              ),
            ],
          ),
        ),
        
        // Price Comparison List
        Expanded(
          child: ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: priceComparisons.length,
            itemBuilder: (context, index) {
              final comparison = priceComparisons[index];
              return _buildPriceComparisonCard(comparison);
            },
          ),
        ),
      ],
    );
  }

  Widget _buildPriceComparisonCard(PriceComparison comparison) {
    final priceDifference = comparison.ourPrice - comparison.averageMarketPrice;
    final percentageDifference = (priceDifference / comparison.averageMarketPrice * 100);
    
    Color statusColor;
    IconData statusIcon;
    String statusText;
    
    if (comparison.pricePosition == 'lowest') {
      statusColor = Colors.green;
      statusIcon = Icons.trending_down;
      statusText = 'Lowest Price';
    } else if (comparison.pricePosition == 'competitive') {
      statusColor = Colors.orange;
      statusIcon = Icons.trending_flat;
      statusText = 'Competitive';
    } else {
      statusColor = Colors.red;
      statusIcon = Icons.trending_up;
      statusText = 'Premium';
    }

    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child: Text(
                    comparison.productName,
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: statusColor.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: statusColor.withValues(alpha: 0.3)),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(statusIcon, size: 16, color: statusColor),
                      const SizedBox(width: 4),
                      Text(
                        statusText,
                        style: TextStyle(
                          color: statusColor,
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            Row(
              children: [
                Expanded(
                  child: _buildPriceInfo('Our Price', '\$${comparison.ourPrice.toStringAsFixed(2)}', Colors.blue),
                ),
                Expanded(
                  child: _buildPriceInfo('Market Avg', '\$${comparison.averageMarketPrice.toStringAsFixed(2)}', Colors.grey),
                ),
                Expanded(
                  child: _buildPriceInfo(
                    'Difference', 
                    '${percentageDifference >= 0 ? '+' : ''}${percentageDifference.toStringAsFixed(1)}%',
                    percentageDifference >= 0 ? Colors.red : Colors.green,
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            // Competitor Prices
            Text(
              'Competitor Prices',
              style: Theme.of(context).textTheme.titleSmall?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            
            Wrap(
              spacing: 8,
              runSpacing: 4,
              children: comparison.competitorPrices.entries.map((entry) {
                return Chip(
                  label: Text('${entry.key}: \$${entry.value.toStringAsFixed(2)}'),
                  backgroundColor: Colors.grey[100],
                );
              }).toList(),
            ),
            
            if (comparison.recommendations.isNotEmpty) ...[
              const SizedBox(height: 16),
              Text(
                'Recommendations',
                style: Theme.of(context).textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              ...comparison.recommendations.map((rec) => Padding(
                padding: const EdgeInsets.only(bottom: 4),
                child: Row(
                  children: [
                    Icon(Icons.lightbulb, size: 16, color: Colors.orange),
                    const SizedBox(width: 8),
                    Expanded(child: Text(rec, style: const TextStyle(fontSize: 14))),
                  ],
                ),
              )),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildPriceInfo(String label, String value, Color color) {
    return Column(
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey[600],
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
      ],
    );
  }

  Widget _buildAnalyticsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Price Analytics Overview',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          
          // Summary Cards
          Row(
            children: [
              Expanded(child: _buildAnalyticsCard('Products Tracked', '24', Icons.inventory, Colors.blue)),
              const SizedBox(width: 16),
              Expanded(child: _buildAnalyticsCard('Avg Price Position', '12%', Icons.trending_up, Colors.green)),
            ],
          ),
          const SizedBox(height: 16),
          
          Row(
            children: [
              Expanded(child: _buildAnalyticsCard('Competitive Products', '8', Icons.compare, Colors.orange)),
              const SizedBox(width: 16),
              Expanded(child: _buildAnalyticsCard('Premium Products', '16', Icons.star, Colors.purple)),
            ],
          ),
          
          const SizedBox(height: 24),
          
          // Price Trends
          Text(
            'Price Trends',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  _buildTrendItem('Ashwagandha Vitamins & Minerals', '+5.2%', 'Last 30 days', Colors.green),
                  const Divider(),
                  _buildTrendItem('Organic Turmeric Capsules', '-2.1%', 'Last 30 days', Colors.red),
                  const Divider(),
                  _buildTrendItem('Lavender Essential Oil', '+8.7%', 'Last 30 days', Colors.green),
                  const Divider(),
                  _buildTrendItem('Vitamin D3 Supplements', '+1.3%', 'Last 30 days', Colors.green),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAnalyticsCard(String title, String value, IconData icon, Color color) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Icon(icon, color: color, size: 32),
            const SizedBox(height: 8),
            Text(
              value,
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            Text(
              title,
              style: Theme.of(context).textTheme.bodySmall,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTrendItem(String product, String change, String period, Color color) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  product,
                  style: const TextStyle(fontWeight: FontWeight.w600),
                ),
                Text(
                  period,
                  style: TextStyle(color: Colors.grey[600], fontSize: 12),
                ),
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              change,
              style: TextStyle(
                color: color,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRecommendationsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Pricing Recommendations',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          
          _buildRecommendationCard(
            'Optimize Premium Products',
            'Consider reducing prices on 3 premium products to increase competitiveness',
            'High Impact',
            Colors.red,
            Icons.trending_down,
            ['Reduce Lavender Oil by 8%', 'Adjust Vitamin D3 pricing', 'Review premium supplement costs'],
          ),
          
          const SizedBox(height: 16),
          
          _buildRecommendationCard(
            'Leverage Competitive Advantage',
            'You have the lowest prices in 8 categories - promote these aggressively',
            'Medium Impact',
            Colors.orange,
            Icons.campaign,
            ['Feature lowest-price products', 'Create price-match guarantee', 'Highlight savings in marketing'],
          ),
          
          const SizedBox(height: 16),
          
          _buildRecommendationCard(
            'Market Opportunity',
            'Competitors raised prices on immune support products - maintain current pricing',
            'Low Impact',
            Colors.green,
            Icons.lock,
            ['Keep current immune support pricing', 'Monitor competitor changes', 'Consider slight increase in Q3'],
          ),
        ],
      ),
    );
  }

  Widget _buildRecommendationCard(String title, String description, String priority, Color color, IconData icon, List<String> actions) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                CircleAvatar(
                  backgroundColor: color.withValues(alpha: 0.1),
                  child: Icon(icon, color: color),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            title,
                            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                            decoration: BoxDecoration(
                              color: color.withValues(alpha: 0.1),
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Text(
                              priority,
                              style: TextStyle(
                                color: color,
                                fontSize: 12,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 4),
                      Text(
                        description,
                        style: TextStyle(color: Colors.grey[600]),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Text(
              'Recommended Actions:',
              style: Theme.of(context).textTheme.titleSmall?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            ...actions.map((action) => Padding(
              padding: const EdgeInsets.only(bottom: 4),
              child: Row(
                children: [
                  Icon(Icons.check_circle_outline, size: 16, color: color),
                  const SizedBox(width: 8),
                  Expanded(child: Text(action)),
                ],
              ),
            )),
          ],
        ),
      ),
    );
  }

  List<PriceComparison> _getMockPriceComparisons() {
    return [
      PriceComparison(
        productId: '1',
        productName: 'Ashwagandha Vitamins & Minerals',
        ourPrice: 24.99,
        competitorPrices: {
          'HealthStore': 29.99,
          'VitaminWorld': 27.50,
          'NaturalLife': 26.99,
        },
        averageMarketPrice: 28.16,
        pricePosition: 'lowest',
        recommendations: ['Consider slight price increase to 26.99', 'Promote as best value option'],
      ),
      PriceComparison(
        productId: '2',
        productName: 'Organic Turmeric Capsules',
        ourPrice: 19.99,
        competitorPrices: {
          'OrganicPlus': 18.99,
          'HerbLife': 21.50,
          'PureNature': 20.99,
        },
        averageMarketPrice: 20.49,
        pricePosition: 'competitive',
        recommendations: ['Price is competitive', 'Monitor OrganicPlus pricing'],
      ),
      PriceComparison(
        productId: '3',
        productName: 'Lavender Essential Oil Set',
        ourPrice: 45.99,
        competitorPrices: {
          'EssentialWorld': 39.99,
          'AromaTherapy': 42.50,
          'PureOils': 41.99,
        },
        averageMarketPrice: 41.49,
        pricePosition: 'premium',
        recommendations: ['Consider reducing price to 42.99', 'Highlight premium quality'],
      ),
    ];
  }
}
