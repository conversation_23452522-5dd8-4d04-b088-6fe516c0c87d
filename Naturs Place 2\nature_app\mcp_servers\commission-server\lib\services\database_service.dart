import 'package:postgres/postgres.dart';
import 'package:dotenv/dotenv.dart' show load, env;

class DatabaseService {
  PostgreSQLConnection? _connection;

  Future<PostgreSQLConnection> get connection async {
    if (_connection == null) {
      load();
      final host = env['DB_HOST'] ?? 'localhost';
      final port = int.parse(env['DB_PORT'] ?? '5432');
      final databaseName = env['DB_NAME'] ?? 'commission_db';
      final username = env['DB_USER'] ?? 'postgres';
      final password = env['DB_PASSWORD'] ?? 'Artofwar1!';

      _connection = PostgreSQLConnection(
        host,
        port,
        databaseName,
        username: username,
        password: password,
      );
    }

    if (_connection!.isClosed) {
      await _connection!.open();
    }
    return _connection!;
  }

  Future<void> createTableIfNotExists() async {
    final conn = await connection;
    await conn.execute('''
      CREATE TABLE IF NOT EXISTS transactions (
        id SERIAL PRIMARY KEY,
        sale_amount DECIMAL NOT NULL,
        platform_fee DECIMAL NOT NULL,
        affiliate_commission DECIMAL NOT NULL,
        vendor_payout DECIMAL NOT NULL
      )
    ''');
  }

  Future<void> logTransaction(Map<String, dynamic> commissionData) async {
    final conn = await connection;
    await conn.execute(
      'INSERT INTO transactions (sale_amount, platform_fee, affiliate_commission, vendor_payout) '
      'VALUES (@saleAmount, @platformFee, @affiliateCommission, @vendorPayout)',
      substitutionValues: {
        'saleAmount': commissionData['saleAmount'],
        'platformFee': commissionData['platformFee'],
        'affiliateCommission': commissionData['affiliateCommission'],
        'vendorPayout': commissionData['vendorPayout'],
      },
    );
  }

  Future<void> close() async {
    if (_connection != null && !_connection!.isClosed) {
      await _connection!.close();
    }
  }
}
