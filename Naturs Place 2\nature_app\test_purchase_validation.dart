// Simple test to verify purchase-only commission logic
void main() {
  print('🛒 Testing Purchase-Only Commission Logic\n');

  // Test data
  final saleAmount = 100.0;
  final partnerCommissionRate = 5.0; // 5%
  final vendorCommissionRate = 10.0; // 10%

  print('📊 Test Scenario: \$${saleAmount.toStringAsFixed(2)} sale via partner referral\n');

  // Scenario 1: ✅ Referral link used + Purchase completed
  print('✅ Scenario 1: Referral Purchase Completed');
  print('   Conditions:');
  print('   • Referral link used: ✅ YES');
  print('   • Purchase completed: ✅ YES');
  print('   • Valid amount: ✅ \$${saleAmount.toStringAsFixed(2)}');
  print('   • Active partner: ✅ YES');
  print('');
  print('   Results:');
  
  final vendorOwesUs = saleAmount * (vendorCommissionRate / 100);
  final partnerEarns = saleAmount * (partnerCommissionRate / 100);
  final netProfit = vendorOwesUs - partnerEarns;
  
  print('   • Vendor owes us: \$${vendorOwesUs.toStringAsFixed(2)} (${vendorCommissionRate}%)');
  print('   • Partner earns: \$${partnerEarns.toStringAsFixed(2)} (${partnerCommissionRate}%)');
  print('   • We keep: \$${netProfit.toStringAsFixed(2)}');
  print('   • Partner commission created: ✅ YES');
  print('   • Partner gets paid in 30 days: ✅ YES\n');

  // Scenario 2: ❌ Referral link used + Purchase cancelled
  print('❌ Scenario 2: Referral Purchase Cancelled');
  print('   Conditions:');
  print('   • Referral link used: ✅ YES');
  print('   • Purchase completed: ❌ NO (cancelled)');
  print('   • Valid amount: ✅ \$${saleAmount.toStringAsFixed(2)}');
  print('   • Active partner: ✅ YES');
  print('');
  print('   Results:');
  print('   • Vendor owes us: \$${vendorOwesUs.toStringAsFixed(2)} (still owe commission)');
  print('   • Partner earns: \$0.00 (no commission for cancelled purchase)');
  print('   • We keep: \$${vendorOwesUs.toStringAsFixed(2)} (full vendor commission)');
  print('   • Partner commission created: ❌ NO');
  print('   • Partner gets paid: ❌ NO\n');

  // Scenario 3: ❌ No referral link + Purchase completed
  print('❌ Scenario 3: Direct Purchase (No Referral)');
  print('   Conditions:');
  print('   • Referral link used: ❌ NO');
  print('   • Purchase completed: ✅ YES');
  print('   • Valid amount: ✅ \$${saleAmount.toStringAsFixed(2)}');
  print('   • Active partner: N/A');
  print('');
  print('   Results:');
  print('   • Vendor owes us: \$${vendorOwesUs.toStringAsFixed(2)} (${vendorCommissionRate}%)');
  print('   • Partner earns: \$0.00 (no referral link)');
  print('   • We keep: \$${vendorOwesUs.toStringAsFixed(2)} (full vendor commission)');
  print('   • Partner commission created: ❌ NO');
  print('   • Partner gets paid: ❌ NO\n');

  // Scenario 4: ❌ Referral link used + Purchase pending
  print('❌ Scenario 4: Referral Purchase Pending');
  print('   Conditions:');
  print('   • Referral link used: ✅ YES');
  print('   • Purchase completed: ❌ NO (pending)');
  print('   • Valid amount: ✅ \$${saleAmount.toStringAsFixed(2)}');
  print('   • Active partner: ✅ YES');
  print('');
  print('   Results:');
  print('   • Vendor owes us: \$0.00 (no commission until completed)');
  print('   • Partner earns: \$0.00 (no commission until completed)');
  print('   • We keep: \$0.00 (waiting for completion)');
  print('   • Partner commission created: ❌ NO (yet)');
  print('   • Partner gets paid: ❌ NO (yet)\n');

  // Summary
  print('📋 Commission Rules Summary:');
  print('');
  print('🎯 Partners ONLY get paid when:');
  print('   ✅ Referral link was used (affiliateId exists)');
  print('   ✅ Purchase was completed (status = completed)');
  print('   ✅ Purchase amount is valid (> \$0)');
  print('   ✅ Partner account is active');
  print('');
  print('❌ Partners get NOTHING when:');
  print('   ❌ No referral link used (direct purchase)');
  print('   ❌ Purchase cancelled or refunded');
  print('   ❌ Purchase still pending');
  print('   ❌ Zero or invalid amount');
  print('   ❌ Partner account inactive');
  print('');
  print('💰 Your Business Model:');
  print('   • Vendors always owe you commission (${vendorCommissionRate}%)');
  print('   • Partners only earn on completed referral purchases (${partnerCommissionRate}%)');
  print('   • You keep the difference (\$${netProfit.toStringAsFixed(2)} per \$${saleAmount.toStringAsFixed(2)} referral sale)');
  print('   • Partners get paid every 30 days via Stripe');
  print('');
  print('🔒 Fraud Prevention:');
  print('   • No payment for clicks or visits');
  print('   • No payment for cancelled orders');
  print('   • Automatic commission cancellation for refunds');
  print('   • Real-time purchase validation');
  print('');
  print('🎉 System ensures partners are only paid for genuine value!');
}
