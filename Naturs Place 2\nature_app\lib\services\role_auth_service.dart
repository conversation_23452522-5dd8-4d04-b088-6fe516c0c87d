import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/user_models.dart';
import 'role_based_access_service.dart';
import 'postgres_service.dart';

/// Role-based authentication service for partners and vendors
class RoleAuthService extends ChangeNotifier {
  static final RoleAuthService _instance = RoleAuthService._internal();
  factory RoleAuthService() => _instance;
  RoleAuthService._internal();

  final RoleBasedAccessService _accessService = RoleBasedAccessService();
  final PostgreSQLService _postgresService = PostgreSQLService();
  
  User? _currentUser;
  bool _isLoading = false;
  bool _isInitialized = false;

  /// Get current authenticated user
  User? get currentUser => _currentUser;

  /// Check if user is authenticated
  bool get isAuthenticated => _currentUser != null;

  /// Get current user role
  UserRole? get currentUserRole => _currentUser?.role;

  /// Check if loading
  bool get isLoading => _isLoading;

  /// Initialize authentication service
  Future<void> initialize() async {
    if (_isInitialized) return;
    
    _isLoading = true;
    notifyListeners();

    try {
      await _postgresService.initialize();
      
      // Try to restore session from SharedPreferences
      final prefs = await SharedPreferences.getInstance();
      final userJson = prefs.getString('role_user');
      
      if (userJson != null) {
        // In production, validate token with backend
        debugPrint('Restoring role-based user session...');
      }
      
      _isInitialized = true;
      debugPrint('✅ Role Auth Service initialized');
    } catch (e) {
      debugPrint('❌ Role Auth Service initialization failed: $e');
    }

    _isLoading = false;
    notifyListeners();
  }

  /// Login with email, password, and role
  Future<RoleAuthResult> loginWithRole(String email, String password, UserRole expectedRole) async {
    _isLoading = true;
    notifyListeners();

    try {
      // Rate limiting
      if (kIsWeb) {
        final prefs = await SharedPreferences.getInstance();
        final lastAttempt = prefs.getInt('last_role_signin_attempt') ?? 0;
        final now = DateTime.now().millisecondsSinceEpoch;

        if (now - lastAttempt < 1000) {
          _isLoading = false;
          notifyListeners();
          return RoleAuthResult(
            success: false,
            error: 'Please wait before trying again',
          );
        }

        await prefs.setInt('last_role_signin_attempt', now);
      }

      // Validate credentials and role
      final user = await _validateCredentialsWithRole(email, password, expectedRole);
      
      if (user == null) {
        _isLoading = false;
        notifyListeners();
        return RoleAuthResult(
          success: false,
          error: 'Invalid credentials or unauthorized role access',
        );
      }

      if (!user.isActive) {
        _isLoading = false;
        notifyListeners();
        return RoleAuthResult(
          success: false,
          error: 'Account is not active. Please contact support.',
        );
      }

      // Check if vendor payment is current (for vendors only)
      if (user.role == UserRole.vendor) {
        final vendorUser = user as VendorUser;
        if (vendorUser.isPaymentOverdue) {
          _isLoading = false;
          notifyListeners();
          return RoleAuthResult(
            success: false,
            error: 'Payment overdue. Please update your payment to access vendor dashboard.',
            requiresPayment: true,
          );
        }
      }

      // Set current user
      _currentUser = user;
      
      // Save to SharedPreferences
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('role_user', user.id);
      
      // Get appropriate dashboard route
      final dashboardRoute = _accessService.getDashboardRoute(user.role);

      debugPrint('✅ Role-based login: ${user.email} (${user.role.name})');

      _isLoading = false;
      notifyListeners();

      return RoleAuthResult(
        success: true,
        user: user,
        redirectRoute: dashboardRoute,
      );
    } catch (e) {
      debugPrint('❌ Role-based login failed: $e');
      _isLoading = false;
      notifyListeners();
      return RoleAuthResult(
        success: false,
        error: 'Login failed. Please try again.',
      );
    }
  }

  /// Partner-specific login
  Future<RoleAuthResult> partnerLogin(String email, String password) async {
    return await loginWithRole(email, password, UserRole.partner);
  }

  /// Vendor-specific login
  Future<RoleAuthResult> vendorLogin(String email, String password) async {
    return await loginWithRole(email, password, UserRole.vendor);
  }

  /// Logout current user
  Future<void> logout() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('role_user');
      
      _currentUser = null;
      debugPrint('✅ Role-based user logged out');
      notifyListeners();
    } catch (e) {
      debugPrint('❌ Logout failed: $e');
    }
  }

  /// Check if current user has access to feature
  bool hasAccess(String feature) {
    if (_currentUser == null) return false;
    return _accessService.hasAccess(_currentUser!.role, feature);
  }

  /// Check if current user can access route
  bool canAccessRoute(String route) {
    if (_currentUser == null) return false;
    return _accessService.canAccessRoute(_currentUser!.role, route);
  }

  /// Get vendor tier fee for current vendor
  double? getVendorTierFee() {
    if (_currentUser?.role != UserRole.vendor) return null;
    final vendorUser = _currentUser as VendorUser;
    return vendorUser.monthlyFee;
  }

  /// Get vendor tier benefits
  Map<String, dynamic>? getVendorTierBenefits() {
    if (_currentUser?.role != UserRole.vendor) return null;
    final vendorUser = _currentUser as VendorUser;
    return _accessService.getVendorTierBenefits(vendorUser.tier);
  }

  /// Check if vendor can add more products
  bool canVendorAddProducts() {
    if (_currentUser?.role != UserRole.vendor) return false;
    final vendorUser = _currentUser as VendorUser;
    final benefits = _accessService.getVendorTierBenefits(vendorUser.tier);
    final maxProducts = benefits['max_products'] as int;
    
    if (maxProducts == -1) return true; // Unlimited
    return vendorUser.productCount < maxProducts;
  }

  /// Get partner performance grade
  String? getPartnerPerformanceGrade() {
    if (_currentUser?.role != UserRole.partner) return null;
    final partnerUser = _currentUser as PartnerUser;
    return partnerUser.performanceGrade;
  }

  /// Enforce role-based access control
  bool enforceAccess(String feature) {
    if (!hasAccess(feature)) {
      debugPrint('❌ Access denied to feature: $feature for role: ${_currentUser?.role.name}');
      return false;
    }
    return true;
  }

  // Private helper methods

  Future<User?> _validateCredentialsWithRole(String email, String password, UserRole expectedRole) async {
    // Get user and validate role matches
    final user = _getMockUser(email);
    
    if (user == null || user.role != expectedRole) {
      return null;
    }
    
    return user;
  }

  // Mock users for testing - replace with actual database queries
  User? _getMockUser(String email) {
    if (email == '<EMAIL>') {
      return PartnerUser(
        id: 'partner_001',
        email: email,
        firstName: 'John',
        lastName: 'Partner',
        status: UserStatus.active,
        createdAt: DateTime.now().subtract(const Duration(days: 30)),
        socialMediaHandle: '@wellness_guru',
        website: 'https://wellnessguru.com',
        audienceDescription: 'Health and wellness enthusiasts',
        followerCount: 5000,
        platforms: ['Instagram', 'TikTok', 'YouTube'],
        totalEarnings: 450.0,
        conversionRate: 0.15,
        stripeAccountId: 'acct_partner_001',
      );
    } else if (email == '<EMAIL>') {
      return VendorUser(
        id: 'vendor_001',
        email: email,
        firstName: 'Jane',
        lastName: 'Vendor',
        status: UserStatus.active,
        createdAt: DateTime.now().subtract(const Duration(days: 60)),
        businessName: 'Healing Herbs Co.',
        businessAddress: '123 Herb Street, Garden City, GC 12345',
        taxId: 'TAX123456789',
        tier: VendorTier.standard,
        monthlyFee: VendorTier.standard.monthlyFee,
        lastPaymentDate: DateTime.now().subtract(const Duration(days: 15)),
        nextPaymentDate: DateTime.now().add(const Duration(days: 15)),
        paymentCurrent: true,
        totalSales: 15000.0,
        totalCommissionOwed: 1800.0,
        productCount: 45,
      );
    } else if (email == '<EMAIL>') {
      return VendorUser(
        id: 'vendor_002',
        email: email,
        firstName: 'Bob',
        lastName: 'BasicVendor',
        status: UserStatus.active,
        createdAt: DateTime.now().subtract(const Duration(days: 30)),
        businessName: 'Small Herbs Shop',
        businessAddress: '456 Small Street, Town, ST 67890',
        taxId: 'TAX987654321',
        tier: VendorTier.basic,
        monthlyFee: VendorTier.basic.monthlyFee,
        lastPaymentDate: DateTime.now().subtract(const Duration(days: 5)),
        nextPaymentDate: DateTime.now().add(const Duration(days: 25)),
        paymentCurrent: true,
        totalSales: 3000.0,
        totalCommissionOwed: 450.0,
        productCount: 15,
      );
    }
    return null;
  }
}

/// Role-based authentication result model
class RoleAuthResult {
  final bool success;
  final User? user;
  final String? error;
  final String? message;
  final String? redirectRoute;
  final bool requiresPayment;

  RoleAuthResult({
    required this.success,
    this.user,
    this.error,
    this.message,
    this.redirectRoute,
    this.requiresPayment = false,
  });
}
