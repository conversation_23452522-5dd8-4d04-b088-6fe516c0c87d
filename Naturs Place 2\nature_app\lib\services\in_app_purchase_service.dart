import 'dart:async';
import 'package:flutter/foundation.dart';

// Mock Product Details for when in_app_purchase is not available
class MockProductDetails {
  final String id;
  final String title;
  final String description;
  final String price;
  final double rawPrice;
  final String currencyCode;

  MockProductDetails({
    required this.id,
    required this.title,
    required this.description,
    required this.price,
    required this.rawPrice,
    required this.currencyCode,
  });
}

// Mock Purchase Details for when in_app_purchase is not available
class MockPurchaseDetails {
  final String productID;
  final String purchaseID;
  final String transactionDate;
  final String status;

  MockPurchaseDetails({
    required this.productID,
    required this.purchaseID,
    required this.transactionDate,
    required this.status,
  });
}

// Subscription Product IDs
class SubscriptionProducts {
  static const String basicMonthly = 'basic_monthly_subscription';
  static const String premiumMonthly = 'premium_monthly_subscription';
  static const String enterpriseMonthly = 'enterprise_monthly_subscription';

  static const List<String> allProductIds = [
    basicMonthly,
    premiumMonthly,
    enterpriseMonthly,
  ];
}

// Subscription Tier Model
class SubscriptionTier {
  final String id;
  final String name;
  final String description;
  final double price;
  final int productLimit;
  final double platformFeeRate;
  final List<String> features;
  final String productId;

  const SubscriptionTier({
    required this.id,
    required this.name,
    required this.description,
    required this.price,
    required this.productLimit,
    required this.platformFeeRate,
    required this.features,
    required this.productId,
  });
}

// Available Subscription Tiers
class SubscriptionTiers {
  static const free = SubscriptionTier(
    id: 'free',
    name: 'Free',
    description: 'Perfect for casual plant enthusiasts',
    price: 0.0,
    productLimit: 0,
    platformFeeRate: 0.0,
    features: [
      '5 free plant scans per month',
      'Basic plant identification (70% accuracy)',
      'Limited access to plant encyclopedia',
      'Basic AI assistant (3 questions/month)',
      'Community access',
    ],
    productId: 'free_plan',
  );

  static const basic = SubscriptionTier(
    id: 'basic',
    name: 'Premium',
    description: 'Ideal for plant lovers and health enthusiasts',
    price: 9.99,
    productLimit: 0,
    platformFeeRate: 0.0,
    features: [
      'Unlimited plant scans',
      'Advanced plant identification',
      'Full plant encyclopedia access',
      'Unlimited AI assistant',
      'Disease detection & treatment',
      'Personalized health recommendations',
      'Offline plant database',
    ],
    productId: SubscriptionProducts.basicMonthly,
  );

  static const premium = SubscriptionTier(
    id: 'premium',
    name: 'Pro',
    description: 'For health practitioners and serious herbalists',
    price: 19.99,
    productLimit: 0,
    platformFeeRate: 0.0,
    features: [
      'Everything in Premium',
      'Professional plant database',
      'Clinical research access',
      'Dosage & preparation guides',
      'Drug interaction warnings',
      'Priority customer support',
    ],
    productId: SubscriptionProducts.premiumMonthly,
  );

  static const List<SubscriptionTier> all = [free, basic, premium];
  
  static SubscriptionTier getByProductId(String productId) {
    return all.firstWhere((tier) => tier.productId == productId);
  }
}

class InAppPurchaseService extends ChangeNotifier {
  static final InAppPurchaseService _instance = InAppPurchaseService._internal();
  factory InAppPurchaseService() => _instance;
  InAppPurchaseService._internal();

  // Mock implementation - replace with real implementation when packages are available
  List<MockProductDetails> _products = [];
  final List<MockPurchaseDetails> _purchases = [];
  bool _isAvailable = true; // Mock as available
  bool _purchasePending = false;
  String? _queryProductError;
  String? _currentSubscription;

  List<MockProductDetails> get products => _products;
  List<MockPurchaseDetails> get purchases => _purchases;
  bool get isAvailable => _isAvailable;
  bool get purchasePending => _purchasePending;
  String? get queryProductError => _queryProductError;
  String? get currentSubscription => _currentSubscription;

  Future<void> initialize() async {
    // Mock initialization - create mock products
    _products = [
      MockProductDetails(
        id: SubscriptionProducts.basicMonthly,
        title: 'Premium Monthly',
        description: 'Premium subscription for plant enthusiasts',
        price: '\$9.99',
        rawPrice: 9.99,
        currencyCode: 'USD',
      ),
      MockProductDetails(
        id: SubscriptionProducts.premiumMonthly,
        title: 'Pro Monthly',
        description: 'Pro subscription for health practitioners',
        price: '\$19.99',
        rawPrice: 19.99,
        currencyCode: 'USD',
      ),
    ];

    _isAvailable = true;
    _queryProductError = null;
    notifyListeners();
  }

  Future<bool> buySubscription(MockProductDetails productDetails) async {
    if (!_isAvailable) {
      return false;
    }

    _purchasePending = true;
    notifyListeners();

    try {
      // Mock purchase - simulate successful purchase
      await Future.delayed(const Duration(seconds: 2));

      // Create mock purchase
      final mockPurchase = MockPurchaseDetails(
        productID: productDetails.id,
        purchaseID: 'mock_purchase_${DateTime.now().millisecondsSinceEpoch}',
        transactionDate: DateTime.now().toIso8601String(),
        status: 'purchased',
      );

      _purchases.add(mockPurchase);
      _currentSubscription = productDetails.id;
      _purchasePending = false;
      notifyListeners();

      return true;
    } catch (e) {
      _purchasePending = false;
      notifyListeners();
      return false;
    }
  }

  SubscriptionTier? getCurrentSubscriptionTier() {
    if (_currentSubscription == null) return null;
    
    try {
      return SubscriptionTiers.getByProductId(_currentSubscription!);
    } catch (e) {
      return null;
    }
  }

  bool hasActiveSubscription() {
    return _currentSubscription != null;
  }

  bool hasSubscription(String productId) {
    return _currentSubscription == productId;
  }

  MockProductDetails? getProductDetails(String productId) {
    try {
      return _products.firstWhere((product) => product.id == productId);
    } catch (e) {
      return null;
    }
  }

  @override
  void dispose() {
    // Mock dispose - no actual cleanup needed
    super.dispose();
  }
}
