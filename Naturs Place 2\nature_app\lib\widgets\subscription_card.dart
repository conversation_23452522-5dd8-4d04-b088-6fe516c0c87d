import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import '../providers/app_state.dart';

class SubscriptionCard extends StatelessWidget {
  final SubscriptionTier tier;
  final String title;
  final String price;
  final String period;
  final String description;
  final List<String> features;
  final List<String> unavailableFeatures;
  final bool isPopular;

  const SubscriptionCard({
    super.key,
    required this.tier,
    required this.title,
    required this.price,
    required this.period,
    required this.description,
    required this.features,
    required this.unavailableFeatures,
    required this.isPopular,
  });

  @override
  Widget build(BuildContext context) {
    return Consumer<AppState>(
      builder: (context, appState, child) {
        final isCurrentPlan = appState.subscriptionTier == tier;
        final isPremiumTier = tier == SubscriptionTier.premium;
        
        return Card(
          elevation: isPremiumTier ? 4 : 2,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
            side: isPremiumTier 
                ? const BorderSide(color: Color(0xFF22c55e), width: 2)
                : BorderSide.none,
          ),
          child: Stack(
            children: [
              // Popular badge
              if (isPopular)
                Positioned(
                  top: -4,
                  right: -4,
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                    decoration: BoxDecoration(
                      color: Colors.amber,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      'Most Popular',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Colors.amber[800],
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ),
              
              // Premium tier top border
              if (isPremiumTier)
                Positioned(
                  top: 0,
                  left: 0,
                  right: 0,
                  child: Container(
                    height: 4,
                    decoration: const BoxDecoration(
                      color: Color(0xFF22c55e),
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(16),
                        topRight: Radius.circular(16),
                      ),
                    ),
                  ),
                ),
              
              Padding(
                padding: const EdgeInsets.all(24.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Header
                    Text(
                      title,
                      style: Theme.of(context).textTheme.titleLarge,
                    ),
                    const SizedBox(height: 8),
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        Text(
                          price,
                          style: Theme.of(context).textTheme.headlineLarge?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        Text(
                          period,
                          style: Theme.of(context).textTheme.titleMedium,
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Text(
                      description,
                      style: Theme.of(context).textTheme.bodyMedium,
                    ),
                    const SizedBox(height: 24),
                    
                    // Features
                    ...features.map((feature) => Padding(
                      padding: const EdgeInsets.only(bottom: 12),
                      child: Row(
                        children: [
                          const FaIcon(
                            FontAwesomeIcons.check,
                            color: Color(0xFF22c55e),
                            size: 16,
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: Text(
                              feature,
                              style: Theme.of(context).textTheme.bodyMedium,
                            ),
                          ),
                        ],
                      ),
                    )),
                    
                    // Unavailable features
                    ...unavailableFeatures.map((feature) => Padding(
                      padding: const EdgeInsets.only(bottom: 12),
                      child: Row(
                        children: [
                          const FaIcon(
                            FontAwesomeIcons.xmark,
                            color: Colors.grey,
                            size: 16,
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: Text(
                              feature,
                              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                color: Colors.grey,
                              ),
                            ),
                          ),
                        ],
                      ),
                    )),
                    
                    const SizedBox(height: 24),
                    
                    // Action button
                    SizedBox(
                      width: double.infinity,
                      child: isCurrentPlan
                          ? OutlinedButton(
                              onPressed: null,
                              child: const Text(
                                'Current Plan',
                                overflow: TextOverflow.ellipsis,
                              ),
                            )
                          : ElevatedButton(
                              onPressed: () {
                                appState.setSubscriptionTier(tier);
                                ScaffoldMessenger.of(context).showSnackBar(
                                  SnackBar(
                                    content: Text('Upgraded to $title plan!'),
                                    backgroundColor: const Color(0xFF22c55e),
                                  ),
                                );
                              },
                              style: tier == SubscriptionTier.free
                                  ? ElevatedButton.styleFrom(
                                      backgroundColor: Colors.grey[100],
                                      foregroundColor: Colors.grey[700],
                                    )
                                  : null,
                              child: Text(
                                tier == SubscriptionTier.free
                                    ? 'Current Plan'
                                    : 'Upgrade Now',
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
