// Script to fix common issues in test files
import 'dart:io';

void main() async {
  print('🔧 Fixing test files...\n');

  final testFiles = [
    'test_30_day_payouts.dart',
    'test_postgresql_connection.dart',
    'test_referral_purchase_only.dart',
  ];

  for (final fileName in testFiles) {
    print('📝 Fixing $fileName...');
    await fixTestFile(fileName);
  }

  print('\n✅ All test files fixed!');
}

Future<void> fixTestFile(String fileName) async {
  try {
    final file = File(fileName);
    if (!await file.exists()) {
      print('   ⚠️ File not found: $fileName');
      return;
    }

    String content = await file.readAsString();

    // Fix common issues
    content = content.replaceAll(
      "metadata: '",
      'metadata: {'
    );
    content = content.replaceAll(
      "'}',",
      '},',
    );
    content = content.replaceAll(
      'ProductCategory.vitamins',
      'ProductCategory.vitamins',
    );
    content = content.replaceAll(
      'ProductCategory.herbs',
      'ProductCategory.herbs',
    );
    content = content.replaceAll(
      'ProductCategory.supplements',
      'ProductCategory.supplements',
    );

    // Fix metadata strings to proper maps
    content = content.replaceAllMapped(
      RegExp(r'metadata: \{([^}]+)\}'),
      (match) {
        final metadataContent = match.group(1)!;
        if (metadataContent.contains('"')) {
          // Already properly formatted
          return match.group(0)!;
        }
        // Convert string to proper map format
        return 'metadata: {"source": "test"}';
      },
    );

    await file.writeAsString(content);
    print('   ✅ Fixed $fileName');
  } catch (e) {
    print('   ❌ Error fixing $fileName: $e');
  }
}
