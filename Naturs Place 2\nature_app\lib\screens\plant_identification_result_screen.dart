import 'dart:io';
import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import '../services/simplified_plant_identification_service.dart';

/// Plant Identification Result Screen
class PlantIdentificationResultScreen extends StatefulWidget {
  final PlantIdentificationResult result;
  final File imageFile;

  const PlantIdentificationResultScreen({
    super.key,
    required this.result,
    required this.imageFile,
  });

  @override
  State<PlantIdentificationResultScreen> createState() => _PlantIdentificationResultScreenState();
}

class _PlantIdentificationResultScreenState extends State<PlantIdentificationResultScreen> {
  int _selectedTabIndex = 0;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Plant Identified'),
        backgroundColor: Colors.transparent,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.share),
            onPressed: _shareResult,
          ),
          IconButton(
            icon: const Icon(Icons.bookmark_border),
            onPressed: _saveToFavorites,
          ),
        ],
      ),
      body: SingleChildScrollView(
        child: Column(
          children: [
            // Header with image and basic info
            _buildHeader(),
            
            // Confidence indicator
            _buildConfidenceIndicator(),
            
            // Tab navigation
            _buildTabNavigation(),
            
            // Tab content
            _buildTabContent(),
          ],
        ),
      ),
    );
  }

  /// Build header section
  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          // Plant image
          ClipRRect(
            borderRadius: BorderRadius.circular(12),
            child: Image.file(
              widget.imageFile,
              width: 100,
              height: 100,
              fit: BoxFit.cover,
              errorBuilder: (context, error, stackTrace) {
                return Container(
                  width: 100,
                  height: 100,
                  decoration: BoxDecoration(
                    color: Colors.grey[300],
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Icon(Icons.local_florist, size: 40),
                );
              },
            ),
          ),
          
          const SizedBox(width: 16),
          
          // Plant info
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  widget.result.plantName,
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  widget.result.scientificName,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontStyle: FontStyle.italic,
                    color: Colors.grey[600],
                  ),
                ),
                const SizedBox(height: 8),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: widget.result.isHealingPlant 
                      ? const Color(0xFF22c55e).withValues(alpha: 0.1)
                      : Colors.blue.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    widget.result.isHealingPlant ? 'Healing Plant' : 'Regular Plant',
                    style: TextStyle(
                      color: widget.result.isHealingPlant 
                        ? const Color(0xFF22c55e)
                        : Colors.blue,
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Build confidence indicator
  Widget _buildConfidenceIndicator() {
    final confidence = widget.result.confidence;
    final confidencePercent = (confidence * 100).round();
    
    Color confidenceColor;
    String confidenceText;
    
    if (confidence >= 0.8) {
      confidenceColor = Colors.green;
      confidenceText = 'High Confidence';
    } else if (confidence >= 0.6) {
      confidenceColor = Colors.orange;
      confidenceText = 'Medium Confidence';
    } else {
      confidenceColor = Colors.red;
      confidenceText = 'Low Confidence';
    }

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: confidenceColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: confidenceColor.withValues(alpha: 0.3)),
      ),
      child: Row(
        children: [
          Icon(Icons.analytics, color: confidenceColor),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '$confidenceText ($confidencePercent%)',
                  style: TextStyle(
                    color: confidenceColor,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  'Identified using: ${widget.result.identificationMethod}',
                  style: Theme.of(context).textTheme.bodySmall,
                ),
              ],
            ),
          ),
          CircularProgressIndicator(
            value: confidence,
            backgroundColor: confidenceColor.withValues(alpha: 0.2),
            valueColor: AlwaysStoppedAnimation<Color>(confidenceColor),
          ),
        ],
      ),
    );
  }

  /// Build tab navigation
  Widget _buildTabNavigation() {
    final tabs = [
      {'icon': FontAwesomeIcons.info, 'label': 'Info'},
      {'icon': FontAwesomeIcons.heartPulse, 'label': 'Healing'},
      {'icon': FontAwesomeIcons.flask, 'label': 'Usage'},
      {'icon': FontAwesomeIcons.triangleExclamation, 'label': 'Safety'},
    ];

    return Container(
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        children: tabs.asMap().entries.map((entry) {
          final index = entry.key;
          final tab = entry.value;
          final isSelected = index == _selectedTabIndex;

          return Expanded(
            child: GestureDetector(
              onTap: () => setState(() => _selectedTabIndex = index),
              child: Container(
                padding: const EdgeInsets.symmetric(vertical: 12),
                decoration: BoxDecoration(
                  color: isSelected ? const Color(0xFF22c55e) : Colors.transparent,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Column(
                  children: [
                    FaIcon(
                      tab['icon'] as IconData,
                      color: isSelected ? Colors.white : Colors.grey[600],
                      size: 16,
                    ),
                    const SizedBox(height: 4),
                    Text(
                      tab['label'] as String,
                      style: TextStyle(
                        color: isSelected ? Colors.white : Colors.grey[600],
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          );
        }).toList(),
      ),
    );
  }

  /// Build tab content
  Widget _buildTabContent() {
    switch (_selectedTabIndex) {
      case 0:
        return _buildInfoTab();
      case 1:
        return _buildHealingTab();
      case 2:
        return _buildUsageTab();
      case 3:
        return _buildSafetyTab();
      default:
        return _buildInfoTab();
    }
  }

  /// Build info tab
  Widget _buildInfoTab() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildInfoCard(
            'Description',
            widget.result.description,
            FontAwesomeIcons.fileLines,
          ),
          
          if (widget.result.plantFamily.isNotEmpty)
            _buildInfoCard(
              'Plant Family',
              widget.result.plantFamily,
              FontAwesomeIcons.sitemap,
            ),
          
          if (widget.result.nativeRegion.isNotEmpty)
            _buildInfoCard(
              'Native Region',
              widget.result.nativeRegion,
              FontAwesomeIcons.earthAmericas,
            ),
          
          if (widget.result.habitat.isNotEmpty)
            _buildInfoCard(
              'Habitat',
              widget.result.habitat,
              FontAwesomeIcons.mountain,
            ),
          
          if (widget.result.alternativeNames.isNotEmpty)
            _buildInfoCard(
              'Alternative Names',
              widget.result.alternativeNames.join(', '),
              FontAwesomeIcons.tags,
            ),
        ],
      ),
    );
  }

  /// Build healing tab
  Widget _buildHealingTab() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (widget.result.healingProperties.isNotEmpty)
            _buildListCard(
              'Healing Properties',
              widget.result.healingProperties,
              FontAwesomeIcons.heartPulse,
              Colors.green,
            ),
          
          if (widget.result.traditionalUses.isNotEmpty)
            _buildListCard(
              'Traditional Uses',
              widget.result.traditionalUses,
              FontAwesomeIcons.bookOpen,
              Colors.blue,
            ),
          
          if (widget.result.activeCompounds.isNotEmpty)
            _buildListCard(
              'Active Compounds',
              widget.result.activeCompounds,
              FontAwesomeIcons.atom,
              Colors.purple,
            ),
        ],
      ),
    );
  }

  /// Build usage tab
  Widget _buildUsageTab() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (widget.result.preparationMethods.isNotEmpty)
            _buildListCard(
              'Preparation Methods',
              widget.result.preparationMethods,
              FontAwesomeIcons.mortarPestle,
              Colors.orange,
            ),
          
          if (widget.result.dosageInformation.isNotEmpty)
            _buildInfoCard(
              'Dosage Information',
              widget.result.dosageInformation,
              FontAwesomeIcons.pills,
            ),
          
          if (widget.result.careInstructions.isNotEmpty)
            _buildListCard(
              'Care Instructions',
              widget.result.careInstructions,
              FontAwesomeIcons.seedling,
              Colors.green,
            ),
        ],
      ),
    );
  }

  /// Build safety tab
  Widget _buildSafetyTab() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (widget.result.precautions.isNotEmpty)
            _buildListCard(
              'Precautions',
              widget.result.precautions,
              FontAwesomeIcons.triangleExclamation,
              Colors.red,
            ),
          
          if (widget.result.possibleDiseases.isNotEmpty)
            _buildListCard(
              'Possible Plant Diseases',
              widget.result.possibleDiseases,
              FontAwesomeIcons.bug,
              Colors.orange,
            ),
          
          // General safety notice
          _buildInfoCard(
            'Important Safety Notice',
            'Always consult with qualified healthcare professionals before using any plant for medicinal purposes. Proper plant identification is crucial for safety.',
            FontAwesomeIcons.shieldHalved,
          ),
        ],
      ),
    );
  }

  /// Build info card
  Widget _buildInfoCard(String title, String content, IconData icon) {
    if (content.isEmpty) return const SizedBox.shrink();
    
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              FaIcon(icon, color: const Color(0xFF22c55e), size: 16),
              const SizedBox(width: 8),
              Text(
                title,
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            content,
            style: Theme.of(context).textTheme.bodyMedium,
          ),
        ],
      ),
    );
  }

  /// Build list card
  Widget _buildListCard(String title, List<String> items, IconData icon, Color color) {
    if (items.isEmpty) return const SizedBox.shrink();
    
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              FaIcon(icon, color: color, size: 16),
              const SizedBox(width: 8),
              Text(
                title,
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          ...items.map((item) => Padding(
            padding: const EdgeInsets.only(bottom: 8),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  width: 6,
                  height: 6,
                  margin: const EdgeInsets.only(top: 6, right: 8),
                  decoration: BoxDecoration(
                    color: color,
                    shape: BoxShape.circle,
                  ),
                ),
                Expanded(
                  child: Text(
                    item,
                    style: Theme.of(context).textTheme.bodyMedium,
                  ),
                ),
              ],
            ),
          )),
        ],
      ),
    );
  }

  /// Share result
  void _shareResult() {
    // Implement sharing functionality
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Sharing functionality coming soon!')),
    );
  }

  /// Save to favorites
  void _saveToFavorites() {
    // Implement save to favorites functionality
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Plant saved to favorites!'),
        backgroundColor: Color(0xFF22c55e),
      ),
    );
  }
}
