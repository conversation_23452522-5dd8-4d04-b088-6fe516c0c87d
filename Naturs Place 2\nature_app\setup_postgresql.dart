import 'dart:io';
import 'lib/services/database_config_service.dart';

/// Interactive setup script for PostgreSQL configuration
void main() async {
  print('🔧 PostgreSQL Setup for Nature\'s Place\n');
  
  try {
    final configService = DatabaseConfigService();
    await configService.initialize();
    
    print('📋 Current Configuration:');
    final currentConfig = configService.getPostgreSQLConfig();
    print('   Host: ${currentConfig['host']}');
    print('   Port: ${currentConfig['port']}');
    print('   Database: ${currentConfig['database']}');
    print('   Username: ${currentConfig['username']}');
    print('   Password: ${currentConfig['password'].toString().replaceAll(RegExp(r'.'), '*')}');
    print('');
    
    print('Would you like to update the configuration? (y/n)');
    final updateConfig = stdin.readLineSync()?.toLowerCase() == 'y';
    
    if (updateConfig) {
      print('\n🔧 Enter new configuration:');
      
      stdout.write('Host (default: localhost): ');
      final host = stdin.readLineSync()?.trim();
      
      stdout.write('Port (default: 5432): ');
      final portInput = stdin.readLineSync()?.trim();
      final port = int.tryParse(portInput ?? '') ?? 5432;
      
      stdout.write('Database name (default: Natures Place): ');
      final database = stdin.readLineSync()?.trim();
      
      stdout.write('Username (default: postgres): ');
      final username = stdin.readLineSync()?.trim();
      
      stdout.write('Password: ');
      final password = stdin.readLineSync()?.trim();
      
      // Update configuration
      await configService.setPostgreSQLConfig(
        host: host?.isNotEmpty == true ? host! : 'localhost',
        port: port,
        database: database?.isNotEmpty == true ? database! : 'Natures Place',
        username: username?.isNotEmpty == true ? username! : 'postgres',
        password: password?.isNotEmpty == true ? password! : 'your_password_here',
      );
      
      print('\n✅ Configuration updated successfully!');
    }
    
    print('\n🔍 Testing connection...');

    // Test the connection
    // Note: In a real app, you'd import and test the PostgreSQL service here
    // For now, we'll just indicate the setup is complete
    
    print('✅ Setup complete!');
    print('\n📋 Next steps:');
    print('   1. Ensure PostgreSQL server is running');
    print('   2. Create the "Natures Place" database if it doesn\'t exist');
    print('   3. Run: dart test_postgresql_connection.dart');
    print('   4. If successful, your app is ready to use PostgreSQL!');
    
  } catch (e) {
    print('❌ Setup failed: $e');
    print('\n🔧 Please check:');
    print('   • PostgreSQL is installed');
    print('   • You have the correct permissions');
    print('   • Network connectivity');
  }
}

/// Helper function to create database if it doesn't exist
void printDatabaseCreationSQL() {
  print('\n📝 Database Creation SQL:');
  print('Copy and paste this into your PostgreSQL client:\n');
  print('''
CREATE DATABASE "Natures Place"
    WITH
    OWNER = postgres
    ENCODING = 'UTF8'
    LC_COLLATE = 'English_Canada.1252'
    LC_CTYPE = 'English_Canada.1252'
    LOCALE_PROVIDER = 'libc'
    TABLESPACE = pg_default
    CONNECTION LIMIT = -1
    IS_TEMPLATE = False;
''');
}
