import 'package:flutter/foundation.dart';

enum SubscriptionTier { free, premium, pro }

class AppState extends ChangeNotifier {
  int _currentIndex = 0;
  SubscriptionTier _subscriptionTier = SubscriptionTier.free;
  final List<String> _favorites = [];
  final List<ChatMessage> _chatMessages = [];

  // Scan tracking for free plan
  int _scansUsedThisMonth = 0;
  DateTime _lastScanResetDate = DateTime.now();
  static const int _freePlanScanLimit = 5;

  // AI assistant tracking for free plan
  int _aiQuestionsUsedThisMonth = 0;
  static const int _freePlanAiQuestionLimit = 3;

  // Getters
  int get currentIndex => _currentIndex;
  SubscriptionTier get subscriptionTier => _subscriptionTier;
  List<String> get favorites => _favorites;
  List<ChatMessage> get chatMessages => _chatMessages;

  // Scan tracking getters
  int get scansUsedThisMonth => _scansUsedThisMonth;
  int get freePlanScanLimit => _freePlanScanLimit;
  int get scansRemainingThisMonth => isPremium ? -1 : (_freePlanScanLimit - _scansUsedThisMonth).clamp(0, _freePlanScanLimit).toInt();
  bool get canScanThisMonth => isPremium || _scansUsedThisMonth < _freePlanScanLimit;

  // AI assistant tracking getters
  int get aiQuestionsUsedThisMonth => _aiQuestionsUsedThisMonth;
  int get freePlanAiQuestionLimit => _freePlanAiQuestionLimit;
  int get aiQuestionsRemainingThisMonth => isPremium ? -1 : (_freePlanAiQuestionLimit - _aiQuestionsUsedThisMonth).clamp(0, _freePlanAiQuestionLimit).toInt();
  bool get canAskAiThisMonth => isPremium || _aiQuestionsUsedThisMonth < _freePlanAiQuestionLimit;

  // Navigation
  void setCurrentIndex(int index) {
    _currentIndex = index;
    notifyListeners();
  }

  // Subscription
  void setSubscriptionTier(SubscriptionTier tier) {
    _subscriptionTier = tier;
    notifyListeners();
  }

  bool get isPremium => _subscriptionTier == SubscriptionTier.premium || _subscriptionTier == SubscriptionTier.pro;
  bool get isPro => _subscriptionTier == SubscriptionTier.pro;

  // Favorites
  void toggleFavorite(String plantId) {
    if (_favorites.contains(plantId)) {
      _favorites.remove(plantId);
    } else {
      _favorites.add(plantId);
    }
    notifyListeners();
  }

  bool isFavorite(String plantId) {
    return _favorites.contains(plantId);
  }

  void addFavorite(String plantId) {
    if (!_favorites.contains(plantId)) {
      _favorites.add(plantId);
      notifyListeners();
    }
  }

  void removeFavorite(String plantId) {
    if (_favorites.contains(plantId)) {
      _favorites.remove(plantId);
      notifyListeners();
    }
  }

  // Chat
  void addChatMessage(ChatMessage message) {
    _chatMessages.add(message);
    notifyListeners();
  }

  void clearChat() {
    _chatMessages.clear();
    notifyListeners();
  }

  // Scan tracking methods
  void useScan() {
    _checkAndResetMonthlyLimits();
    if (canScanThisMonth) {
      _scansUsedThisMonth++;
      notifyListeners();
    }
  }

  // AI assistant tracking methods
  void useAiQuestion() {
    _checkAndResetMonthlyLimits();
    if (canAskAiThisMonth) {
      _aiQuestionsUsedThisMonth++;
      notifyListeners();
    }
  }

  void _checkAndResetMonthlyLimits() {
    final now = DateTime.now();
    final currentMonth = DateTime(now.year, now.month);
    final lastResetMonth = DateTime(_lastScanResetDate.year, _lastScanResetDate.month);

    if (currentMonth.isAfter(lastResetMonth)) {
      _scansUsedThisMonth = 0;
      _aiQuestionsUsedThisMonth = 0;
      _lastScanResetDate = now;
    }
  }

  void resetMonthlyLimits() {
    _scansUsedThisMonth = 0;
    _aiQuestionsUsedThisMonth = 0;
    _lastScanResetDate = DateTime.now();
    notifyListeners();
  }
}

class ChatMessage {
  final String id;
  final String message;
  final bool isUser;
  final DateTime timestamp;

  ChatMessage({
    required this.id,
    required this.message,
    required this.isUser,
    required this.timestamp,
  });
}

// Enhanced Plant model for world-class encyclopedia
class Plant {
  final String id;
  final String name;
  final String scientificName;
  final String category;
  final String description;
  final String imageUrl;
  final List<String> benefits;
  final double rating;
  final int reviewCount;

  // Enhanced fields for comprehensive information
  final List<String> commonNames;
  final String origin;
  final String habitat;
  final List<String> activeCompounds;
  final List<String> uses;
  final List<String> preparationMethods;
  final List<String> precautions;
  final String dosage;
  final List<String> interactions;
  final String harvestingSeason;
  final String partUsed;
  final String traditionalUse;
  final String modernResearch;
  final String cultivation;
  final List<String> synonyms;
  final String family;
  final String energetics;
  final String taste;
  final List<String> contraindications;
  final bool isEndangered;
  final String conservationStatus;

  Plant({
    required this.id,
    required this.name,
    required this.scientificName,
    required this.category,
    required this.description,
    required this.imageUrl,
    required this.benefits,
    required this.rating,
    required this.reviewCount,
    this.commonNames = const [],
    this.origin = '',
    this.habitat = '',
    this.activeCompounds = const [],
    this.uses = const [],
    this.preparationMethods = const [],
    this.precautions = const [],
    this.dosage = '',
    this.interactions = const [],
    this.harvestingSeason = '',
    this.partUsed = '',
    this.traditionalUse = '',
    this.modernResearch = '',
    this.cultivation = '',
    this.synonyms = const [],
    this.family = '',
    this.energetics = '',
    this.taste = '',
    this.contraindications = const [],
    this.isEndangered = false,
    this.conservationStatus = 'Least Concern',
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'scientificName': scientificName,
      'category': category,
      'description': description,
      'imageUrl': imageUrl,
      'benefits': benefits,
      'rating': rating,
      'reviewCount': reviewCount,
      'commonNames': commonNames,
      'origin': origin,
      'habitat': habitat,
      'activeCompounds': activeCompounds,
      'uses': uses,
      'preparationMethods': preparationMethods,
      'precautions': precautions,
      'dosage': dosage,
      'interactions': interactions,
      'harvestingSeason': harvestingSeason,
      'partUsed': partUsed,
      'traditionalUse': traditionalUse,
      'modernResearch': modernResearch,
      'cultivation': cultivation,
      'synonyms': synonyms,
      'family': family,
      'energetics': energetics,
      'taste': taste,
      'contraindications': contraindications,
      'isEndangered': isEndangered,
      'conservationStatus': conservationStatus,
    };
  }
}

// Product model
class Product {
  final String id;
  final String name;
  final String description;
  final String imageUrl;
  final double price;
  final String category;
  final double rating;
  final int reviewCount;
  final String seller;

  Product({
    required this.id,
    required this.name,
    required this.description,
    required this.imageUrl,
    required this.price,
    required this.category,
    required this.rating,
    required this.reviewCount,
    required this.seller,
  });
}
