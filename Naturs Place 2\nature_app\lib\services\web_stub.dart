// Stub implementation for non-web platforms
// This file provides empty implementations of web-only functionality

class Document {
  Element? get head => null;
  bool get hidden => false;
  
  void addEventListener(String type, Function callback) {}
  Element? querySelector(String selector) => null;
  
  Stream<Event> get onContextMenu => Stream.empty();
  Stream<Event> get onVisibilityChange => Stream.empty();
}

class Window {
  Navigator get navigator => Navigator();
  Location get location => Location();
  
  void addEventListener(String type, Function callback) {}
  MediaQueryList matchMedia(String query) => MediaQueryList();
  
  Stream<Event> get onResize => Stream.empty();
  Stream<Event> get onOnline => Stream.empty();
  Stream<Event> get onOffline => Stream.empty();
}

class Navigator {
  String get userAgent => 'mobile';
  ServiceWorkerContainer? get serviceWorker => null;
}

class Location {
  String get href => '';
}

class Element {
  void setAttribute(String name, String value) {}
  void append(Element element) {}
  void setProperty(String property, String value) {}
  Element? querySelector(String selector) => null;
}

class MetaElement extends Element {
  String get httpEquiv => '';
  set httpEquiv(String value) {}
  
  String get content => '';
  set content(String value) {}
}

class LinkElement extends Element {
  String get rel => '';
  set rel(String value) {}
  
  String get href => '';
  set href(String value) {}
  
  String get as => '';
  set as(String value) {}
}

class InputElement extends Element {
  String? get value => null;
}

class Event {
  dynamic get target => null;
  void preventDefault() {}
}

class MediaQueryList {
  bool get matches => false;
}

class ServiceWorkerContainer {}

class Style {
  String get userSelect => '';
  set userSelect(String value) {}
  
  void setProperty(String property, String value) {}
}

// Global objects for non-web platforms
final document = Document();
final window = Window();
