import 'dart:io';

import 'package:shelf/shelf.dart';
import 'package:shelf/shelf_io.dart' as io;
import 'package:shelf_router/shelf_router.dart';
import 'package:dotenv/dotenv.dart' show load, env;

import 'routes/commission_routes.dart';
import 'services/database_service.dart';

void main() async {
  final envFile = File('mcp_servers/commission-server/.env');
  if (await envFile.exists()) {
    load();
  } else {
    print('Error: .env file not found!');
  }

  final dbService = DatabaseService();
  await dbService.createTableIfNotExists();

  final app = Router();

  // Mount commission routes
  app.mount('/api', commissionRoutes());

  // Basic health check
  app.get('/', (Request request) => Response.ok('Commission Server is running'));

  final port = int.parse(env['PORT'] ?? '8080');
  final handler = Pipeline().addHandler(app);

  io.serve(handler, InternetAddress.anyIPv4, port).then((server) {
    print('Server listening on port ${server.port}');
  });
}
