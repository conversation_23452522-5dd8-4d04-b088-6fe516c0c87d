import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';

/// High-performance optimized image widget with advanced caching
class OptimizedImage extends StatefulWidget {
  final String imageUrl;
  final double? width;
  final double? height;
  final BoxFit fit;
  final Widget? placeholder;
  final Widget? errorWidget;
  final bool enableHeroAnimation;
  final String? heroTag;
  final BorderRadius? borderRadius;
  final bool enableMemoryCache;
  final bool enableDiskCache;
  final Duration cacheDuration;
  final int? maxWidth;
  final int? maxHeight;
  final int imageQuality;

  const OptimizedImage({
    super.key,
    required this.imageUrl,
    this.width,
    this.height,
    this.fit = BoxFit.cover,
    this.placeholder,
    this.errorWidget,
    this.enableHeroAnimation = false,
    this.heroTag,
    this.borderRadius,
    this.enableMemoryCache = true,
    this.enableDiskCache = true,
    this.cacheDuration = const Duration(days: 7),
    this.maxWidth,
    this.maxHeight,
    this.imageQuality = 85,
  });

  @override
  State<OptimizedImage> createState() => _OptimizedImageState();
}

class _OptimizedImageState extends State<OptimizedImage>
    with AutomaticKeepAliveClientMixin {
  
  @override
  bool get wantKeepAlive => widget.enableMemoryCache;

  String get _cacheKey => _generateCacheKey(widget.imageUrl);

  String _generateCacheKey(String url) {
    // Simple hash-like key generation without crypto dependency
    return 'img_${url.hashCode.abs().toString()}';
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);

    Widget imageWidget = _buildImageWidget();

    // Apply border radius if specified
    if (widget.borderRadius != null) {
      imageWidget = ClipRRect(
        borderRadius: widget.borderRadius!,
        child: imageWidget,
      );
    }

    // Apply hero animation if enabled
    if (widget.enableHeroAnimation && widget.heroTag != null) {
      imageWidget = Hero(
        tag: widget.heroTag!,
        child: imageWidget,
      );
    }

    return imageWidget;
  }

  Widget _buildImageWidget() {
    return CachedNetworkImage(
      imageUrl: widget.imageUrl,
      width: widget.width,
      height: widget.height,
      fit: widget.fit,
      cacheKey: _cacheKey,
      memCacheWidth: widget.maxWidth,
      memCacheHeight: widget.maxHeight,
      maxWidthDiskCache: widget.maxWidth,
      maxHeightDiskCache: widget.maxHeight,
      placeholder: (context, url) => _buildPlaceholder(),
      errorWidget: (context, url, error) => _buildErrorWidget(),
      fadeInDuration: const Duration(milliseconds: 200),
      fadeOutDuration: const Duration(milliseconds: 100),
      useOldImageOnUrlChange: true,
      // Use default cache manager to avoid type compatibility issues
    );
  }

  Widget _buildPlaceholder() {
    if (widget.placeholder != null) {
      return widget.placeholder!;
    }

    return Container(
      width: widget.width,
      height: widget.height,
      color: Colors.grey[200],
      child: const Center(
        child: SizedBox(
          width: 24,
          height: 24,
          child: CircularProgressIndicator(
            strokeWidth: 2,
            valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF22c55e)),
          ),
        ),
      ),
    );
  }

  Widget _buildErrorWidget() {
    if (widget.errorWidget != null) {
      return widget.errorWidget!;
    }

    return Container(
      width: widget.width,
      height: widget.height,
      color: Colors.grey[200],
      child: const Center(
        child: Icon(
          Icons.image_not_supported_outlined,
          color: Colors.grey,
          size: 32,
        ),
      ),
    );
  }
}

/// Simple image cache utilities
class ImageCacheUtils {
  /// Clear all image caches
  static void clearAllCaches() {
    PaintingBinding.instance.imageCache.clear();
    PaintingBinding.instance.imageCache.clearLiveImages();
  }

  /// Get cache statistics
  static Map<String, dynamic> getCacheStats() {
    final imageCache = PaintingBinding.instance.imageCache;
    return {
      'currentSize': imageCache.currentSize,
      'maximumSize': imageCache.maximumSize,
      'currentSizeBytes': imageCache.currentSizeBytes,
      'maximumSizeBytes': imageCache.maximumSizeBytes,
    };
  }
}

/// Optimized image builder for lists and grids
class OptimizedImageBuilder {
  /// Build optimized image for list items
  static Widget buildListImage({
    required String imageUrl,
    double? width,
    double? height,
    BoxFit fit = BoxFit.cover,
    BorderRadius? borderRadius,
  }) {
    return OptimizedImage(
      imageUrl: imageUrl,
      width: width,
      height: height,
      fit: fit,
      borderRadius: borderRadius,
      maxWidth: 400, // Limit size for list items
      maxHeight: 400,
      imageQuality: 75, // Lower quality for list items
      enableMemoryCache: true,
      enableDiskCache: true,
    );
  }

  /// Build optimized image for detail views
  static Widget buildDetailImage({
    required String imageUrl,
    double? width,
    double? height,
    BoxFit fit = BoxFit.cover,
    BorderRadius? borderRadius,
    String? heroTag,
  }) {
    return OptimizedImage(
      imageUrl: imageUrl,
      width: width,
      height: height,
      fit: fit,
      borderRadius: borderRadius,
      enableHeroAnimation: heroTag != null,
      heroTag: heroTag,
      maxWidth: 1024, // Higher resolution for detail views
      maxHeight: 1024,
      imageQuality: 90, // Higher quality for detail views
      enableMemoryCache: true,
      enableDiskCache: true,
    );
  }

  /// Build optimized thumbnail image
  static Widget buildThumbnail({
    required String imageUrl,
    double size = 80,
    BorderRadius? borderRadius,
  }) {
    return OptimizedImage(
      imageUrl: imageUrl,
      width: size,
      height: size,
      fit: BoxFit.cover,
      borderRadius: borderRadius ?? BorderRadius.circular(8),
      maxWidth: 200, // Small size for thumbnails
      maxHeight: 200,
      imageQuality: 60, // Lower quality for thumbnails
      enableMemoryCache: true,
      enableDiskCache: true,
    );
  }

  /// Build optimized avatar image
  static Widget buildAvatar({
    required String imageUrl,
    double radius = 30,
  }) {
    return ClipOval(
      child: OptimizedImage(
        imageUrl: imageUrl,
        width: radius * 2,
        height: radius * 2,
        fit: BoxFit.cover,
        maxWidth: 200, // Small size for avatars
        maxHeight: 200,
        imageQuality: 70,
        enableMemoryCache: true,
        enableDiskCache: true,
        placeholder: Container(
          width: radius * 2,
          height: radius * 2,
          color: const Color(0xFF22c55e),
          child: const Icon(
            Icons.person,
            color: Colors.white,
          ),
        ),
      ),
    );
  }
}

/// Preloader for critical images
class ImagePreloader {
  static final Set<String> _preloadedImages = {};

  /// Preload images for faster display
  static Future<void> preloadImages(List<String> imageUrls) async {
    final futures = imageUrls
        .where((url) => !_preloadedImages.contains(url))
        .map((url) => _preloadImage(url));

    await Future.wait(futures, eagerError: false);
  }

  static Future<void> _preloadImage(String imageUrl) async {
    try {
      await precacheImage(NetworkImage(imageUrl), NavigationService.navigatorKey.currentContext!);
      _preloadedImages.add(imageUrl);
    } catch (e) {
      debugPrint('❌ Error preloading image $imageUrl: $e');
    }
  }

  /// Check if image is preloaded
  static bool isPreloaded(String imageUrl) {
    return _preloadedImages.contains(imageUrl);
  }

  /// Clear preloaded images cache
  static void clearPreloadedCache() {
    _preloadedImages.clear();
  }
}

/// Navigation service for global context access
class NavigationService {
  static final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();
}
