import 'package:camera/camera.dart';
import 'package:flutter/foundation.dart';
import 'package:image_picker/image_picker.dart';

// Cross-platform file wrapper
class CrossPlatformFile {
  final String path;
  final Uint8List? bytes;

  CrossPlatformFile({required this.path, this.bytes});

  static CrossPlatformFile? fromXFile(XFile? xFile) {
    if (xFile == null) return null;
    return CrossPlatformFile(path: xFile.path);
  }
}

class CameraService {
  static List<CameraDescription>? _cameras;
  static CameraController? _controller;
  static final ImagePicker _picker = ImagePicker();

  static Future<void> initializeCameras() async {
    try {
      if (!kIsWeb) {
        _cameras = await availableCameras();
      }
    } catch (e) {
      debugPrint('Error initializing cameras: $e');
    }
  }

  static Future<CameraController?> initializeCamera() async {
    if (_cameras == null || _cameras!.isEmpty) {
      await initializeCameras();
    }

    if (_cameras == null || _cameras!.isEmpty) {
      return null;
    }

    _controller = CameraController(
      _cameras!.first,
      ResolutionPreset.high,
      enableAudio: false,
    );

    try {
      await _controller!.initialize();
      return _controller;
    } catch (e) {
      debugPrint('Error initializing camera controller: $e');
      return null;
    }
  }

  static Future<CrossPlatformFile?> takePicture() async {
    if (_controller == null || !_controller!.value.isInitialized) {
      return null;
    }

    try {
      final XFile picture = await _controller!.takePicture();
      return CrossPlatformFile(path: picture.path);
    } catch (e) {
      debugPrint('Error taking picture: $e');
      return null;
    }
  }

  static Future<CrossPlatformFile?> pickImageFromGallery() async {
    try {
      final XFile? image = await _picker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 1024,
        maxHeight: 1024,
        imageQuality: 85,
      );

      return CrossPlatformFile.fromXFile(image);
    } catch (e) {
      debugPrint('Error picking image from gallery: $e');
      return null;
    }
  }

  static Future<CrossPlatformFile?> pickImageFromCamera() async {
    try {
      final XFile? image = await _picker.pickImage(
        source: ImageSource.camera,
        maxWidth: 1024,
        maxHeight: 1024,
        imageQuality: 85,
      );

      return CrossPlatformFile.fromXFile(image);
    } catch (e) {
      debugPrint('Error picking image from camera: $e');
      return null;
    }
  }

  static void dispose() {
    _controller?.dispose();
    _controller = null;
  }
}

// Plant identification result model
class PlantIdentificationResult {
  final String plantName;
  final String scientificName;
  final double confidence;
  final List<String> possibleDiseases;
  final List<String> careInstructions;
  final String description;

  PlantIdentificationResult({
    required this.plantName,
    required this.scientificName,
    required this.confidence,
    required this.possibleDiseases,
    required this.careInstructions,
    required this.description,
  });
}

// Mock plant identification service
class PlantIdentificationService {
  static Future<PlantIdentificationResult?> identifyPlant(CrossPlatformFile imageFile) async {
    // Simulate API call delay
    await Future.delayed(const Duration(seconds: 2));

    // Mock results based on common plants
    final mockResults = [
      PlantIdentificationResult(
        plantName: 'Aloe Vera',
        scientificName: 'Aloe barbadensis miller',
        confidence: 0.92,
        possibleDiseases: [],
        careInstructions: [
          'Water sparingly, only when soil is completely dry',
          'Place in bright, indirect sunlight',
          'Use well-draining soil',
          'Keep in temperatures between 60-75°F',
        ],
        description: 'A succulent plant known for its healing gel and easy care requirements.',
      ),
      PlantIdentificationResult(
        plantName: 'Snake Plant',
        scientificName: 'Sansevieria trifasciata',
        confidence: 0.88,
        possibleDiseases: ['Root rot'],
        careInstructions: [
          'Water every 2-3 weeks',
          'Tolerates low light conditions',
          'Use well-draining soil',
          'Wipe leaves occasionally to remove dust',
        ],
        description: 'A hardy plant that purifies air and tolerates neglect.',
      ),
      PlantIdentificationResult(
        plantName: 'Pothos',
        scientificName: 'Epipremnum aureum',
        confidence: 0.85,
        possibleDiseases: ['Leaf spot', 'Root rot'],
        careInstructions: [
          'Water when top inch of soil is dry',
          'Thrives in medium to bright indirect light',
          'Trim regularly to maintain shape',
          'Can be propagated in water',
        ],
        description: 'A popular trailing houseplant that\'s easy to grow and propagate.',
      ),
    ];

    // Return a random result for demo purposes
    mockResults.shuffle();
    return mockResults.first;
  }
}
