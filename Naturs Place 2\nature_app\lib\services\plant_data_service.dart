import 'dart:convert';
import 'dart:math';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../providers/app_state.dart';
import '../data/world_plant_encyclopedia.dart';

/// Plant Data Service for managing plant database and daily updates
class PlantDataService extends ChangeNotifier {
  static final PlantDataService _instance = PlantDataService._internal();
  factory PlantDataService() => _instance;
  PlantDataService._internal();

  List<Plant> _allPlants = [];
  List<Plant> _dailyAddedPlants = [];
  DateTime? _lastUpdateDate;
  bool _isLoading = false;
  int _totalPlantsAdded = 0;

  // Getters
  List<Plant> get allPlants => _allPlants;
  List<Plant> get dailyAddedPlants => _dailyAddedPlants;
  DateTime? get lastUpdateDate => _lastUpdateDate;
  bool get isLoading => _isLoading;
  int get totalPlantsAdded => _totalPlantsAdded;

  /// Initialize the plant data service
  Future<void> initialize() async {
    _isLoading = true;
    notifyListeners();

    try {
      // Load base plants from encyclopedia
      _allPlants = List.from(WorldPlantEncyclopedia.getAllPlants());
      
      // Load saved data from local storage
      await _loadSavedData();
      
      // Check if we need to add daily plants
      await _checkAndAddDailyPlants();
      
    } catch (e) {
      debugPrint('Error initializing PlantDataService: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  /// Load saved data from SharedPreferences
  Future<void> _loadSavedData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      // Load last update date
      final lastUpdateString = prefs.getString('last_plant_update_date');
      if (lastUpdateString != null) {
        _lastUpdateDate = DateTime.parse(lastUpdateString);
      }
      
      // Load total plants added
      _totalPlantsAdded = prefs.getInt('total_plants_added') ?? 0;
      
      // Load daily added plants
      final dailyPlantsJson = prefs.getString('daily_added_plants');
      if (dailyPlantsJson != null) {
        final List<dynamic> dailyPlantsData = jsonDecode(dailyPlantsJson);
        _dailyAddedPlants = dailyPlantsData.map((data) => _plantFromJson(data)).toList();
        
        // Add daily plants to main list if not already present
        for (final plant in _dailyAddedPlants) {
          if (!_allPlants.any((p) => p.id == plant.id)) {
            _allPlants.add(plant);
          }
        }
      }
      
    } catch (e) {
      debugPrint('Error loading saved plant data: $e');
    }
  }

  /// Save data to SharedPreferences
  Future<void> _saveData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      // Save last update date
      if (_lastUpdateDate != null) {
        await prefs.setString('last_plant_update_date', _lastUpdateDate!.toIso8601String());
      }
      
      // Save total plants added
      await prefs.setInt('total_plants_added', _totalPlantsAdded);
      
      // Save daily added plants
      final dailyPlantsJson = jsonEncode(_dailyAddedPlants.map((plant) => _plantToJson(plant)).toList());
      await prefs.setString('daily_added_plants', dailyPlantsJson);
      
    } catch (e) {
      debugPrint('Error saving plant data: $e');
    }
  }

  /// Check if we need to add daily plants and add them if necessary
  Future<void> checkAndAddDailyPlants() async {
    await _checkAndAddDailyPlants();
  }

  Future<void> _checkAndAddDailyPlants() async {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    
    // Check if we've already updated today
    if (_lastUpdateDate != null) {
      final lastUpdate = DateTime(_lastUpdateDate!.year, _lastUpdateDate!.month, _lastUpdateDate!.day);
      if (lastUpdate.isAtSameMomentAs(today)) {
        debugPrint('Plants already updated today');
        return;
      }
    }
    
    // Add daily plants
    await _addDailyHealingPlants();
    
    // Update last update date
    _lastUpdateDate = now;
    await _saveData();
    
    notifyListeners();
  }

  /// Add daily healing plants to the database
  Future<void> _addDailyHealingPlants() async {
    try {
      debugPrint('Adding daily healing plants...');
      
      // Get new healing plants for today
      final newPlants = _generateDailyHealingPlants();
      
      // Add to daily list
      _dailyAddedPlants.clear();
      _dailyAddedPlants.addAll(newPlants);
      
      // Add to main list if not already present
      for (final plant in newPlants) {
        if (!_allPlants.any((p) => p.id == plant.id)) {
          _allPlants.add(plant);
          _totalPlantsAdded++;
        }
      }
      
      debugPrint('Added ${newPlants.length} new healing plants');
      
    } catch (e) {
      debugPrint('Error adding daily healing plants: $e');
    }
  }

  /// Generate daily healing plants based on current date
  List<Plant> _generateDailyHealingPlants() {
    final now = DateTime.now();
    final dayOfYear = now.difference(DateTime(now.year, 1, 1)).inDays;
    final random = Random(dayOfYear); // Seed with day of year for consistency
    
    // Select 3-5 plants per day
    final plantsToAdd = 3 + random.nextInt(3);
    final newPlants = <Plant>[];
    
    for (int i = 0; i < plantsToAdd; i++) {
      final plant = _generateHealingPlant(dayOfYear, i);
      newPlants.add(plant);
    }
    
    return newPlants;
  }

  /// Generate a specific healing plant for the day
  Plant _generateHealingPlant(int dayOfYear, int plantIndex) {
    final random = Random(dayOfYear + plantIndex);
    final plantId = 'daily_${dayOfYear}_$plantIndex';
    
    // Healing plant templates
    final healingPlants = _getHealingPlantTemplates();
    final template = healingPlants[random.nextInt(healingPlants.length)];
    
    return Plant(
      id: plantId,
      name: template['name']!,
      scientificName: template['scientificName']!,
      category: template['category']!,
      description: template['description']!,
      imageUrl: template['imageUrl']!,
      benefits: List<String>.from(template['benefits']!),
      rating: 4.0 + random.nextDouble() * 1.0, // 4.0 - 5.0
      reviewCount: 50 + random.nextInt(500),
      commonNames: List<String>.from(template['commonNames'] ?? []),
      origin: template['origin'] ?? '',
      habitat: template['habitat'] ?? '',
      activeCompounds: List<String>.from(template['activeCompounds'] ?? []),
      uses: List<String>.from(template['uses'] ?? []),
      preparationMethods: List<String>.from(template['preparationMethods'] ?? []),
      precautions: List<String>.from(template['precautions'] ?? []),
      dosage: template['dosage'] ?? '',
      interactions: List<String>.from(template['interactions'] ?? []),
      harvestingSeason: template['harvestingSeason'] ?? '',
      partUsed: template['partUsed'] ?? '',
      traditionalUse: template['traditionalUse'] ?? '',
      modernResearch: template['modernResearch'] ?? '',
      cultivation: template['cultivation'] ?? '',
      synonyms: List<String>.from(template['synonyms'] ?? []),
      family: template['family'] ?? '',
      energetics: template['energetics'] ?? '',
      taste: template['taste'] ?? '',
      contraindications: List<String>.from(template['contraindications'] ?? []),
      isEndangered: template['isEndangered'] ?? false,
    );
  }

  /// Get all plants including daily additions
  List<Plant> getAllPlants() {
    return _allPlants;
  }

  /// Get plants added today
  List<Plant> getTodaysPlants() {
    return _dailyAddedPlants;
  }

  /// Force refresh daily plants (for testing)
  Future<void> forceRefreshDailyPlants() async {
    _lastUpdateDate = null;
    await _checkAndAddDailyPlants();
  }

  /// Convert Plant to JSON
  Map<String, dynamic> _plantToJson(Plant plant) {
    return {
      'id': plant.id,
      'name': plant.name,
      'scientificName': plant.scientificName,
      'category': plant.category,
      'description': plant.description,
      'imageUrl': plant.imageUrl,
      'benefits': plant.benefits,
      'rating': plant.rating,
      'reviewCount': plant.reviewCount,
      'commonNames': plant.commonNames,
      'origin': plant.origin,
      'habitat': plant.habitat,
      'activeCompounds': plant.activeCompounds,
      'uses': plant.uses,
      'preparationMethods': plant.preparationMethods,
      'precautions': plant.precautions,
      'dosage': plant.dosage,
      'interactions': plant.interactions,
      'harvestingSeason': plant.harvestingSeason,
      'partUsed': plant.partUsed,
      'traditionalUse': plant.traditionalUse,
      'modernResearch': plant.modernResearch,
      'cultivation': plant.cultivation,
      'synonyms': plant.synonyms,
      'family': plant.family,
      'energetics': plant.energetics,
      'taste': plant.taste,
      'contraindications': plant.contraindications,
      'isEndangered': plant.isEndangered,
    };
  }

  /// Convert JSON to Plant
  Plant _plantFromJson(Map<String, dynamic> json) {
    return Plant(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      scientificName: json['scientificName'] ?? '',
      category: json['category'] ?? '',
      description: json['description'] ?? '',
      imageUrl: json['imageUrl'] ?? '',
      benefits: List<String>.from(json['benefits'] ?? []),
      rating: (json['rating'] ?? 0.0).toDouble(),
      reviewCount: json['reviewCount'] ?? 0,
      commonNames: List<String>.from(json['commonNames'] ?? []),
      origin: json['origin'] ?? '',
      habitat: json['habitat'] ?? '',
      activeCompounds: List<String>.from(json['activeCompounds'] ?? []),
      uses: List<String>.from(json['uses'] ?? []),
      preparationMethods: List<String>.from(json['preparationMethods'] ?? []),
      precautions: List<String>.from(json['precautions'] ?? []),
      dosage: json['dosage'] ?? '',
      interactions: List<String>.from(json['interactions'] ?? []),
      harvestingSeason: json['harvestingSeason'] ?? '',
      partUsed: json['partUsed'] ?? '',
      traditionalUse: json['traditionalUse'] ?? '',
      modernResearch: json['modernResearch'] ?? '',
      cultivation: json['cultivation'] ?? '',
      synonyms: List<String>.from(json['synonyms'] ?? []),
      family: json['family'] ?? '',
      energetics: json['energetics'] ?? '',
      taste: json['taste'] ?? '',
      contraindications: List<String>.from(json['contraindications'] ?? []),
      isEndangered: json['isEndangered'] ?? false,
    );
  }

  /// Get healing plant templates for daily generation
  List<Map<String, dynamic>> _getHealingPlantTemplates() {
    return [
      // Adaptogenic Herbs
      {
        'name': 'Rhodiola Rosea',
        'scientificName': 'Rhodiola rosea',
        'category': 'Adaptogenic Herb',
        'description': 'Known as "Golden Root," this powerful adaptogen helps combat fatigue, stress, and depression while enhancing mental performance and physical endurance.',
        'imageUrl': 'https://cdn.pixabay.com/photo/2019/07/15/12/35/rhodiola-4339297_1280.jpg',
        'benefits': ['Stress Relief', 'Energy Enhancement', 'Mental Clarity', 'Fatigue Reduction', 'Mood Support'],
        'commonNames': ['Golden Root', 'Arctic Root', 'Rose Root'],
        'origin': 'Arctic regions of Europe, Asia, and North America',
        'habitat': 'High altitude mountainous regions',
        'activeCompounds': ['Rosavin', 'Salidroside', 'Tyrosol'],
        'uses': ['Stress management', 'Athletic performance', 'Mental fatigue', 'Depression support'],
        'preparationMethods': ['Standardized extract', 'Tea', 'Tincture', 'Capsules'],
        'precautions': ['May cause jitteriness in sensitive individuals'],
        'dosage': '200-400mg daily of standardized extract',
        'partUsed': 'Root',
        'traditionalUse': 'Used by Vikings for strength and endurance',
        'family': 'Crassulaceae',
        'energetics': 'Warming, stimulating',
        'taste': 'Bitter, astringent',
      },

      {
        'name': 'Holy Basil',
        'scientificName': 'Ocimum tenuiflorum',
        'category': 'Sacred Adaptogen',
        'description': 'Revered as "The Queen of Herbs" in Ayurveda, Holy Basil is a powerful adaptogen that supports stress resilience, immune function, and spiritual well-being.',
        'imageUrl': 'https://cdn.pixabay.com/photo/2018/10/15/19/52/tulsi-3749933_1280.jpg',
        'benefits': ['Stress Relief', 'Immune Support', 'Respiratory Health', 'Blood Sugar Balance', 'Spiritual Clarity'],
        'commonNames': ['Tulsi', 'Sacred Basil', 'Queen of Herbs'],
        'origin': 'Indian subcontinent',
        'habitat': 'Tropical and subtropical regions',
        'activeCompounds': ['Eugenol', 'Rosmarinic acid', 'Oleanolic acid'],
        'uses': ['Daily stress support', 'Respiratory conditions', 'Immune enhancement', 'Meditation aid'],
        'preparationMethods': ['Fresh leaves', 'Tea', 'Tincture', 'Essential oil'],
        'dosage': '2-3 cups of tea daily or 300-600mg extract',
        'partUsed': 'Leaves, flowers',
        'traditionalUse': 'Sacred plant in Hindu tradition, used for purification',
        'family': 'Lamiaceae',
        'energetics': 'Warming, uplifting',
        'taste': 'Spicy, clove-like',
      },

      // Immune Supporting Herbs
      {
        'name': 'Astragalus',
        'scientificName': 'Astragalus membranaceus',
        'category': 'Immune Tonic',
        'description': 'A cornerstone herb in Traditional Chinese Medicine, Astragalus is renowned for its immune-strengthening properties and ability to enhance vitality and longevity.',
        'imageUrl': 'https://cdn.pixabay.com/photo/2019/09/26/18/25/astragalus-4506026_1280.jpg',
        'benefits': ['Immune Support', 'Energy Enhancement', 'Longevity', 'Heart Health', 'Kidney Support'],
        'commonNames': ['Huang Qi', 'Milk Vetch', 'Mongolian Milk Vetch'],
        'origin': 'Northern China, Mongolia, Korea',
        'habitat': 'Grasslands and mountainous regions',
        'activeCompounds': ['Astragalosides', 'Polysaccharides', 'Flavonoids'],
        'uses': ['Immune system support', 'Chronic fatigue', 'Seasonal wellness', 'Recovery support'],
        'preparationMethods': ['Decoction', 'Powder', 'Standardized extract', 'Soup ingredient'],
        'dosage': '9-30g daily in decoction or 500mg extract 2-3 times daily',
        'partUsed': 'Root',
        'traditionalUse': 'Used for over 2000 years in TCM as a superior tonic',
        'family': 'Fabaceae',
        'energetics': 'Warming, tonifying',
        'taste': 'Sweet, slightly warm',
      },

      {
        'name': 'Reishi Mushroom',
        'scientificName': 'Ganoderma lucidum',
        'category': 'Medicinal Mushroom',
        'description': 'Known as the "Mushroom of Immortality," Reishi is a powerful adaptogen that supports immune function, stress resilience, and spiritual well-being.',
        'imageUrl': 'https://cdn.pixabay.com/photo/2019/11/07/21/32/reishi-4611052_1280.jpg',
        'benefits': ['Immune Support', 'Stress Relief', 'Sleep Quality', 'Liver Health', 'Longevity'],
        'commonNames': ['Lingzhi', 'Mushroom of Immortality', 'Varnish Shelf'],
        'origin': 'Asia, now cultivated worldwide',
        'habitat': 'Hardwood trees in humid environments',
        'activeCompounds': ['Triterpenes', 'Beta-glucans', 'Peptidoglycans'],
        'uses': ['Immune modulation', 'Stress management', 'Sleep support', 'Liver protection'],
        'preparationMethods': ['Powder', 'Extract', 'Tea', 'Capsules'],
        'dosage': '1-3g daily of extract or 6-12g of powder',
        'partUsed': 'Fruiting body',
        'traditionalUse': 'Prized by Chinese emperors for immortality',
        'family': 'Ganodermataceae',
        'energetics': 'Neutral, calming',
        'taste': 'Bitter, woody',
      },

      // Digestive Herbs
      {
        'name': 'Slippery Elm',
        'scientificName': 'Ulmus rubra',
        'category': 'Digestive Soother',
        'description': 'A gentle yet powerful digestive remedy, Slippery Elm provides soothing mucilage that coats and protects the digestive tract while promoting healing.',
        'imageUrl': 'https://cdn.pixabay.com/photo/2018/05/15/21/24/elm-3404651_1280.jpg',
        'benefits': ['Digestive Health', 'Soothing', 'Wound Healing', 'Throat Comfort', 'Gut Lining Support'],
        'commonNames': ['Red Elm', 'Moose Elm', 'Indian Elm'],
        'origin': 'Eastern North America',
        'habitat': 'Rich, moist soils in deciduous forests',
        'activeCompounds': ['Mucilage', 'Tannins', 'Starch'],
        'uses': ['Digestive upset', 'Sore throat', 'Wound healing', 'IBS support'],
        'preparationMethods': ['Powder mixed with water', 'Gruel', 'Poultice', 'Lozenges'],
        'dosage': '1-2 teaspoons powder in water, 2-3 times daily',
        'partUsed': 'Inner bark',
        'traditionalUse': 'Native American remedy for digestive and respiratory issues',
        'family': 'Ulmaceae',
        'energetics': 'Cool, moist',
        'taste': 'Bland, mucilaginous',
      },

      // Cardiovascular Herbs
      {
        'name': 'Hawthorn',
        'scientificName': 'Crataegus monogyna',
        'category': 'Heart Tonic',
        'description': 'A beloved heart herb used for centuries, Hawthorn gently supports cardiovascular health, circulation, and emotional well-being with its heart-opening properties.',
        'imageUrl': 'https://cdn.pixabay.com/photo/2019/05/20/09/42/hawthorn-4216068_1280.jpg',
        'benefits': ['Heart Health', 'Circulation', 'Blood Pressure Support', 'Emotional Balance', 'Antioxidant'],
        'commonNames': ['May Bush', 'Whitethorn', 'Haw'],
        'origin': 'Europe, Asia, North America',
        'habitat': 'Hedgerows, woodland edges, hillsides',
        'activeCompounds': ['Oligomeric proanthocyanidins', 'Flavonoids', 'Triterpenes'],
        'uses': ['Cardiovascular support', 'Mild hypertension', 'Heart palpitations', 'Circulation'],
        'preparationMethods': ['Tea', 'Tincture', 'Standardized extract', 'Syrup'],
        'dosage': '160-900mg standardized extract daily',
        'partUsed': 'Flowers, leaves, berries',
        'traditionalUse': 'Symbol of protection and heart healing in European folklore',
        'family': 'Rosaceae',
        'energetics': 'Cool, sweet',
        'taste': 'Sweet, slightly astringent',
      },

      // Nervous System Herbs
      {
        'name': 'Lemon Balm',
        'scientificName': 'Melissa officinalis',
        'category': 'Nervine Herb',
        'description': 'A gentle yet effective nervine, Lemon Balm calms the mind, soothes the nervous system, and uplifts the spirit with its delightful lemony fragrance.',
        'imageUrl': 'https://cdn.pixabay.com/photo/2018/07/01/20/01/lemon-balm-3510072_1280.jpg',
        'benefits': ['Calming', 'Mood Support', 'Digestive Aid', 'Sleep Quality', 'Cognitive Function'],
        'commonNames': ['Bee Balm', 'Sweet Balm', 'Honey Plant'],
        'origin': 'Mediterranean region',
        'habitat': 'Gardens, meadows, woodland edges',
        'activeCompounds': ['Rosmarinic acid', 'Citronellal', 'Geraniol'],
        'uses': ['Anxiety relief', 'Insomnia', 'Digestive upset', 'Cold sores', 'ADHD support'],
        'preparationMethods': ['Fresh tea', 'Tincture', 'Essential oil', 'Glycerite'],
        'dosage': '2-4g dried herb as tea, 3 times daily',
        'partUsed': 'Leaves',
        'traditionalUse': 'Used by ancient Greeks for melancholy and heart conditions',
        'family': 'Lamiaceae',
        'energetics': 'Cool, moist',
        'taste': 'Lemony, sweet',
      },

      // Respiratory Herbs
      {
        'name': 'Mullein',
        'scientificName': 'Verbascum thapsus',
        'category': 'Respiratory Herb',
        'description': 'A gentle yet powerful respiratory ally, Mullein soothes irritated airways, supports lung health, and has been treasured for centuries as a smoking cessation aid.',
        'imageUrl': 'https://cdn.pixabay.com/photo/2019/07/15/16/14/mullein-4339758_1280.jpg',
        'benefits': ['Respiratory Health', 'Cough Relief', 'Lung Support', 'Soothing', 'Expectorant'],
        'commonNames': ['Great Mullein', 'Velvet Plant', 'Hag\'s Taper'],
        'origin': 'Europe, Asia, North Africa',
        'habitat': 'Disturbed soils, roadsides, fields',
        'activeCompounds': ['Mucilage', 'Saponins', 'Flavonoids'],
        'uses': ['Cough', 'Bronchitis', 'Asthma support', 'Ear infections', 'Smoking cessation'],
        'preparationMethods': ['Tea', 'Tincture', 'Oil infusion', 'Smoking blend'],
        'dosage': '1-2 teaspoons dried leaves as tea, 3 times daily',
        'partUsed': 'Leaves, flowers',
        'traditionalUse': 'Used as torches and for respiratory ailments in ancient times',
        'family': 'Scrophulariaceae',
        'energetics': 'Cool, moist',
        'taste': 'Bland, slightly bitter',
      },

      // Women's Health Herbs
      {
        'name': 'Red Clover',
        'scientificName': 'Trifolium pratense',
        'category': 'Women\'s Health Herb',
        'description': 'A gentle hormonal balancer, Red Clover supports women\'s health throughout all life stages with its phytoestrogen content and blood-purifying properties.',
        'imageUrl': 'https://cdn.pixabay.com/photo/2018/06/09/16/39/red-clover-3464988_1280.jpg',
        'benefits': ['Hormonal Balance', 'Menopause Support', 'Skin Health', 'Blood Purifying', 'Bone Health'],
        'commonNames': ['Purple Clover', 'Trefoil', 'Wild Clover'],
        'origin': 'Europe, Asia, North Africa',
        'habitat': 'Meadows, fields, roadsides',
        'activeCompounds': ['Isoflavones', 'Coumarins', 'Salicylates'],
        'uses': ['Menopause symptoms', 'PMS', 'Skin conditions', 'Osteoporosis prevention'],
        'preparationMethods': ['Tea', 'Tincture', 'Capsules', 'Topical preparations'],
        'dosage': '2-4g dried flowers as tea, 2-3 times daily',
        'partUsed': 'Flowers',
        'traditionalUse': 'Used by Native Americans for women\'s health and purification',
        'family': 'Fabaceae',
        'energetics': 'Cool, sweet',
        'taste': 'Sweet, grassy',
        'contraindications': ['Pregnancy', 'Hormone-sensitive conditions'],
      },

      // Liver Support Herbs
      {
        'name': 'Dandelion',
        'scientificName': 'Taraxacum officinale',
        'category': 'Liver Tonic',
        'description': 'Often dismissed as a weed, Dandelion is actually a powerful liver tonic and digestive bitter that supports detoxification and overall vitality.',
        'imageUrl': 'https://cdn.pixabay.com/photo/2018/04/15/18/05/dandelion-3322447_1280.jpg',
        'benefits': ['Liver Support', 'Detoxification', 'Digestive Health', 'Diuretic', 'Nutrient Dense'],
        'commonNames': ['Lion\'s Tooth', 'Blowball', 'Piss-a-bed'],
        'origin': 'Europe, now worldwide',
        'habitat': 'Lawns, fields, roadsides, disturbed soils',
        'activeCompounds': ['Taraxasterol', 'Inulin', 'Potassium'],
        'uses': ['Liver congestion', 'Digestive sluggishness', 'Water retention', 'Skin conditions'],
        'preparationMethods': ['Fresh leaves in salad', 'Root decoction', 'Tincture', 'Coffee substitute'],
        'dosage': '4-10g dried root as decoction, 2-3 times daily',
        'partUsed': 'Leaves, root, flowers',
        'traditionalUse': 'Used in European folk medicine for liver and kidney ailments',
        'family': 'Asteraceae',
        'energetics': 'Cool, bitter',
        'taste': 'Bitter, slightly sweet',
      },
    ];
  }
}
