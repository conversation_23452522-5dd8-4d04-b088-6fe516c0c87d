import 'package:flutter/foundation.dart';
import 'lib/services/postgres_service.dart';
import 'lib/services/database_config_service.dart';
import 'lib/models/commission_models.dart';

/// Test script to verify PostgreSQL connection and functionality
void main() async {
  print('🔍 Testing PostgreSQL Connection...\n');

  try {
    // Initialize services
    print('📱 Initializing PostgreSQL service...');
    final postgresService = PostgreSQLService();
    final configService = DatabaseConfigService();
    
    await configService.initialize();
    
    // Test connection
    print('🔗 Testing database connection...');
    final isConnected = await postgresService.testConnection();
    
    if (isConnected) {
      print('✅ PostgreSQL connection successful!\n');
      
      // Initialize the service (creates tables)
      print('🗄️ Initializing database tables...');
      await postgresService.initialize();
      print('✅ Database tables created successfully!\n');
      
      // Test inserting a sample transaction
      print('💰 Testing transaction insertion...');
      final testTransaction = Transaction(
        id: 'test_txn_${DateTime.now().millisecondsSinceEpoch}',
        orderId: 'test_order_001',
        customerId: 'test_customer_001',
        vendorId: 'test_vendor_001',
        productId: 'test_product_001',
        productName: 'Test Healing Plant',
        category: ProductCategory.herbs,
        productPrice: 29.99,
        quantity: 1,
        subtotal: 29.99,
        tax: 2.40,
        shipping: 5.99,
        total: 38.38,
        status: TransactionStatus.completed,
        createdAt: DateTime.now(),
        completedAt: DateTime.now(),
        metadata: {"test": true},
      );
      
      await postgresService.insertTransaction(testTransaction);
      print('✅ Test transaction inserted successfully!\n');
      
      // Test inserting a sample commission
      print('💼 Testing commission insertion...');
      final testCommission = Commission(
        id: 'test_comm_${DateTime.now().millisecondsSinceEpoch}',
        transactionId: testTransaction.id,
        partnerId: 'test_vendor_001',
        partnerType: 'vendor',
        category: ProductCategory.herbs,
        saleAmount: 29.99,
        commissionRate: 15.0,
        commissionAmount: 4.50,
        totalAmount: 4.50,
        appRevenue: 25.49,
        status: CommissionStatus.pending,
        createdAt: DateTime.now(),
        metadata: {"test": true},
      );
      
      await postgresService.insertCommission(testCommission);
      print('✅ Test commission inserted successfully!\n');
      
      print('🎉 All PostgreSQL tests passed!');
      print('📊 Your database is ready for production use.');
      
    } else {
      print('❌ PostgreSQL connection failed!');
      print('📋 Please check:');
      print('   • PostgreSQL server is running');
      print('   • Database "Natures Place" exists');
      print('   • Connection credentials are correct');
      print('   • Network connectivity');
    }
    
    // Close connection
    await postgresService.close();
    print('\n🔌 PostgreSQL connection closed.');
    
  } catch (e) {
    print('❌ Test failed with error: $e');
    print('\n🔧 Troubleshooting:');
    print('   • Verify PostgreSQL is installed and running');
    print('   • Check database credentials in environment variables');
    print('   • Ensure database "Natures Place" exists');
    print('   • Check firewall and network settings');
  }
}
