import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:provider/provider.dart';
import '../providers/app_state.dart';
import '../widgets/plant_card.dart';
import '../widgets/optimized_list_view.dart';
import '../data/plant_dataset.dart';
import 'plant_detail_screen.dart';

class EncyclopediaScreen extends StatefulWidget {
  const EncyclopediaScreen({super.key});

  @override
  State<EncyclopediaScreen> createState() => _EncyclopediaScreenState();
}

class _EncyclopediaScreenState extends State<EncyclopediaScreen> with SingleTickerProviderStateMixin {
  TabController? _tabController;
  String searchQuery = '';
  String selectedCategory = 'All';

  List<String> get categories => PlantDataset.getAllBenefits();
  List<Plant> get plants => PlantDataset.getAllPlants();

  List<Plant> get filteredPlants {
    var filtered = plants;
    
    // Filter by search query
    if (searchQuery.isNotEmpty) {
      filtered = filtered.where((plant) =>
          plant.name.toLowerCase().contains(searchQuery.toLowerCase()) ||
          plant.scientificName.toLowerCase().contains(searchQuery.toLowerCase()) ||
          plant.description.toLowerCase().contains(searchQuery.toLowerCase())).toList();
    }
    
    // Filter by category
    if (selectedCategory != 'All') {
      filtered = filtered.where((plant) =>
          plant.category == selectedCategory ||
          plant.benefits.contains(selectedCategory)).toList();
    }
    
    return filtered;
  }

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // Return loading indicator if TabController is not initialized yet
    if (_tabController == null) {
      return const Scaffold(
        body: Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('Plant Encyclopedia'),
        actions: [
          IconButton(
            icon: const Icon(Icons.favorite_border),
            onPressed: () {
              _tabController?.animateTo(1); // Switch to favorites tab
            },
          ),
        ],
        bottom: TabBar(
          controller: _tabController!,
          tabs: const [
            Tab(icon: Icon(Icons.grid_view), text: 'All Plants'),
            Tab(icon: Icon(Icons.favorite), text: 'Favorites'),
            Tab(icon: Icon(Icons.category), text: 'Categories'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController!,
        children: [
          _buildAllPlantsTab(),
          _buildFavoritesTab(),
          _buildCategoriesTab(),
        ],
      ),
    );
  }

  Widget _buildAllPlantsTab() {
    return Column(
      children: [
        // Search bar
        Padding(
          padding: const EdgeInsets.all(16.0),
          child: TextField(
            decoration: const InputDecoration(
              hintText: 'Search plants...',
              prefixIcon: Icon(Icons.search),
              border: OutlineInputBorder(),
            ),
            onChanged: (value) {
              setState(() {
                searchQuery = value;
              });
            },
          ),
        ),

        // Category filter
        Container(
          height: 50,
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            itemCount: categories.length,
            itemBuilder: (context, index) {
              final category = categories[index];
              final isSelected = category == selectedCategory;

              return Padding(
                padding: const EdgeInsets.only(right: 8),
                child: FilterChip(
                  label: Text(category),
                  selected: isSelected,
                  onSelected: (selected) {
                    setState(() {
                      selectedCategory = category;
                    });
                  },
                  backgroundColor: Colors.grey[100],
                  selectedColor: const Color(0xFF22c55e),
                  labelStyle: TextStyle(
                    color: isSelected ? Colors.white : Colors.grey[800],
                    fontWeight: isSelected ? FontWeight.w500 : FontWeight.normal,
                  ),
                ),
              );
            },
          ),
        ),

        const SizedBox(height: 16),

        // Plants grid
        Expanded(
          child: filteredPlants.isEmpty
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const FaIcon(
                        FontAwesomeIcons.leaf,
                        size: 64,
                        color: Colors.grey,
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'No plants found',
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          color: Colors.grey,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Try adjusting your search or filters',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Colors.grey,
                        ),
                      ),
                    ],
                  ),
                )
              : OptimizedGridView<Plant>(
                  items: filteredPlants,
                  padding: const EdgeInsets.all(16),
                  crossAxisCount: 2,
                  childAspectRatio: 0.8,
                  crossAxisSpacing: 16,
                  mainAxisSpacing: 16,
                  itemBuilder: (context, plant, index) {
                    return PlantCard(
                      plant: plant,
                      onTap: () {
                        Navigator.of(context).push(
                          MaterialPageRoute(
                            builder: (context) => PlantDetailScreen(plant: plant),
                          ),
                        );
                      },
                    );
                  },
                ),
        ),
      ],
    );
  }

  Widget _buildFavoritesTab() {
    return Consumer<AppState>(
      builder: (context, appState, child) {
        final favoritePlants = plants.where((plant) => appState.isFavorite(plant.id)).toList();

        return favoritePlants.isEmpty
            ? Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const FaIcon(
                      FontAwesomeIcons.heart,
                      size: 64,
                      color: Colors.grey,
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'No favorite plants yet',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        color: Colors.grey,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Tap the heart icon on plants to add them to favorites',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Colors.grey,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              )
            : OptimizedGridView<Plant>(
                items: favoritePlants,
                padding: const EdgeInsets.all(16),
                crossAxisCount: 2,
                childAspectRatio: 0.8,
                crossAxisSpacing: 16,
                mainAxisSpacing: 16,
                itemBuilder: (context, plant, index) {
                  return PlantCard(
                    plant: plant,
                    onTap: () {
                      Navigator.of(context).push(
                        MaterialPageRoute(
                          builder: (context) => PlantDetailScreen(plant: plant),
                        ),
                      );
                    },
                  );
                },
              );
      },
    );
  }

  Widget _buildCategoriesTab() {
    final plantCategories = PlantDataset.getAllCategories();

    return OptimizedListView<String>(
      items: plantCategories,
      padding: const EdgeInsets.all(16),
      itemBuilder: (context, category, index) {
        final categoryPlants = plants.where((plant) => plant.category == category).toList();

        return Card(
          margin: const EdgeInsets.only(bottom: 16),
          child: ExpansionTile(
            leading: const FaIcon(
              FontAwesomeIcons.leaf,
              color: Color(0xFF22c55e),
            ),
            title: Text(
              category,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            subtitle: Text('${categoryPlants.length} plants'),
            children: [
              SizedBox(
                height: 200,
                child: ListView.builder(
                  scrollDirection: Axis.horizontal,
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  itemCount: categoryPlants.length,
                  itemBuilder: (context, plantIndex) {
                    final plant = categoryPlants[plantIndex];
                    return Container(
                      width: 150,
                      margin: const EdgeInsets.only(right: 12),
                      child: PlantCard(
                        plant: plant,
                        onTap: () {
                          Navigator.of(context).push(
                            MaterialPageRoute(
                              builder: (context) => PlantDetailScreen(plant: plant),
                            ),
                          );
                        },
                      ),
                    );
                  },
                ),
              ),
              const SizedBox(height: 16),
            ],
          ),
        );
      },
    );
  }
}
