import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// Database configuration service for managing connection settings
class DatabaseConfigService {
  static final DatabaseConfigService _instance = DatabaseConfigService._internal();
  factory DatabaseConfigService() => _instance;
  DatabaseConfigService._internal();

  SharedPreferences? _prefs;

  /// Initialize the configuration service
  Future<void> initialize() async {
    _prefs = await SharedPreferences.getInstance();
    debugPrint('✅ Database Config Service initialized');
  }

  /// Get PostgreSQL connection configuration
  Map<String, dynamic> getPostgreSQLConfig() {
    return {
      'host': _getConfigValue('postgres_host', 'localhost'),
      'port': int.tryParse(_getConfigValue('postgres_port', '5432')) ?? 5432,
      'database': _getConfigValue('postgres_database', 'Natures Place'),
      'username': _getConfigValue('postgres_username', 'postgres'),
      'password': _getConfigValue('postgres_password', ''),
      'ssl_mode': _getConfigValue('postgres_ssl_mode', 'prefer'),
      'connect_timeout': int.tryParse(_getConfigValue('postgres_connect_timeout', '10')) ?? 10,
      'query_timeout': int.tryParse(_getConfigValue('postgres_query_timeout', '30')) ?? 30,
    };
  }

  /// Set PostgreSQL connection configuration
  Future<void> setPostgreSQLConfig({
    String? host,
    int? port,
    String? database,
    String? username,
    String? password,
    String? sslMode,
    int? connectTimeout,
    int? queryTimeout,
  }) async {
    if (host != null) await _setConfigValue('postgres_host', host);
    if (port != null) await _setConfigValue('postgres_port', port.toString());
    if (database != null) await _setConfigValue('postgres_database', database);
    if (username != null) await _setConfigValue('postgres_username', username);
    if (password != null) await _setConfigValue('postgres_password', password);
    if (sslMode != null) await _setConfigValue('postgres_ssl_mode', sslMode);
    if (connectTimeout != null) await _setConfigValue('postgres_connect_timeout', connectTimeout.toString());
    if (queryTimeout != null) await _setConfigValue('postgres_query_timeout', queryTimeout.toString());
    
    debugPrint('✅ PostgreSQL configuration updated');
  }

  /// Get database sync settings
  Map<String, dynamic> getSyncSettings() {
    return {
      'auto_sync_enabled': _getBoolConfigValue('auto_sync_enabled', true),
      'sync_interval_minutes': int.tryParse(_getConfigValue('sync_interval_minutes', '15')) ?? 15,
      'sync_on_startup': _getBoolConfigValue('sync_on_startup', true),
      'sync_on_transaction': _getBoolConfigValue('sync_on_transaction', true),
      'offline_mode': _getBoolConfigValue('offline_mode', false),
      'retry_failed_syncs': _getBoolConfigValue('retry_failed_syncs', true),
      'max_retry_attempts': int.tryParse(_getConfigValue('max_retry_attempts', '3')) ?? 3,
    };
  }

  /// Set database sync settings
  Future<void> setSyncSettings({
    bool? autoSyncEnabled,
    int? syncIntervalMinutes,
    bool? syncOnStartup,
    bool? syncOnTransaction,
    bool? offlineMode,
    bool? retryFailedSyncs,
    int? maxRetryAttempts,
  }) async {
    if (autoSyncEnabled != null) await _setBoolConfigValue('auto_sync_enabled', autoSyncEnabled);
    if (syncIntervalMinutes != null) await _setConfigValue('sync_interval_minutes', syncIntervalMinutes.toString());
    if (syncOnStartup != null) await _setBoolConfigValue('sync_on_startup', syncOnStartup);
    if (syncOnTransaction != null) await _setBoolConfigValue('sync_on_transaction', syncOnTransaction);
    if (offlineMode != null) await _setBoolConfigValue('offline_mode', offlineMode);
    if (retryFailedSyncs != null) await _setBoolConfigValue('retry_failed_syncs', retryFailedSyncs);
    if (maxRetryAttempts != null) await _setConfigValue('max_retry_attempts', maxRetryAttempts.toString());
    
    debugPrint('✅ Sync settings updated');
  }

  /// Get database performance settings
  Map<String, dynamic> getPerformanceSettings() {
    return {
      'connection_pool_size': int.tryParse(_getConfigValue('connection_pool_size', '5')) ?? 5,
      'query_cache_enabled': _getBoolConfigValue('query_cache_enabled', true),
      'cache_size_mb': int.tryParse(_getConfigValue('cache_size_mb', '50')) ?? 50,
      'batch_insert_size': int.tryParse(_getConfigValue('batch_insert_size', '100')) ?? 100,
      'enable_compression': _getBoolConfigValue('enable_compression', true),
      'log_slow_queries': _getBoolConfigValue('log_slow_queries', true),
      'slow_query_threshold_ms': int.tryParse(_getConfigValue('slow_query_threshold_ms', '1000')) ?? 1000,
    };
  }

  /// Set database performance settings
  Future<void> setPerformanceSettings({
    int? connectionPoolSize,
    bool? queryCacheEnabled,
    int? cacheSizeMb,
    int? batchInsertSize,
    bool? enableCompression,
    bool? logSlowQueries,
    int? slowQueryThresholdMs,
  }) async {
    if (connectionPoolSize != null) await _setConfigValue('connection_pool_size', connectionPoolSize.toString());
    if (queryCacheEnabled != null) await _setBoolConfigValue('query_cache_enabled', queryCacheEnabled);
    if (cacheSizeMb != null) await _setConfigValue('cache_size_mb', cacheSizeMb.toString());
    if (batchInsertSize != null) await _setConfigValue('batch_insert_size', batchInsertSize.toString());
    if (enableCompression != null) await _setBoolConfigValue('enable_compression', enableCompression);
    if (logSlowQueries != null) await _setBoolConfigValue('log_slow_queries', logSlowQueries);
    if (slowQueryThresholdMs != null) await _setConfigValue('slow_query_threshold_ms', slowQueryThresholdMs.toString());
    
    debugPrint('✅ Performance settings updated');
  }

  /// Check if PostgreSQL is configured
  bool get isPostgreSQLConfigured {
    final config = getPostgreSQLConfig();
    return config['host'] != 'localhost' || config['password'].toString().isNotEmpty;
  }

  /// Check if offline mode is enabled
  bool get isOfflineModeEnabled {
    return getSyncSettings()['offline_mode'] as bool;
  }

  /// Get environment-based configuration
  Map<String, dynamic> getEnvironmentConfig() {
    return {
      'postgres_host': Platform.environment['POSTGRES_HOST'] ?? 'localhost',
      'postgres_port': int.tryParse(Platform.environment['POSTGRES_PORT'] ?? '5432') ?? 5432,
      'postgres_database': Platform.environment['POSTGRES_DB'] ?? 'Natures Place',
      'postgres_username': Platform.environment['POSTGRES_USER'] ?? 'postgres',
      'postgres_password': Platform.environment['POSTGRES_PASSWORD'] ?? '',
      'postgres_ssl_mode': Platform.environment['POSTGRES_SSL_MODE'] ?? 'prefer',
    };
  }

  /// Load configuration from environment variables
  Future<void> loadFromEnvironment() async {
    final envConfig = getEnvironmentConfig();
    
    await setPostgreSQLConfig(
      host: envConfig['postgres_host'],
      port: envConfig['postgres_port'],
      database: envConfig['postgres_database'],
      username: envConfig['postgres_username'],
      password: envConfig['postgres_password'],
      sslMode: envConfig['postgres_ssl_mode'],
    );
    
    debugPrint('✅ Configuration loaded from environment variables');
  }

  /// Export configuration as environment variables format
  String exportAsEnvironmentVariables() {
    final config = getPostgreSQLConfig();
    final syncSettings = getSyncSettings();
    
    return '''
# PostgreSQL Configuration
export POSTGRES_HOST="${config['host']}"
export POSTGRES_PORT="${config['port']}"
export POSTGRES_DB="${config['database']}"
export POSTGRES_USER="${config['username']}"
export POSTGRES_PASSWORD="${config['password']}"
export POSTGRES_SSL_MODE="${config['ssl_mode']}"

# Sync Settings
export AUTO_SYNC_ENABLED="${syncSettings['auto_sync_enabled']}"
export SYNC_INTERVAL_MINUTES="${syncSettings['sync_interval_minutes']}"
export OFFLINE_MODE="${syncSettings['offline_mode']}"
''';
  }

  /// Reset configuration to defaults
  Future<void> resetToDefaults() async {
    if (_prefs == null) return;
    
    final keys = _prefs!.getKeys().where((key) => key.startsWith('postgres_') || key.startsWith('sync_') || key.startsWith('performance_'));
    for (final key in keys) {
      await _prefs!.remove(key);
    }
    
    debugPrint('✅ Configuration reset to defaults');
  }

  /// Validate PostgreSQL configuration
  Map<String, dynamic> validateConfiguration() {
    final config = getPostgreSQLConfig();
    final issues = <String>[];
    
    if (config['host'].toString().isEmpty) {
      issues.add('Host is required');
    }
    
    if (config['port'] < 1 || config['port'] > 65535) {
      issues.add('Port must be between 1 and 65535');
    }
    
    if (config['database'].toString().isEmpty) {
      issues.add('Database name is required');
    }
    
    if (config['username'].toString().isEmpty) {
      issues.add('Username is required');
    }
    
    return {
      'isValid': issues.isEmpty,
      'issues': issues,
      'config': config,
    };
  }

  /// Get configuration value with fallback
  String _getConfigValue(String key, String defaultValue) {
    if (_prefs == null) return defaultValue;
    return _prefs!.getString(key) ?? defaultValue;
  }

  /// Set configuration value
  Future<void> _setConfigValue(String key, String value) async {
    if (_prefs == null) return;
    await _prefs!.setString(key, value);
  }

  /// Get boolean configuration value with fallback
  bool _getBoolConfigValue(String key, bool defaultValue) {
    if (_prefs == null) return defaultValue;
    return _prefs!.getBool(key) ?? defaultValue;
  }

  /// Set boolean configuration value
  Future<void> _setBoolConfigValue(String key, bool value) async {
    if (_prefs == null) return;
    await _prefs!.setBool(key, value);
  }
}
