<!DOCTYPE html>
<html lang="en">
<head>
  <!--
    If you are serving your web app in a path other than the root, change the
    href value below to reflect the base path you are serving from.

    The path provided below has to start and end with a slash "/" in order for
    it to work correctly.

    For more details:
    * https://developer.mozilla.org/en-US/docs/Web/HTML/Element/base

    This is a placeholder for base href that will be replaced by the value of
    the `--base-href` argument provided to `flutter build`.
  -->
  <base href="$FLUTTER_BASE_HREF">

  <meta charset="UTF-8">
  <meta content="IE=Edge" http-equiv="X-UA-Compatible">
  <meta name="description" content="Nature's Place - Your comprehensive healing plants and herbs companion app with AI-powered plant identification, marketplace, and holistic health guidance.">
  <meta name="keywords" content="healing plants, herbs, natural medicine, plant identification, holistic health, naturopathy, herbal remedies">
  <meta name="author" content="Nature's Place">
  <meta name="robots" content="index, follow">

  <!-- Security Headers -->
  <meta http-equiv="Content-Security-Policy" content="default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self' https:;">
  <meta http-equiv="X-Content-Type-Options" content="nosniff">
  <meta http-equiv="X-Frame-Options" content="DENY">
  <meta http-equiv="X-XSS-Protection" content="1; mode=block">
  <meta http-equiv="Referrer-Policy" content="strict-origin-when-cross-origin">

  <!-- Mobile-first viewport (same as mobile app) -->
  <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=yes">

  <!-- Progressive Web App (PWA) meta tags -->
  <meta name="mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
  <meta name="apple-mobile-web-app-title" content="Nature's Place">
  <meta name="application-name" content="Nature's Place">
  <meta name="msapplication-TileColor" content="#22c55e">
  <meta name="theme-color" content="#22c55e">

  <!-- Icons and favicons -->
  <link rel="apple-touch-icon" href="icons/Icon-192.png">
  <link rel="icon" type="image/png" sizes="32x32" href="favicon.png"/>
  <link rel="icon" type="image/png" sizes="16x16" href="favicon.png"/>
  <link rel="shortcut icon" href="favicon.png">

  <!-- Open Graph meta tags for social sharing -->
  <meta property="og:title" content="Nature's Place - Healing Plants & Herbs App">
  <meta property="og:description" content="Discover the power of healing plants and herbs with AI-powered identification, comprehensive encyclopedia, and holistic health guidance.">
  <meta property="og:type" content="website">
  <meta property="og:image" content="icons/Icon-512.png">
  <meta property="og:site_name" content="Nature's Place">

  <!-- Twitter Card meta tags -->
  <meta name="twitter:card" content="summary_large_image">
  <meta name="twitter:title" content="Nature's Place - Healing Plants & Herbs App">
  <meta name="twitter:description" content="Your comprehensive companion for healing plants and natural medicine.">
  <meta name="twitter:image" content="icons/Icon-512.png">

  <title>Nature's Place - Healing Plants & Herbs Companion</title>
  <link rel="manifest" href="manifest.json">

  <!-- Mobile-first layout preservation -->
  <link rel="stylesheet" href="mobile_layout.css">

  <!-- Preload critical resources -->
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="dns-prefetch" href="https://fonts.gstatic.com">
</head>
<body>
  <!-- Simple Flutter app container (mobile-first approach) -->
  <script src="flutter_bootstrap.js" async></script>

  <!-- Minimal security and PWA enhancements -->
  <script>
    // Security: Prevent clickjacking
    if (window.top !== window.self) {
      window.top.location = window.self.location;
    }

    // Service Worker registration for PWA capabilities
    if ('serviceWorker' in navigator) {
      window.addEventListener('load', function() {
        navigator.serviceWorker.register('/flutter_service_worker.js')
          .then(function(registration) {
            console.log('ServiceWorker registration successful');
          })
          .catch(function(error) {
            console.log('ServiceWorker registration failed');
          });
      });
    }
  </script>
</body>
</html>
