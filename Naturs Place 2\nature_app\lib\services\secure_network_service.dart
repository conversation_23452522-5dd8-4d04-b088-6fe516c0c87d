import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
// Note: crypto package is optional dependency
// import 'package:crypto/crypto.dart';
import 'performance_optimization_service.dart';

/// Secure and optimized network service with caching and retry logic
class SecureNetworkService {
  static final SecureNetworkService _instance = SecureNetworkService._internal();
  factory SecureNetworkService() => _instance;
  SecureNetworkService._internal();

  // Security configurations
  static const Duration _defaultTimeout = Duration(seconds: 30);
  static const int _maxRetries = 3;
  static const Duration _retryDelay = Duration(seconds: 1);
  static const int _maxConcurrentRequests = 10;

  // Performance configurations
  static const Duration _defaultCacheDuration = Duration(hours: 1);

  // Request management
  final Map<String, Completer<http.Response>> _pendingRequests = {};
  int _activeRequests = 0;

  // Security headers
  static const Map<String, String> _securityHeaders = {
    'X-Content-Type-Options': 'nosniff',
    'X-Frame-Options': 'DENY',
    'X-XSS-Protection': '1; mode=block',
    'Strict-Transport-Security': 'max-age=31536000; includeSubDomains',
    'Content-Security-Policy': "default-src 'self'",
  };

  /// Initialize the network service
  static Future<void> initialize() async {
    try {
      // Configure HTTP client settings
      HttpOverrides.global = _SecureHttpOverrides();
      debugPrint('✅ Secure network service initialized');
    } catch (e) {
      debugPrint('❌ Error initializing network service: $e');
    }
  }

  /// Secure GET request with caching and optimization
  Future<Map<String, dynamic>?> get(
    String url, {
    Map<String, String>? headers,
    Duration? timeout,
    Duration? cacheDuration,
    bool useCache = true,
    bool retryOnFailure = true,
  }) async {
    return await _makeRequest(
      'GET',
      url,
      headers: headers,
      timeout: timeout,
      cacheDuration: cacheDuration,
      useCache: useCache,
      retryOnFailure: retryOnFailure,
    );
  }

  /// Secure POST request with validation
  Future<Map<String, dynamic>?> post(
    String url, {
    Map<String, dynamic>? body,
    Map<String, String>? headers,
    Duration? timeout,
    bool retryOnFailure = false,
  }) async {
    return await _makeRequest(
      'POST',
      url,
      body: body,
      headers: headers,
      timeout: timeout,
      retryOnFailure: retryOnFailure,
    );
  }

  /// Secure PUT request
  Future<Map<String, dynamic>?> put(
    String url, {
    Map<String, dynamic>? body,
    Map<String, String>? headers,
    Duration? timeout,
    bool retryOnFailure = false,
  }) async {
    return await _makeRequest(
      'PUT',
      url,
      body: body,
      headers: headers,
      timeout: timeout,
      retryOnFailure: retryOnFailure,
    );
  }

  /// Secure DELETE request
  Future<Map<String, dynamic>?> delete(
    String url, {
    Map<String, String>? headers,
    Duration? timeout,
    bool retryOnFailure = false,
  }) async {
    return await _makeRequest(
      'DELETE',
      url,
      headers: headers,
      timeout: timeout,
      retryOnFailure: retryOnFailure,
    );
  }

  /// Core request method with security and performance optimizations
  Future<Map<String, dynamic>?> _makeRequest(
    String method,
    String url, {
    Map<String, dynamic>? body,
    Map<String, String>? headers,
    Duration? timeout,
    Duration? cacheDuration,
    bool useCache = false,
    bool retryOnFailure = true,
  }) async {
    // Validate URL
    if (!_isValidUrl(url)) {
      throw ArgumentError('Invalid URL: $url');
    }

    // Generate cache key
    final cacheKey = _generateCacheKey(method, url, body);

    // Check cache for GET requests
    if (method == 'GET' && useCache) {
      final cached = await PerformanceOptimizationService.getCachedData<Map<String, dynamic>>(cacheKey);
      if (cached != null) {
        return cached;
      }
    }

    // Check for duplicate requests
    if (_pendingRequests.containsKey(cacheKey)) {
      final response = await _pendingRequests[cacheKey]!.future;
      return _parseResponse(response);
    }

    // Rate limiting
    if (_activeRequests >= _maxConcurrentRequests) {
      await _waitForAvailableSlot();
    }

    // Create completer for duplicate request prevention
    final completer = Completer<http.Response>();
    _pendingRequests[cacheKey] = completer;
    _activeRequests++;

    try {
      final response = await _executeRequest(
        method,
        url,
        body: body,
        headers: headers,
        timeout: timeout ?? _defaultTimeout,
        retryOnFailure: retryOnFailure,
      );

      completer.complete(response);
      
      final result = _parseResponse(response);

      // Cache successful GET responses
      if (method == 'GET' && useCache && response.statusCode == 200 && result != null) {
        await PerformanceOptimizationService.cacheData(
          cacheKey,
          result,
          expiry: cacheDuration ?? _defaultCacheDuration,
        );
      }

      return result;
    } catch (e) {
      completer.completeError(e);
      rethrow;
    } finally {
      _pendingRequests.remove(cacheKey);
      _activeRequests--;
    }
  }

  /// Execute HTTP request with retry logic
  Future<http.Response> _executeRequest(
    String method,
    String url, {
    Map<String, dynamic>? body,
    Map<String, String>? headers,
    required Duration timeout,
    bool retryOnFailure = true,
  }) async {
    final uri = Uri.parse(url);
    final requestHeaders = _buildHeaders(headers);
    
    int attempts = 0;
    Exception? lastException;

    while (attempts < (retryOnFailure ? _maxRetries : 1)) {
      attempts++;
      
      try {
        http.Response response;
        
        switch (method.toUpperCase()) {
          case 'GET':
            response = await http.get(uri, headers: requestHeaders).timeout(timeout);
            break;
          case 'POST':
            response = await http.post(
              uri,
              headers: requestHeaders,
              body: body != null ? jsonEncode(body) : null,
            ).timeout(timeout);
            break;
          case 'PUT':
            response = await http.put(
              uri,
              headers: requestHeaders,
              body: body != null ? jsonEncode(body) : null,
            ).timeout(timeout);
            break;
          case 'DELETE':
            response = await http.delete(uri, headers: requestHeaders).timeout(timeout);
            break;
          default:
            throw ArgumentError('Unsupported HTTP method: $method');
        }

        // Validate response
        _validateResponse(response);
        return response;
        
      } on TimeoutException catch (e) {
        lastException = e;
        debugPrint('⏰ Request timeout (attempt $attempts): $url');
      } on SocketException catch (e) {
        lastException = e;
        debugPrint('🌐 Network error (attempt $attempts): $url - ${e.message}');
      } on HttpException catch (e) {
        lastException = e;
        debugPrint('🔗 HTTP error (attempt $attempts): $url - ${e.message}');
      } catch (e) {
        lastException = Exception('Unexpected error: $e');
        debugPrint('❌ Unexpected error (attempt $attempts): $url - $e');
      }

      // Wait before retry
      if (attempts < _maxRetries && retryOnFailure) {
        await Future.delayed(_retryDelay * attempts);
      }
    }

    throw lastException ?? Exception('Request failed after $attempts attempts');
  }

  /// Build secure headers
  Map<String, String> _buildHeaders(Map<String, String>? customHeaders) {
    final headers = <String, String>{
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      'User-Agent': 'NaturesPlace/1.0',
      ..._securityHeaders,
    };

    if (customHeaders != null) {
      headers.addAll(customHeaders);
    }

    return headers;
  }

  /// Validate URL for security
  bool _isValidUrl(String url) {
    try {
      final uri = Uri.parse(url);
      
      // Only allow HTTPS in production
      if (kReleaseMode && uri.scheme != 'https') {
        return false;
      }
      
      // Allow HTTP only for localhost in debug mode
      if (kDebugMode && uri.scheme == 'http' && !uri.host.contains('localhost')) {
        return false;
      }
      
      // Block suspicious domains
      final blockedDomains = ['malicious.com', 'phishing.net'];
      if (blockedDomains.any((domain) => uri.host.contains(domain))) {
        return false;
      }
      
      return true;
    } catch (e) {
      return false;
    }
  }

  /// Validate response for security
  void _validateResponse(http.Response response) {
    // Check for suspicious response headers
    final contentType = response.headers['content-type'] ?? '';
    if (!contentType.contains('application/json') && !contentType.contains('text/')) {
      throw HttpException('Suspicious content type: $contentType');
    }

    // Check response size
    if (response.bodyBytes.length > 10 * 1024 * 1024) { // 10MB limit
      throw HttpException('Response too large: ${response.bodyBytes.length} bytes');
    }
  }

  /// Parse response safely
  Map<String, dynamic>? _parseResponse(http.Response response) {
    try {
      if (response.statusCode >= 200 && response.statusCode < 300) {
        if (response.body.isEmpty) return null;
        
        final decoded = jsonDecode(response.body);
        if (decoded is Map<String, dynamic>) {
          return decoded;
        } else {
          return {'data': decoded};
        }
      } else {
        throw HttpException('HTTP ${response.statusCode}: ${response.reasonPhrase}');
      }
    } catch (e) {
      debugPrint('❌ Error parsing response: $e');
      throw FormatException('Invalid response format');
    }
  }

  /// Generate cache key
  String _generateCacheKey(String method, String url, Map<String, dynamic>? body) {
    final content = '$method:$url:${body != null ? jsonEncode(body) : ''}';
    // Simple hash generation without crypto package
    final hash = content.hashCode.abs().toString();
    return 'net_${hash.substring(0, hash.length > 16 ? 16 : hash.length)}';
  }

  /// Wait for available request slot
  Future<void> _waitForAvailableSlot() async {
    while (_activeRequests >= _maxConcurrentRequests) {
      await Future.delayed(const Duration(milliseconds: 100));
    }
  }

  /// Clear all network caches
  Future<void> clearCache() async {
    await PerformanceOptimizationService.clearAllCache();
  }

  /// Get network statistics
  Map<String, dynamic> getNetworkStats() {
    return {
      'activeRequests': _activeRequests,
      'pendingRequests': _pendingRequests.length,
      'maxConcurrentRequests': _maxConcurrentRequests,
    };
  }
}

/// Custom HTTP overrides for security
class _SecureHttpOverrides extends HttpOverrides {
  @override
  HttpClient createHttpClient(SecurityContext? context) {
    final client = super.createHttpClient(context);
    
    // Security configurations
    client.badCertificateCallback = (cert, host, port) {
      // Only allow bad certificates in debug mode for localhost
      return kDebugMode && (host == 'localhost' || host == '127.0.0.1');
    };
    
    // Set timeouts
    client.connectionTimeout = const Duration(seconds: 15);
    client.idleTimeout = const Duration(seconds: 30);
    
    return client;
  }
}
