import 'package:flutter/foundation.dart';
import '../models/user_models.dart';
import 'product_upload_service.dart';
import 'postgres_service.dart';

/// Service for managing vendor marketing features based on tier
class VendorMarketingService {
  static final VendorMarketingService _instance = VendorMarketingService._internal();
  factory VendorMarketingService() => _instance;
  VendorMarketingService._internal();

  final ProductUploadService _uploadService = ProductUploadService();
  final PostgreSQLService _postgresService = PostgreSQLService();

  /// Initialize marketing service
  Future<void> initialize() async {
    await _postgresService.initialize();
    debugPrint('✅ Vendor Marketing Service initialized');
  }

  /// Create homepage banner campaign (Premium+ only)
  Future<MarketingCampaignResult> createHomepageBanner({
    required String vendorId,
    required String title,
    required String description,
    required String imageUrl,
    required String targetUrl,
    required DateTime startDate,
    required DateTime endDate,
  }) async {
    try {
      final marketingOptions = await _uploadService.getVendorMarketingOptions(vendorId);
      
      if (!marketingOptions.homepageBannerAccess) {
        return MarketingCampaignResult(
          success: false,
          error: 'Homepage banner access requires Premium tier or higher',
          requiredTier: VendorTier.premium,
        );
      }

      // Check if vendor already has an active banner
      final activeBanners = await _getActiveBanners(vendorId);
      if (activeBanners.isNotEmpty) {
        return MarketingCampaignResult(
          success: false,
          error: 'You already have an active homepage banner. Only one banner allowed per vendor.',
        );
      }

      final campaignId = await _postgresService.createMarketingCampaign(
        vendorId: vendorId,
        type: 'homepage_banner',
        title: title,
        description: description,
        imageUrl: imageUrl,
        targetUrl: targetUrl,
        startDate: startDate,
        endDate: endDate,
      );

      return MarketingCampaignResult(
        success: true,
        campaignId: campaignId,
        message: 'Homepage banner campaign created successfully',
      );
    } catch (e) {
      debugPrint('❌ Failed to create homepage banner: $e');
      return MarketingCampaignResult(
        success: false,
        error: 'Failed to create banner campaign',
      );
    }
  }

  /// Create email marketing campaign (Standard+ only)
  Future<MarketingCampaignResult> createEmailCampaign({
    required String vendorId,
    required String subject,
    required String content,
    required List<String> targetSegments,
    required DateTime scheduledDate,
  }) async {
    try {
      final marketingOptions = await _uploadService.getVendorMarketingOptions(vendorId);
      
      if (!marketingOptions.emailCampaignAccess) {
        return MarketingCampaignResult(
          success: false,
          error: 'Email campaigns require Standard tier or higher',
          requiredTier: VendorTier.standard,
        );
      }

      // Check monthly email limit based on tier
      final monthlyEmailLimit = _getMonthlyEmailLimit(marketingOptions.tier);
      final currentMonthEmails = await _getMonthlyEmailCount(vendorId);
      
      if (currentMonthEmails >= monthlyEmailLimit) {
        return MarketingCampaignResult(
          success: false,
          error: 'Monthly email limit reached ($currentMonthEmails/$monthlyEmailLimit)',
        );
      }

      final campaignId = await _postgresService.createEmailCampaign(
        vendorId: vendorId,
        subject: subject,
        content: content,
        targetSegments: targetSegments,
        scheduledDate: scheduledDate,
      );

      return MarketingCampaignResult(
        success: true,
        campaignId: campaignId,
        message: 'Email campaign scheduled successfully',
      );
    } catch (e) {
      debugPrint('❌ Failed to create email campaign: $e');
      return MarketingCampaignResult(
        success: false,
        error: 'Failed to create email campaign',
      );
    }
  }

  /// Apply product badge (tier-dependent)
  Future<BadgeApplicationResult> applyProductBadge({
    required String vendorId,
    required String productId,
    required String badgeType,
  }) async {
    try {
      final marketingOptions = await _uploadService.getVendorMarketingOptions(vendorId);
      
      if (!marketingOptions.availableBadges.contains(badgeType)) {
        return BadgeApplicationResult(
          success: false,
          error: 'Badge "$badgeType" not available in your tier',
          availableBadges: marketingOptions.availableBadges,
        );
      }

      // Check badge limits per tier
      final badgeLimit = _getBadgeLimit(marketingOptions.tier);
      final currentBadges = await _getProductBadgeCount(vendorId, productId);
      
      if (currentBadges >= badgeLimit) {
        return BadgeApplicationResult(
          success: false,
          error: 'Badge limit reached for this product ($currentBadges/$badgeLimit)',
        );
      }

      await _postgresService.applyProductBadge(productId, badgeType);

      return BadgeApplicationResult(
        success: true,
        message: 'Badge "$badgeType" applied successfully',
      );
    } catch (e) {
      debugPrint('❌ Failed to apply product badge: $e');
      return BadgeApplicationResult(
        success: false,
        error: 'Failed to apply badge',
      );
    }
  }

  /// Get vendor marketing dashboard data
  Future<MarketingDashboard> getMarketingDashboard(String vendorId) async {
    try {
      final marketingOptions = await _uploadService.getVendorMarketingOptions(vendorId);
      
      return MarketingDashboard(
        vendorId: vendorId,
        tier: marketingOptions.tier,
        featuredProducts: await _getFeaturedProducts(vendorId),
        activeCampaigns: await _getActiveCampaigns(vendorId),
        campaignPerformance: await _getCampaignPerformance(vendorId),
        availableFeatures: _getAvailableMarketingFeatures(marketingOptions.tier),
        usageStats: await _getMarketingUsageStats(vendorId),
        upgradeRecommendations: _getUpgradeRecommendations(marketingOptions.tier),
      );
    } catch (e) {
      debugPrint('❌ Failed to get marketing dashboard: $e');
      return MarketingDashboard.empty(vendorId);
    }
  }

  /// Get SEO optimization suggestions (Standard+ only)
  Future<SEOSuggestions> getSEOSuggestions(String vendorId, String productId) async {
    try {
      final marketingOptions = await _uploadService.getVendorMarketingOptions(vendorId);
      
      if (!marketingOptions.seoOptimizationAccess) {
        return SEOSuggestions(
          available: false,
          message: 'SEO optimization requires Standard tier or higher',
          requiredTier: VendorTier.standard,
        );
      }

      final suggestions = await _generateSEOSuggestions(productId);
      
      return SEOSuggestions(
        available: true,
        suggestions: suggestions,
        seoScore: await _calculateSEOScore(productId),
      );
    } catch (e) {
      debugPrint('❌ Failed to get SEO suggestions: $e');
      return SEOSuggestions(available: false, message: 'Failed to load SEO suggestions');
    }
  }

  // Private helper methods

  int _getMonthlyEmailLimit(VendorTier tier) {
    switch (tier) {
      case VendorTier.basic:
        return 0; // No email campaigns
      case VendorTier.standard:
        return 5; // 5 emails per month
      case VendorTier.premium:
        return 20; // 20 emails per month
      case VendorTier.enterprise:
        return 100; // 100 emails per month
    }
  }

  int _getBadgeLimit(VendorTier tier) {
    switch (tier) {
      case VendorTier.basic:
        return 1; // 1 badge per product
      case VendorTier.standard:
        return 2; // 2 badges per product
      case VendorTier.premium:
        return 3; // 3 badges per product
      case VendorTier.enterprise:
        return 5; // 5 badges per product
    }
  }

  List<String> _getAvailableMarketingFeatures(VendorTier tier) {
    final features = <String>[];
    
    // Basic tier features
    features.add('Product badges (limited)');
    
    if (tier.index >= VendorTier.standard.index) {
      features.addAll([
        'Email campaigns (limited)',
        'Category spotlight',
        'SEO optimization',
        'Bulk upload',
        'Video uploads',
      ]);
    }
    
    if (tier.index >= VendorTier.premium.index) {
      features.addAll([
        'Homepage banners',
        'Social media promotion',
        'Custom branding',
        'Priority support',
        'Advanced analytics',
      ]);
    }
    
    if (tier == VendorTier.enterprise) {
      features.addAll([
        'API access',
        'Unlimited email campaigns',
        'Dedicated account manager',
        'Custom integrations',
      ]);
    }
    
    return features;
  }

  List<String> _getUpgradeRecommendations(VendorTier currentTier) {
    if (currentTier == VendorTier.enterprise) return [];
    
    final nextTier = VendorTier.values[currentTier.index + 1];
    final recommendations = <String>[];
    
    switch (nextTier) {
      case VendorTier.standard:
        recommendations.addAll([
          'Unlock email marketing campaigns',
          'Get category spotlight placement',
          'Access SEO optimization tools',
          'Upload videos and bulk products',
        ]);
        break;
      case VendorTier.premium:
        recommendations.addAll([
          'Create homepage banner campaigns',
          'Get social media promotion',
          'Add custom branding',
          'Receive priority support',
        ]);
        break;
      case VendorTier.enterprise:
        recommendations.addAll([
          'Get API access for integrations',
          'Unlimited email campaigns',
          'Dedicated account manager',
          'Custom feature development',
        ]);
        break;
      case VendorTier.basic:
        break;
    }
    
    return recommendations;
  }

  // Mock data methods - replace with actual database queries
  Future<List<String>> _getActiveBanners(String vendorId) async => [];
  Future<int> _getMonthlyEmailCount(String vendorId) async => 2;
  Future<int> _getProductBadgeCount(String vendorId, String productId) async => 1;
  Future<List<String>> _getFeaturedProducts(String vendorId) async => ['product_1', 'product_2'];
  Future<List<String>> _getActiveCampaigns(String vendorId) async => ['Spring Sale', 'New Launch'];
  Future<Map<String, dynamic>> _getCampaignPerformance(String vendorId) async => {
    'email_open_rate': 0.25,
    'click_through_rate': 0.08,
    'conversion_rate': 0.03,
  };
  Future<Map<String, dynamic>> _getMarketingUsageStats(String vendorId) async => {
    'featured_products_used': 2,
    'email_campaigns_sent': 3,
    'badges_applied': 15,
  };
  Future<List<String>> _generateSEOSuggestions(String productId) async => [
    'Add more descriptive keywords to title',
    'Include product benefits in description',
    'Add high-quality product images',
  ];
  Future<int> _calculateSEOScore(String productId) async => 75;
}

/// Marketing campaign creation result
class MarketingCampaignResult {
  final bool success;
  final String? campaignId;
  final String? message;
  final String? error;
  final VendorTier? requiredTier;

  MarketingCampaignResult({
    required this.success,
    this.campaignId,
    this.message,
    this.error,
    this.requiredTier,
  });
}

/// Badge application result
class BadgeApplicationResult {
  final bool success;
  final String? message;
  final String? error;
  final List<String>? availableBadges;

  BadgeApplicationResult({
    required this.success,
    this.message,
    this.error,
    this.availableBadges,
  });
}

/// Marketing dashboard data
class MarketingDashboard {
  final String vendorId;
  final VendorTier tier;
  final List<String> featuredProducts;
  final List<String> activeCampaigns;
  final Map<String, dynamic> campaignPerformance;
  final List<String> availableFeatures;
  final Map<String, dynamic> usageStats;
  final List<String> upgradeRecommendations;

  MarketingDashboard({
    required this.vendorId,
    required this.tier,
    required this.featuredProducts,
    required this.activeCampaigns,
    required this.campaignPerformance,
    required this.availableFeatures,
    required this.usageStats,
    required this.upgradeRecommendations,
  });

  factory MarketingDashboard.empty(String vendorId) {
    return MarketingDashboard(
      vendorId: vendorId,
      tier: VendorTier.basic,
      featuredProducts: [],
      activeCampaigns: [],
      campaignPerformance: {},
      availableFeatures: [],
      usageStats: {},
      upgradeRecommendations: [],
    );
  }
}

/// SEO optimization suggestions
class SEOSuggestions {
  final bool available;
  final String? message;
  final VendorTier? requiredTier;
  final List<String>? suggestions;
  final int? seoScore;

  SEOSuggestions({
    required this.available,
    this.message,
    this.requiredTier,
    this.suggestions,
    this.seoScore,
  });
}
