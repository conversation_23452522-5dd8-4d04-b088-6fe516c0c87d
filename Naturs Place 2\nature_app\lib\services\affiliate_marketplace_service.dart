import 'package:flutter/foundation.dart';
import '../providers/app_state.dart';

// Partner Tiers
enum PartnerTier { basic, premium, enterprise }

// Partner Fee Structure
class PartnerFees {
  // Monthly subscription fees for partners
  static const Map<PartnerTier, double> monthlyFees = {
    PartnerTier.basic: 49.99,    // Basic tier - $49.99/month for profitability
    PartnerTier.premium: 299.99, // Premium tier
    PartnerTier.enterprise: 999.99, // Enterprise tier
  };

  // Platform fees (percentage of sales)
  static const Map<PartnerTier, double> platformFeeRates = {
    PartnerTier.basic: 0.15,      // 15% platform fee
    PartnerTier.premium: 0.10,    // 10% platform fee
    PartnerTier.enterprise: 0.05, // 5% platform fee
  };

  // Product upload limits by tier
  static const Map<PartnerTier, int> uploadLimits = {
    PartnerTier.basic: 10,        // 10 products max
    PartnerTier.premium: 100,     // 100 products max
    PartnerTier.enterprise: 500,  // 500 products max (high but limited)
  };

  // Marketing boost fees (per campaign)
  static const Map<String, double> marketingBoostFees = {
    'featured_placement': 199.99,    // Featured product placement
    'category_sponsorship': 499.99,  // Category page sponsorship
    'homepage_banner': 999.99,       // Homepage banner placement
    'email_campaign': 299.99,        // Email marketing campaign
    'social_media_boost': 149.99,    // Social media promotion
  };

  static double getMonthlyFee(PartnerTier tier) {
    return monthlyFees[tier] ?? 0.0;
  }

  static double getPlatformFeeRate(PartnerTier tier) {
    return platformFeeRates[tier] ?? 0.15;
  }

  static int getUploadLimit(PartnerTier tier) {
    return uploadLimits[tier] ?? 10;
  }

  static bool hasUnlimitedUploads(PartnerTier tier) {
    // No tier has unlimited uploads anymore - all have limits
    return false;
  }

  static double calculatePlatformFee(double salePrice, PartnerTier tier) {
    return salePrice * getPlatformFeeRate(tier);
  }

  static String getTierDisplayName(PartnerTier tier) {
    switch (tier) {
      case PartnerTier.basic:
        return 'Basic';
      case PartnerTier.premium:
        return 'Premium';
      case PartnerTier.enterprise:
        return 'Enterprise';
    }
  }

  static List<String> getTierFeatures(PartnerTier tier) {
    switch (tier) {
      case PartnerTier.basic:
        return [
          'Up to 10 product uploads',
          'Basic marketplace access',
          'Standard analytics',
          'Email support',
          '15% platform fee',
          '\$49.99/month subscription',
        ];
      case PartnerTier.premium:
        return [
          'Up to 100 product uploads',
          'Advanced analytics & insights',
          'Priority support',
          'Marketing campaign tools',
          'Content optimization',
          'Price comparison tools',
          '10% platform fee',
          '\$299.99/month subscription',
        ];
      case PartnerTier.enterprise:
        return [
          'Up to 500 product uploads',
          'Dedicated account manager',
          'Custom integrations',
          'White-label options',
          'Advanced API access',
          'Custom reporting',
          'Bulk upload tools',
          '5% platform fee',
          '\$999.99/month subscription',
        ];
    }
  }
}

// App Commission Structure (Only the app earns commissions)
class PartnerCommissions {
  // Commission rates the app earns from partner networks
  static const Map<String, double> networkCommissionRates = {
    'iHerb': 0.10,        // 10% commission to app
    'Amazon': 0.08,       // 8% commission to app
    'Thrive Market': 0.12, // 12% commission to app
    'Plant Therapy': 0.15, // 15% commission to app
    'Vitacost': 0.09,     // 9% commission to app
    'Mountain Rose Herbs': 0.14, // 14% commission to app
  };

  static double getCommissionRate(String network) {
    return networkCommissionRates[network] ?? 0.05; // Default 5% to app
  }

  static double calculateAppCommission(double salePrice, String network) {
    return salePrice * getCommissionRate(network);
  }

  static double calculateMonthlyAppEarnings(List<PartnerSale> sales) {
    return sales.fold(0.0, (sum, sale) => sum + sale.commissionAmount);
  }
}

// Purchase Transaction Model
class PurchaseTransaction {
  final String id;
  final String productId;
  final String vendorId;
  final String customerId;
  final String customerName;
  final String customerEmail;
  final double purchaseAmount;
  final double appCommissionAmount; // Commission earned by the app
  final DateTime purchaseDate;
  final String status; // 'pending', 'completed', 'failed', 'refunded'
  final String paymentMethod;
  final String? orderId;
  final bool isVendorSiteRedirect;
  final String purchaseUrl;

  PurchaseTransaction({
    required this.id,
    required this.productId,
    required this.vendorId,
    required this.customerId,
    required this.customerName,
    required this.customerEmail,
    required this.purchaseAmount,
    required this.appCommissionAmount,
    required this.purchaseDate,
    required this.status,
    required this.paymentMethod,
    this.orderId,
    required this.isVendorSiteRedirect,
    required this.purchaseUrl,
  });
}

// Product Review Model
class ProductReview {
  final String id;
  final String productId;
  final String customerId;
  final String customerName;
  final double rating; // 1-5 stars
  final String title;
  final String comment;
  final DateTime createdAt;
  final bool isVerified; // Verified purchase
  final List<String> helpfulVotes; // Customer IDs who found helpful
  final bool isNaturalProduct; // Customer confirms natural/organic
  final Map<String, dynamic>? additionalData;

  ProductReview({
    required this.id,
    required this.productId,
    required this.customerId,
    required this.customerName,
    required this.rating,
    required this.title,
    required this.comment,
    required this.createdAt,
    required this.isVerified,
    required this.helpfulVotes,
    required this.isNaturalProduct,
    this.additionalData,
  });
}

// Platform Commission Tracking (ADMIN ONLY - App Earnings)
class PlatformCommissionData {
  final String id;
  final String transactionId;
  final String vendorId;
  final String productId;
  final double saleAmount;
  final double vendorPlatformFee; // Fee collected from vendor (APP EARNINGS)
  final double affiliateCommissionFee; // Commission earned by app from partner network (APP EARNINGS)
  final double totalPlatformEarnings; // Total earnings from this transaction (APP EARNINGS ONLY)
  final DateTime date;
  final String tier; // Vendor tier at time of sale
  final double platformFeeRate; // Platform fee rate applied to vendor
  final double affiliateCommissionRate; // Commission rate from affiliate partner

  PlatformCommissionData({
    required this.id,
    required this.transactionId,
    required this.vendorId,
    required this.productId,
    required this.saleAmount,
    required this.vendorPlatformFee,
    required this.affiliateCommissionFee,
    required this.totalPlatformEarnings,
    required this.date,
    required this.tier,
    required this.platformFeeRate,
    required this.affiliateCommissionRate,
  });
}

// Natural/Organic Verification System
class ProductVerification {
  final String productId;
  final bool isNatural;
  final bool isOrganic;
  final bool isCertified;
  final List<String> certifications; // USDA Organic, Non-GMO, etc.
  final DateTime verifiedDate;
  final String verifiedBy; // Admin ID who verified
  final double naturalScore; // 0-100 based on ingredients
  final double organicScore; // 0-100 based on certifications
  final Map<String, bool> ingredientAnalysis; // Ingredient breakdown

  ProductVerification({
    required this.productId,
    required this.isNatural,
    required this.isOrganic,
    required this.isCertified,
    required this.certifications,
    required this.verifiedDate,
    required this.verifiedBy,
    required this.naturalScore,
    required this.organicScore,
    required this.ingredientAnalysis,
  });
}

// Vendor Notification Model
class VendorNotification {
  final String id;
  final String vendorId;
  final String type; // 'purchase', 'commission', 'system', 'marketing'
  final String title;
  final String message;
  final DateTime createdAt;
  final bool isRead;
  final Map<String, dynamic>? data; // Additional data (purchase details, etc.)
  final String priority; // 'low', 'medium', 'high', 'urgent'

  VendorNotification({
    required this.id,
    required this.vendorId,
    required this.type,
    required this.title,
    required this.message,
    required this.createdAt,
    required this.isRead,
    this.data,
    required this.priority,
  });
}

// Partner Sale Model (keeping for backward compatibility)
class PartnerSale {
  final String id;
  final String productId;
  final String affiliateNetwork;
  final String productName;
  final double salePrice;
  final double commissionRate;
  final double commissionAmount;
  final DateTime saleDate;
  final String status; // 'pending', 'confirmed', 'paid'
  final String? orderId;
  final String? customerId;

  PartnerSale({
    required this.id,
    required this.productId,
    required this.affiliateNetwork,
    required this.productName,
    required this.salePrice,
    required this.commissionRate,
    required this.commissionAmount,
    required this.saleDate,
    required this.status,
    this.orderId,
    this.customerId,
  });
}

// Partner Product Model
class PartnerProduct extends Product {
  final String partnerNetwork;
  final String affiliateLink;
  final String affiliateCode;
  final double commissionRate;
  final bool isVerified;
  final List<String> tags;
  final String shortDescription;
  final List<String> benefits;
  final List<String> ingredients;
  final DateTime dateAdded;
  final bool inStock;
  final String? vendorWebsite; // Vendor's own website URL
  final bool redirectToVendorSite; // Whether to redirect to vendor site
  final String vendorId; // ID of the vendor who owns this product

  PartnerProduct({
    required super.id,
    required super.name,
    required super.description,
    required super.imageUrl,
    required super.price,
    required super.category,
    required super.rating,
    required super.reviewCount,
    required super.seller,
    required this.partnerNetwork,
    required this.affiliateLink,
    required this.affiliateCode,
    required this.commissionRate,
    required this.isVerified,
    required this.tags,
    required this.shortDescription,
    required this.benefits,
    required this.ingredients,
    required this.dateAdded,
    required this.inStock,
    this.vendorWebsite,
    required this.redirectToVendorSite,
    required this.vendorId,
  });

  double get commissionAmount => price * commissionRate;

  // Get the purchase URL (vendor site or affiliate link)
  String get purchaseUrl => redirectToVendorSite && vendorWebsite != null
    ? vendorWebsite!
    : affiliateLink;
}

// Marketing Opportunities
class MarketingOpportunity {
  final String id;
  final String title;
  final String description;
  final double cost;
  final int duration; // in days
  final List<String> benefits;
  final String category;
  final bool isActive;

  MarketingOpportunity({
    required this.id,
    required this.title,
    required this.description,
    required this.cost,
    required this.duration,
    required this.benefits,
    required this.category,
    required this.isActive,
  });
}

// Content Optimization Tools
class ContentOptimization {
  final String productId;
  final double seoScore;
  final List<String> suggestedKeywords;
  final List<String> missingIngredients;
  final List<String> competitorPrices;
  final List<String> optimizationTips;
  final DateTime lastUpdated;

  ContentOptimization({
    required this.productId,
    required this.seoScore,
    required this.suggestedKeywords,
    required this.missingIngredients,
    required this.competitorPrices,
    required this.optimizationTips,
    required this.lastUpdated,
  });
}

// Price Comparison Data
class PriceComparison {
  final String productId;
  final String productName;
  final double ourPrice;
  final Map<String, double> competitorPrices;
  final double averageMarketPrice;
  final String pricePosition; // 'lowest', 'competitive', 'premium'
  final List<String> recommendations;

  PriceComparison({
    required this.productId,
    required this.productName,
    required this.ourPrice,
    required this.competitorPrices,
    required this.averageMarketPrice,
    required this.pricePosition,
    required this.recommendations,
  });
}

// Ingredient Verification
class IngredientVerification {
  final String productId;
  final List<String> verifiedIngredients;
  final List<String> unverifiedIngredients;
  final List<String> certifications;
  final bool isOrganicCertified;
  final bool isNonGMO;
  final bool isGlutenFree;
  final double verificationScore;
  final DateTime lastVerified;

  IngredientVerification({
    required this.productId,
    required this.verifiedIngredients,
    required this.unverifiedIngredients,
    required this.certifications,
    required this.isOrganicCertified,
    required this.isNonGMO,
    required this.isGlutenFree,
    required this.verificationScore,
    required this.lastVerified,
  });
}

// Partner Model
class Partner {
  final String id;
  final String name;
  final String network;
  final String logoUrl;
  final String description;
  final double commissionRate;
  final int totalProducts;
  final double totalEarnings;
  final int totalSales;
  final bool isActive;
  final DateTime joinedDate;
  final Map<String, String> affiliateCodes; // Different codes for different categories
  final PartnerTier tier;
  final List<String> activeMarketingCampaigns;
  final double monthlyFeesPaid;
  final double platformFeesOwed;
  final int uploadedProductsCount; // Current number of uploaded products

  Partner({
    required this.id,
    required this.name,
    required this.network,
    required this.logoUrl,
    required this.description,
    required this.commissionRate,
    required this.totalProducts,
    required this.totalEarnings,
    required this.totalSales,
    required this.isActive,
    required this.joinedDate,
    required this.affiliateCodes,
    required this.tier,
    required this.activeMarketingCampaigns,
    required this.monthlyFeesPaid,
    required this.platformFeesOwed,
    required this.uploadedProductsCount,
  });

  // Calculate monthly subscription fee
  double get monthlySubscriptionFee => PartnerFees.getMonthlyFee(tier);

  // Calculate platform fee rate
  double get platformFeeRate => PartnerFees.getPlatformFeeRate(tier);

  // Get upload limit for this tier
  int get uploadLimit => PartnerFees.getUploadLimit(tier);

  // Check if uploads are unlimited
  bool get hasUnlimitedUploads => PartnerFees.hasUnlimitedUploads(tier);

  // Get remaining upload slots
  int get remainingUploads {
    if (hasUnlimitedUploads) return -1; // Unlimited
    return (uploadLimit - uploadedProductsCount).clamp(0, uploadLimit);
  }

  // Check if can upload more products
  bool get canUploadMore {
    if (hasUnlimitedUploads) return true;
    return uploadedProductsCount < uploadLimit;
  }

  // Get upload progress percentage (0-100)
  double get uploadProgressPercentage {
    if (hasUnlimitedUploads) return 0.0; // No limit to show progress for
    return (uploadedProductsCount / uploadLimit * 100).clamp(0.0, 100.0);
  }

  // Get tier benefits
  List<String> get tierBenefits => PartnerFees.getTierFeatures(tier);

  // Get tier display name
  String get tierDisplayName => PartnerFees.getTierDisplayName(tier);

  // Create a copy with updated upload count
  Partner copyWith({
    String? id,
    String? name,
    String? network,
    String? logoUrl,
    String? description,
    double? commissionRate,
    int? totalProducts,
    double? totalEarnings,
    int? totalSales,
    bool? isActive,
    DateTime? joinedDate,
    Map<String, String>? affiliateCodes,
    PartnerTier? tier,
    List<String>? activeMarketingCampaigns,
    double? monthlyFeesPaid,
    double? platformFeesOwed,
    int? uploadedProductsCount,
  }) {
    return Partner(
      id: id ?? this.id,
      name: name ?? this.name,
      network: network ?? this.network,
      logoUrl: logoUrl ?? this.logoUrl,
      description: description ?? this.description,
      commissionRate: commissionRate ?? this.commissionRate,
      totalProducts: totalProducts ?? this.totalProducts,
      totalEarnings: totalEarnings ?? this.totalEarnings,
      totalSales: totalSales ?? this.totalSales,
      isActive: isActive ?? this.isActive,
      joinedDate: joinedDate ?? this.joinedDate,
      affiliateCodes: affiliateCodes ?? this.affiliateCodes,
      tier: tier ?? this.tier,
      activeMarketingCampaigns: activeMarketingCampaigns ?? this.activeMarketingCampaigns,
      monthlyFeesPaid: monthlyFeesPaid ?? this.monthlyFeesPaid,
      platformFeesOwed: platformFeesOwed ?? this.platformFeesOwed,
      uploadedProductsCount: uploadedProductsCount ?? this.uploadedProductsCount,
    );
  }
}

// Brand Dashboard Analytics
class BrandAnalytics {
  final double totalRevenue;
  final double totalCommissions;
  final int totalSales;
  final int totalClicks;
  final double conversionRate;
  final Map<String, double> revenueByNetwork;
  final Map<String, int> salesByCategory;
  final List<PartnerProduct> topPerformingProducts;
  final List<PartnerSale> recentSales;

  BrandAnalytics({
    required this.totalRevenue,
    required this.totalCommissions,
    required this.totalSales,
    required this.totalClicks,
    required this.conversionRate,
    required this.revenueByNetwork,
    required this.salesByCategory,
    required this.topPerformingProducts,
    required this.recentSales,
  });
}

class PartnerMarketplaceService extends ChangeNotifier {
  static final PartnerMarketplaceService _instance = PartnerMarketplaceService._internal();
  factory PartnerMarketplaceService() => _instance;
  PartnerMarketplaceService._internal();

  final List<PartnerProduct> _products = [];
  final List<Partner> _partners = [];
  final List<PartnerSale> _sales = [];
  final List<PurchaseTransaction> _purchases = [];
  final List<VendorNotification> _notifications = [];
  final List<ProductReview> _reviews = [];
  final List<PlatformCommissionData> _platformCommissions = [];
  final List<ProductVerification> _verifications = [];
  final bool _isLoading = false;

  List<PartnerProduct> get products => _products;
  List<Partner> get partners => _partners;
  List<PartnerSale> get sales => _sales;
  List<PurchaseTransaction> get purchases => _purchases;
  List<VendorNotification> get notifications => _notifications;
  List<ProductReview> get reviews => _reviews;
  List<PlatformCommissionData> get platformCommissions => _platformCommissions;
  List<ProductVerification> get verifications => _verifications;
  bool get isLoading => _isLoading;

  // Initialize with mock data
  void initialize() {
    _loadMockData();
    notifyListeners();
  }

  void _loadMockData() {
    // Mock affiliate partners
    _partners.addAll([
      Partner(
        id: 'iherb_001',
        name: 'iHerb',
        network: 'iHerb',
        logoUrl: 'https://cdn.pixabay.com/photo/2018/03/17/19/36/iherb-logo-3234344_1280.jpg',
        description: 'Leading online retailer of natural health products',
        commissionRate: 0.10,
        totalProducts: 1250,
        totalEarnings: 2847.50,
        totalSales: 156,
        isActive: true,
        joinedDate: DateTime.now().subtract(const Duration(days: 180)),
        affiliateCodes: {'supplements': 'NATURE10', 'herbs': 'HERB15'},
        tier: PartnerTier.premium,
        activeMarketingCampaigns: ['featured_placement', 'email_campaign'],
        monthlyFeesPaid: 299.99,
        platformFeesOwed: 284.75,
        uploadedProductsCount: 67, // 67 out of 100 allowed for Premium tier
      ),
      Partner(
        id: 'amazon_001',
        name: 'Amazon',
        network: 'Amazon',
        logoUrl: 'https://cdn.pixabay.com/photo/2018/05/15/21/24/amazon-logo-3404181_1280.jpg',
        description: 'World\'s largest online marketplace',
        commissionRate: 0.08,
        totalProducts: 850,
        totalEarnings: 1923.75,
        totalSales: 98,
        isActive: true,
        joinedDate: DateTime.now().subtract(const Duration(days: 120)),
        affiliateCodes: {'general': 'naturesplace-20'},
        tier: PartnerTier.enterprise,
        activeMarketingCampaigns: ['homepage_banner', 'category_sponsorship'],
        monthlyFeesPaid: 999.99,
        platformFeesOwed: 96.19,
        uploadedProductsCount: 287, // 287 out of 500 allowed for Enterprise tier
      ),
      Partner(
        id: 'thrive_001',
        name: 'Thrive Market',
        network: 'Thrive Market',
        logoUrl: 'https://cdn.pixabay.com/photo/2019/08/21/15/34/thrive-market-logo-4421847_1280.jpg',
        description: 'Organic and natural products marketplace',
        commissionRate: 0.12,
        totalProducts: 650,
        totalEarnings: 1456.80,
        totalSales: 67,
        isActive: true,
        joinedDate: DateTime.now().subtract(const Duration(days: 90)),
        affiliateCodes: {'organic': 'THRIVE12'},
        tier: PartnerTier.basic,
        activeMarketingCampaigns: ['social_media_boost'],
        monthlyFeesPaid: 49.99, // Updated to reflect new Basic tier pricing
        platformFeesOwed: 218.52,
        uploadedProductsCount: 8, // 8 out of 10 allowed for Basic tier
      ),
    ]);

    // Mock affiliate products
    _products.addAll([
      PartnerProduct(
        id: 'af_001',
        name: 'Organic Ashwagandha Vitamins & Minerals',
        description: 'Premium organic ashwagandha root extract vitamins & minerals for stress relief and energy support.',
        imageUrl: 'https://cdn.pixabay.com/photo/2019/11/07/21/32/ashwagandha-4611052_1280.jpg',
        price: 24.99,
        category: 'Supplements',
        rating: 4.7,
        reviewCount: 1234,
        seller: 'iHerb',
        partnerNetwork: 'iHerb',
        affiliateLink: 'https://iherb.com/product/123?rcode=NATURE10',
        affiliateCode: 'NATURE10',
        commissionRate: 0.10,
        isVerified: true,
        tags: ['organic', 'stress-relief', 'adaptogen'],
        shortDescription: 'Premium organic ashwagandha for stress relief',
        benefits: ['Reduces stress', 'Supports energy', 'Improves sleep'],
        ingredients: ['Organic Ashwagandha Root Extract', 'Vegetable Cellulose'],
        dateAdded: DateTime.now().subtract(const Duration(days: 30)),
        inStock: true,
        vendorWebsite: 'https://naturalsupplements.com/ashwagandha',
        redirectToVendorSite: true,
        vendorId: 'iherb_001',
      ),
      PartnerProduct(
        id: 'af_002',
        name: 'Lavender Essential Oil Set',
        description: 'Pure therapeutic grade lavender essential oil set with diffuser blend recipes.',
        imageUrl: 'https://cdn.pixabay.com/photo/2015/07/02/20/57/lavender-830922_1280.jpg',
        price: 32.99,
        category: 'Essential Oils',
        rating: 4.9,
        reviewCount: 856,
        seller: 'Plant Therapy',
        partnerNetwork: 'Plant Therapy',
        affiliateLink: 'https://planttherapy.com/lavender-set?ref=naturesplace',
        affiliateCode: 'NATURESPLACE',
        commissionRate: 0.15,
        isVerified: true,
        tags: ['essential-oil', 'aromatherapy', 'relaxation'],
        shortDescription: 'Pure therapeutic grade lavender oil set',
        benefits: ['Promotes relaxation', 'Improves sleep quality', 'Natural aromatherapy'],
        ingredients: ['100% Pure Lavandula Angustifolia'],
        dateAdded: DateTime.now().subtract(const Duration(days: 15)),
        inStock: true,
        vendorWebsite: null, // Uses affiliate link instead
        redirectToVendorSite: false,
        vendorId: 'plant_therapy_001',
      ),
    ]);

    // Mock sales data
    _sales.addAll([
      PartnerSale(
        id: 'sale_001',
        productId: 'af_001',
        affiliateNetwork: 'iHerb',
        productName: 'Organic Ashwagandha Vitamins & Minerals',
        salePrice: 24.99,
        commissionRate: 0.10,
        commissionAmount: 2.50,
        saleDate: DateTime.now().subtract(const Duration(days: 2)),
        status: 'confirmed',
        orderId: 'IH123456',
        customerId: 'cust_001',
      ),
      PartnerSale(
        id: 'sale_002',
        productId: 'af_002',
        affiliateNetwork: 'Plant Therapy',
        productName: 'Lavender Essential Oil Set',
        salePrice: 32.99,
        commissionRate: 0.15,
        commissionAmount: 4.95,
        saleDate: DateTime.now().subtract(const Duration(days: 1)),
        status: 'pending',
        orderId: 'PT789012',
        customerId: 'cust_002',
      ),
    ]);

    // Mock product reviews
    _reviews.addAll([
      ProductReview(
        id: 'review_001',
        productId: 'af_001',
        customerId: 'cust_001',
        customerName: 'Sarah Johnson',
        rating: 5.0,
        title: 'Amazing stress relief!',
        comment: 'This ashwagandha supplement has been a game-changer for my stress levels. I feel more balanced and energetic throughout the day.',
        createdAt: DateTime.now().subtract(const Duration(days: 5)),
        isVerified: true,
        helpfulVotes: ['cust_002', 'cust_003'],
        isNaturalProduct: true,
      ),
      ProductReview(
        id: 'review_002',
        productId: 'af_001',
        customerId: 'cust_004',
        customerName: 'Mike Chen',
        rating: 4.0,
        title: 'Good quality, natural ingredients',
        comment: 'High quality supplement with clean ingredients. Takes a few weeks to notice effects but definitely worth it.',
        createdAt: DateTime.now().subtract(const Duration(days: 3)),
        isVerified: true,
        helpfulVotes: ['cust_001'],
        isNaturalProduct: true,
      ),
      ProductReview(
        id: 'review_003',
        productId: 'af_002',
        customerId: 'cust_005',
        customerName: 'Emma Wilson',
        rating: 5.0,
        title: 'Pure and therapeutic grade',
        comment: 'Absolutely love this lavender oil set! The quality is exceptional and it\'s 100% pure. Perfect for aromatherapy and relaxation.',
        createdAt: DateTime.now().subtract(const Duration(days: 1)),
        isVerified: true,
        helpfulVotes: ['cust_001', 'cust_002', 'cust_004'],
        isNaturalProduct: true,
      ),
    ]);

    // Mock product verifications
    _verifications.addAll([
      ProductVerification(
        productId: 'af_001',
        isNatural: true,
        isOrganic: true,
        isCertified: true,
        certifications: ['USDA Organic', 'Non-GMO Project Verified', 'Third-Party Tested'],
        verifiedDate: DateTime.now().subtract(const Duration(days: 10)),
        verifiedBy: 'admin_001',
        naturalScore: 95.0,
        organicScore: 98.0,
        ingredientAnalysis: {
          'Organic Ashwagandha Root Extract': true,
          'Vegetable Cellulose': true,
          'Artificial Additives': false,
          'Synthetic Fillers': false,
          'Pesticide Residues': false,
        },
      ),
      ProductVerification(
        productId: 'af_002',
        isNatural: true,
        isOrganic: true,
        isCertified: true,
        certifications: ['Therapeutic Grade', 'GC/MS Tested', 'Organic Certified'],
        verifiedDate: DateTime.now().subtract(const Duration(days: 8)),
        verifiedBy: 'admin_001',
        naturalScore: 100.0,
        organicScore: 100.0,
        ingredientAnalysis: {
          '100% Pure Lavandula Angustifolia': true,
          'Synthetic Fragrances': false,
          'Chemical Additives': false,
          'Diluted with Carrier Oils': false,
        },
      ),
    ]);
  }

  // Filter products by category and network
  List<PartnerProduct> filterProducts({
    String? category,
    String? network,
    bool verifiedOnly = false,
  }) {
    return _products.where((product) {
      if (category != null && category != 'All Products' && product.category != category) {
        return false;
      }
      if (network != null && product.partnerNetwork != network) {
        return false;
      }
      if (verifiedOnly && !product.isVerified) {
        return false;
      }
      return true;
    }).toList();
  }

  // Get brand analytics
  BrandAnalytics getBrandAnalytics() {
    final totalRevenue = _sales.fold(0.0, (sum, sale) => sum + sale.salePrice);
    final totalCommissions = _sales.fold(0.0, (sum, sale) => sum + sale.commissionAmount);
    final totalSales = _sales.length;
    final totalClicks = totalSales * 10; // Mock click data
    final conversionRate = totalSales / totalClicks;

    final revenueByNetwork = <String, double>{};
    final salesByCategory = <String, int>{};

    for (final sale in _sales) {
      revenueByNetwork[sale.affiliateNetwork] = 
          (revenueByNetwork[sale.affiliateNetwork] ?? 0) + sale.commissionAmount;
      
      final product = _products.firstWhere((p) => p.id == sale.productId);
      salesByCategory[product.category] = 
          (salesByCategory[product.category] ?? 0) + 1;
    }

    return BrandAnalytics(
      totalRevenue: totalRevenue,
      totalCommissions: totalCommissions,
      totalSales: totalSales,
      totalClicks: totalClicks,
      conversionRate: conversionRate,
      revenueByNetwork: revenueByNetwork,
      salesByCategory: salesByCategory,
      topPerformingProducts: _products.take(5).toList(),
      recentSales: _sales.take(10).toList(),
    );
  }

  // Get top performing partners
  List<Partner> getTopPartners({int limit = 5}) {
    final sortedPartners = List<Partner>.from(_partners);
    sortedPartners.sort((a, b) => b.totalEarnings.compareTo(a.totalEarnings));
    return sortedPartners.take(limit).toList();
  }

  // Get recent sales
  List<PartnerSale> getRecentSales({int limit = 10}) {
    final sortedSales = List<PartnerSale>.from(_sales);
    sortedSales.sort((a, b) => b.saleDate.compareTo(a.saleDate));
    return sortedSales.take(limit).toList();
  }

  // Upload management methods

  // Check if a partner can upload more products
  bool canPartnerUploadMore(String partnerId) {
    final partner = _partners.firstWhere(
      (p) => p.id == partnerId,
      orElse: () => throw Exception('Partner not found'),
    );
    return partner.canUploadMore;
  }

  // Get upload quota information for a partner
  Map<String, dynamic> getUploadQuota(String partnerId) {
    final partner = _partners.firstWhere(
      (p) => p.id == partnerId,
      orElse: () => throw Exception('Partner not found'),
    );

    return {
      'tier': partner.tierDisplayName,
      'uploadLimit': partner.uploadLimit,
      'uploadedCount': partner.uploadedProductsCount,
      'remainingUploads': partner.remainingUploads,
      'hasUnlimitedUploads': partner.hasUnlimitedUploads,
      'uploadProgressPercentage': partner.uploadProgressPercentage,
      'canUploadMore': partner.canUploadMore,
    };
  }

  // Simulate product upload (returns success/failure with reason)
  Map<String, dynamic> uploadProduct(String partnerId, PartnerProduct product) {
    final partnerIndex = _partners.indexWhere((p) => p.id == partnerId);
    if (partnerIndex == -1) {
      return {
        'success': false,
        'reason': 'Partner not found',
        'errorCode': 'PARTNER_NOT_FOUND',
      };
    }

    final partner = _partners[partnerIndex];

    if (!partner.canUploadMore) {
      return {
        'success': false,
        'reason': 'Upload limit reached for ${partner.tierDisplayName} tier',
        'errorCode': 'UPLOAD_LIMIT_REACHED',
        'currentLimit': partner.uploadLimit,
        'uploadedCount': partner.uploadedProductsCount,
        'suggestedAction': 'Upgrade to ${_getNextTierName(partner.tier)} for more uploads',
      };
    }

    // Simulate successful upload
    _products.add(product);
    _partners[partnerIndex] = partner.copyWith(
      uploadedProductsCount: partner.uploadedProductsCount + 1,
      totalProducts: partner.totalProducts + 1,
    );

    notifyListeners();

    return {
      'success': true,
      'message': 'Product uploaded successfully',
      'remainingUploads': _partners[partnerIndex].remainingUploads,
    };
  }

  // Get next tier name for upgrade suggestions
  String _getNextTierName(PartnerTier currentTier) {
    switch (currentTier) {
      case PartnerTier.basic:
        return 'Premium';
      case PartnerTier.premium:
        return 'Enterprise';
      case PartnerTier.enterprise:
        return 'Enterprise'; // Already at highest tier
    }
  }

  // Get tier upgrade information
  Map<String, dynamic> getTierUpgradeInfo(PartnerTier currentTier) {
    switch (currentTier) {
      case PartnerTier.basic:
        return {
          'nextTier': 'Premium',
          'nextTierPrice': 299.99,
          'nextTierUploadLimit': 100,
          'nextTierFeatures': PartnerFees.getTierFeatures(PartnerTier.premium),
        };
      case PartnerTier.premium:
        return {
          'nextTier': 'Enterprise',
          'nextTierPrice': 999.99,
          'nextTierUploadLimit': 500, // Updated to 500 products
          'nextTierFeatures': PartnerFees.getTierFeatures(PartnerTier.enterprise),
        };
      case PartnerTier.enterprise:
        return {
          'nextTier': null,
          'message': 'You are already on the highest tier with 500 product uploads',
        };
    }
  }

  // Get all partners with their upload status
  List<Map<String, dynamic>> getPartnersWithUploadStatus() {
    return _partners.map((partner) {
      return {
        'partner': partner,
        'uploadQuota': getUploadQuota(partner.id),
      };
    }).toList();
  }

  // Purchase Management Methods

  // Process a product purchase
  Future<Map<String, dynamic>> processPurchase({
    required String productId,
    required String customerId,
    required String customerName,
    required String customerEmail,
    required String paymentMethod,
  }) async {
    try {
      final product = _products.firstWhere(
        (p) => p.id == productId,
        orElse: () => throw Exception('Product not found'),
      );

      // Create purchase transaction
      final purchase = PurchaseTransaction(
        id: 'purchase_${DateTime.now().millisecondsSinceEpoch}',
        productId: productId,
        vendorId: product.vendorId,
        customerId: customerId,
        customerName: customerName,
        customerEmail: customerEmail,
        purchaseAmount: product.price,
        appCommissionAmount: product.commissionAmount,
        purchaseDate: DateTime.now(),
        status: 'completed',
        paymentMethod: paymentMethod,
        orderId: 'ORDER_${DateTime.now().millisecondsSinceEpoch}',
        isVendorSiteRedirect: product.redirectToVendorSite,
        purchaseUrl: product.purchaseUrl,
      );

      _purchases.add(purchase);

      // Create platform commission data (ADMIN ONLY - App Earnings)
      final vendor = _partners.firstWhere((p) => p.id == product.vendorId);
      final platformFeeRate = PartnerFees.getPlatformFeeRate(vendor.tier);
      final vendorPlatformFee = product.price * platformFeeRate; // Fee collected from vendor (APP EARNINGS)
      final affiliateCommissionFee = product.price * product.commissionRate; // Commission earned by app from partner network (APP EARNINGS)
      final totalPlatformEarnings = vendorPlatformFee + affiliateCommissionFee; // TOTAL APP EARNINGS (Partners/vendors earn nothing)

      final commissionData = PlatformCommissionData(
        id: 'comm_${DateTime.now().millisecondsSinceEpoch}',
        transactionId: purchase.id,
        vendorId: product.vendorId,
        productId: productId,
        saleAmount: product.price,
        vendorPlatformFee: vendorPlatformFee,
        affiliateCommissionFee: affiliateCommissionFee,
        totalPlatformEarnings: totalPlatformEarnings,
        date: DateTime.now(),
        tier: vendor.tierDisplayName,
        platformFeeRate: platformFeeRate,
        affiliateCommissionRate: product.commissionRate,
      );

      _platformCommissions.add(commissionData);

      // Create vendor notification
      await _createVendorNotification(
        vendorId: product.vendorId,
        type: 'purchase',
        title: 'New Purchase!',
        message: 'Your product "${product.name}" was purchased by $customerName',
        priority: 'high',
        data: {
          'productId': productId,
          'productName': product.name,
          'purchaseAmount': product.price,
          'appCommissionAmount': product.commissionAmount, // Commission earned by app
          'customerName': customerName,
          'orderId': purchase.orderId,
        },
      );

      notifyListeners();

      return {
        'success': true,
        'purchaseId': purchase.id,
        'orderId': purchase.orderId,
        'redirectUrl': product.purchaseUrl,
        'isVendorRedirect': product.redirectToVendorSite,
        'message': 'Purchase processed successfully',
      };
    } catch (e) {
      return {
        'success': false,
        'error': e.toString(),
        'message': 'Failed to process purchase',
      };
    }
  }

  // Create vendor notification
  Future<void> _createVendorNotification({
    required String vendorId,
    required String type,
    required String title,
    required String message,
    required String priority,
    Map<String, dynamic>? data,
  }) async {
    final notification = VendorNotification(
      id: 'notif_${DateTime.now().millisecondsSinceEpoch}',
      vendorId: vendorId,
      type: type,
      title: title,
      message: message,
      createdAt: DateTime.now(),
      isRead: false,
      data: data,
      priority: priority,
    );

    _notifications.add(notification);
    notifyListeners();
  }

  // Get notifications for a vendor
  List<VendorNotification> getVendorNotifications(String vendorId, {bool unreadOnly = false}) {
    return _notifications.where((notification) {
      if (notification.vendorId != vendorId) return false;
      if (unreadOnly && notification.isRead) return false;
      return true;
    }).toList()..sort((a, b) => b.createdAt.compareTo(a.createdAt));
  }

  // Mark notification as read
  void markNotificationAsRead(String notificationId) {
    final index = _notifications.indexWhere((n) => n.id == notificationId);
    if (index != -1) {
      final notification = _notifications[index];
      _notifications[index] = VendorNotification(
        id: notification.id,
        vendorId: notification.vendorId,
        type: notification.type,
        title: notification.title,
        message: notification.message,
        createdAt: notification.createdAt,
        isRead: true,
        data: notification.data,
        priority: notification.priority,
      );
      notifyListeners();
    }
  }

  // Get unread notification count for vendor
  int getUnreadNotificationCount(String vendorId) {
    return _notifications.where((n) => n.vendorId == vendorId && !n.isRead).length;
  }

  // Get purchase history for vendor
  List<PurchaseTransaction> getVendorPurchases(String vendorId) {
    return _purchases.where((p) => p.vendorId == vendorId).toList()
      ..sort((a, b) => b.purchaseDate.compareTo(a.purchaseDate));
  }

  // Get purchase analytics for vendor
  Map<String, dynamic> getVendorPurchaseAnalytics(String vendorId) {
    final vendorPurchases = getVendorPurchases(vendorId);
    final totalRevenue = vendorPurchases.fold(0.0, (sum, p) => sum + p.purchaseAmount);
    final totalAppCommissions = vendorPurchases.fold(0.0, (sum, p) => sum + p.appCommissionAmount);
    final totalSales = vendorPurchases.length;

    return {
      'totalRevenue': totalRevenue,
      'totalAppCommissions': totalAppCommissions, // Commission earned by app
      'totalSales': totalSales,
      'averageOrderValue': totalSales > 0 ? totalRevenue / totalSales : 0.0,
      'recentPurchases': vendorPurchases.take(5).toList(),
    };
  }

  // ADMIN ONLY - App Commission Analytics (Only app earns commissions)
  Map<String, dynamic> getPlatformCommissionAnalytics() {
    final totalPlatformEarnings = _platformCommissions.fold(0.0, (sum, c) => sum + c.totalPlatformEarnings);
    final totalVendorFees = _platformCommissions.fold(0.0, (sum, c) => sum + c.vendorPlatformFee);
    final totalPartnerCommissions = _platformCommissions.fold(0.0, (sum, c) => sum + c.affiliateCommissionFee);
    final totalSalesVolume = _platformCommissions.fold(0.0, (sum, c) => sum + c.saleAmount);

    // Earnings by tier (from vendor platform fees)
    final earningsByTier = <String, double>{};
    for (final commission in _platformCommissions) {
      earningsByTier[commission.tier] = (earningsByTier[commission.tier] ?? 0.0) + commission.vendorPlatformFee;
    }

    // Earnings by affiliate network (from affiliate commission fees)
    final earningsByNetwork = <String, double>{};
    for (final commission in _platformCommissions) {
      // Get the product to find affiliate network
      final product = _products.firstWhere((p) => p.id == commission.productId, orElse: () => _products.first);
      final network = product.partnerNetwork;
      earningsByNetwork[network] = (earningsByNetwork[network] ?? 0.0) + commission.affiliateCommissionFee;
    }

    // Monthly earnings
    final monthlyEarnings = <String, double>{};
    for (final commission in _platformCommissions) {
      final monthKey = '${commission.date.year}-${commission.date.month.toString().padLeft(2, '0')}';
      monthlyEarnings[monthKey] = (monthlyEarnings[monthKey] ?? 0.0) + commission.totalPlatformEarnings;
    }

    return {
      'totalPlatformEarnings': totalPlatformEarnings, // APP'S TOTAL EARNINGS
      'totalVendorFees': totalVendorFees, // Fees collected from vendors (APP EARNINGS)
      'totalPartnerCommissions': totalPartnerCommissions, // Commissions earned by app from partners (APP EARNINGS)
      'totalSalesVolume': totalSalesVolume,
      'platformMargin': totalSalesVolume > 0 ? (totalPlatformEarnings / totalSalesVolume * 100) : 0.0,
      'earningsByTier': earningsByTier, // App earnings from vendor fees by tier
      'earningsByNetwork': earningsByNetwork, // App earnings from partner commissions by network
      'monthlyEarnings': monthlyEarnings,
      'averageEarningsPerSale': _platformCommissions.isNotEmpty
        ? totalPlatformEarnings / _platformCommissions.length
        : 0.0,
      'vendorFeePercentage': totalPlatformEarnings > 0 ? (totalVendorFees / totalPlatformEarnings * 100) : 0.0,
      'partnerCommissionPercentage': totalPlatformEarnings > 0 ? (totalPartnerCommissions / totalPlatformEarnings * 100) : 0.0,
    };
  }

  // Review Management Methods

  // Add a product review
  Future<Map<String, dynamic>> addProductReview({
    required String productId,
    required String customerId,
    required String customerName,
    required double rating,
    required String title,
    required String comment,
    required bool isVerifiedPurchase,
    required bool isNaturalProduct,
  }) async {
    try {
      final review = ProductReview(
        id: 'review_${DateTime.now().millisecondsSinceEpoch}',
        productId: productId,
        customerId: customerId,
        customerName: customerName,
        rating: rating.clamp(1.0, 5.0),
        title: title,
        comment: comment,
        createdAt: DateTime.now(),
        isVerified: isVerifiedPurchase,
        helpfulVotes: [],
        isNaturalProduct: isNaturalProduct,
      );

      _reviews.add(review);

      // Update product rating
      _updateProductRating(productId);

      notifyListeners();

      return {
        'success': true,
        'reviewId': review.id,
        'message': 'Review added successfully',
      };
    } catch (e) {
      return {
        'success': false,
        'error': e.toString(),
        'message': 'Failed to add review',
      };
    }
  }

  // Get reviews for a product
  List<ProductReview> getProductReviews(String productId) {
    return _reviews.where((r) => r.productId == productId).toList()
      ..sort((a, b) => b.createdAt.compareTo(a.createdAt));
  }

  // Update product rating based on reviews
  void _updateProductRating(String productId) {
    final productReviews = getProductReviews(productId);
    if (productReviews.isEmpty) return;

    final averageRating = productReviews.fold(0.0, (sum, r) => sum + r.rating) / productReviews.length;
    final reviewCount = productReviews.length;

    // Update product in the list
    final productIndex = _products.indexWhere((p) => p.id == productId);
    if (productIndex != -1) {
      final product = _products[productIndex];
      _products[productIndex] = PartnerProduct(
        id: product.id,
        name: product.name,
        description: product.description,
        imageUrl: product.imageUrl,
        price: product.price,
        category: product.category,
        rating: averageRating,
        reviewCount: reviewCount,
        seller: product.seller,
        partnerNetwork: product.partnerNetwork,
        affiliateLink: product.affiliateLink,
        affiliateCode: product.affiliateCode,
        commissionRate: product.commissionRate,
        isVerified: product.isVerified,
        tags: product.tags,
        shortDescription: product.shortDescription,
        benefits: product.benefits,
        ingredients: product.ingredients,
        dateAdded: product.dateAdded,
        inStock: product.inStock,
        vendorWebsite: product.vendorWebsite,
        redirectToVendorSite: product.redirectToVendorSite,
        vendorId: product.vendorId,
      );
    }
  }

  // Vote review as helpful
  void voteReviewHelpful(String reviewId, String customerId) {
    final reviewIndex = _reviews.indexWhere((r) => r.id == reviewId);
    if (reviewIndex != -1) {
      final review = _reviews[reviewIndex];
      final helpfulVotes = List<String>.from(review.helpfulVotes);

      if (!helpfulVotes.contains(customerId)) {
        helpfulVotes.add(customerId);

        _reviews[reviewIndex] = ProductReview(
          id: review.id,
          productId: review.productId,
          customerId: review.customerId,
          customerName: review.customerName,
          rating: review.rating,
          title: review.title,
          comment: review.comment,
          createdAt: review.createdAt,
          isVerified: review.isVerified,
          helpfulVotes: helpfulVotes,
          isNaturalProduct: review.isNaturalProduct,
          additionalData: review.additionalData,
        );

        notifyListeners();
      }
    }
  }

  // Natural/Organic Verification Methods

  // Verify product as natural/organic (ADMIN ONLY)
  Future<void> verifyProduct({
    required String productId,
    required bool isNatural,
    required bool isOrganic,
    required bool isCertified,
    required List<String> certifications,
    required String verifiedBy,
    required double naturalScore,
    required double organicScore,
    required Map<String, bool> ingredientAnalysis,
  }) async {
    final verification = ProductVerification(
      productId: productId,
      isNatural: isNatural,
      isOrganic: isOrganic,
      isCertified: isCertified,
      certifications: certifications,
      verifiedDate: DateTime.now(),
      verifiedBy: verifiedBy,
      naturalScore: naturalScore.clamp(0.0, 100.0),
      organicScore: organicScore.clamp(0.0, 100.0),
      ingredientAnalysis: ingredientAnalysis,
    );

    // Remove existing verification if any
    _verifications.removeWhere((v) => v.productId == productId);
    _verifications.add(verification);

    notifyListeners();
  }

  // Get product verification
  ProductVerification? getProductVerification(String productId) {
    try {
      return _verifications.firstWhere((v) => v.productId == productId);
    } catch (e) {
      return null;
    }
  }

  // Get natural/organic statistics
  Map<String, dynamic> getNaturalOrganicStats() {
    final totalProducts = _products.length;
    final verifiedProducts = _verifications.length;
    final naturalProducts = _verifications.where((v) => v.isNatural).length;
    final organicProducts = _verifications.where((v) => v.isOrganic).length;
    final certifiedProducts = _verifications.where((v) => v.isCertified).length;

    final averageNaturalScore = _verifications.isNotEmpty
      ? _verifications.fold(0.0, (sum, v) => sum + v.naturalScore) / _verifications.length
      : 0.0;

    final averageOrganicScore = _verifications.isNotEmpty
      ? _verifications.fold(0.0, (sum, v) => sum + v.organicScore) / _verifications.length
      : 0.0;

    return {
      'totalProducts': totalProducts,
      'verifiedProducts': verifiedProducts,
      'naturalProducts': naturalProducts,
      'organicProducts': organicProducts,
      'certifiedProducts': certifiedProducts,
      'verificationRate': totalProducts > 0 ? (verifiedProducts / totalProducts * 100) : 0.0,
      'naturalRate': verifiedProducts > 0 ? (naturalProducts / verifiedProducts * 100) : 0.0,
      'organicRate': verifiedProducts > 0 ? (organicProducts / verifiedProducts * 100) : 0.0,
      'averageNaturalScore': averageNaturalScore,
      'averageOrganicScore': averageOrganicScore,
    };
  }
}
