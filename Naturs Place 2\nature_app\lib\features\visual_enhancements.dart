import 'package:flutter/material.dart';
import '../providers/app_state.dart';

/// Visual enhancement features for the plant encyclopedia
class VisualEnhancementService {
  
  /// Get enhanced plant images with multiple views
  static PlantImageGallery getPlantImageGallery(Plant plant) {
    return PlantImageGallery(
      plantId: plant.id,
      plantName: plant.name,
      images: _generatePlantImages(plant),
      primaryImage: plant.imageUrl,
    );
  }
  
  /// Generate multiple image views for a plant
  static List<PlantImage> _generatePlantImages(Plant plant) {
    List<PlantImage> images = [];
    
    // Primary plant image
    images.add(PlantImage(
      url: plant.imageUrl,
      type: ImageType.wholePlant,
      description: "Full ${plant.name} plant",
      isHighRes: true,
    ));
    
    // Generate additional images based on plant type
    if (plant.category.contains('Mushroom')) {
      images.addAll(_generateMushroomImages(plant));
    } else if (plant.category.contains('Tree')) {
      images.addAll(_generateTreeImages(plant));
    } else if (plant.category.contains('Herb')) {
      images.addAll(_generateHerbImages(plant));
    } else if (plant.category.contains('Root')) {
      images.addAll(_generateRootImages(plant));
    }
    
    return images;
  }
  
  static List<PlantImage> _generateMushroomImages(Plant plant) {
    // Get mushroom-specific images based on plant name
    final mushroomImages = _getMushroomImageUrls(plant.name);

    return [
      PlantImage(
        url: mushroomImages['closeUp']!,
        type: ImageType.closeUp,
        description: "${plant.name} close-up view",
        isHighRes: true,
      ),
      PlantImage(
        url: mushroomImages['habitat']!,
        type: ImageType.habitat,
        description: "${plant.name} in natural habitat",
        isHighRes: false,
      ),
      PlantImage(
        url: mushroomImages['preparation']!,
        type: ImageType.preparation,
        description: "${plant.name} prepared for use",
        isHighRes: false,
      ),
    ];
  }
  
  static List<PlantImage> _generateTreeImages(Plant plant) {
    // Get tree-specific images based on plant name
    final treeImages = _getTreeImageUrls(plant.name);

    return [
      PlantImage(
        url: treeImages['leaves']!,
        type: ImageType.leaves,
        description: "${plant.name} leaves detail",
        isHighRes: true,
      ),
      PlantImage(
        url: treeImages['bark']!,
        type: ImageType.bark,
        description: "${plant.name} bark texture",
        isHighRes: false,
      ),
      PlantImage(
        url: treeImages['habitat']!,
        type: ImageType.habitat,
        description: "${plant.name} in forest",
        isHighRes: false,
      ),
    ];
  }
  
  static List<PlantImage> _generateHerbImages(Plant plant) {
    // Get herb-specific images based on plant name
    final herbImages = _getHerbImageUrls(plant.name);

    return [
      PlantImage(
        url: herbImages['leaves']!,
        type: ImageType.leaves,
        description: "${plant.name} leaf structure",
        isHighRes: true,
      ),
      PlantImage(
        url: herbImages['flowers']!,
        type: ImageType.flowers,
        description: "${plant.name} flowers",
        isHighRes: false,
      ),
      PlantImage(
        url: herbImages['dried']!,
        type: ImageType.dried,
        description: "Dried ${plant.name}",
        isHighRes: false,
      ),
    ];
  }
  
  static List<PlantImage> _generateRootImages(Plant plant) {
    // Get root-specific images based on plant name
    final rootImages = _getRootImageUrls(plant.name);

    return [
      PlantImage(
        url: rootImages['roots']!,
        type: ImageType.roots,
        description: "${plant.name} root system",
        isHighRes: true,
      ),
      PlantImage(
        url: rootImages['crossSection']!,
        type: ImageType.crossSection,
        description: "${plant.name} root cross-section",
        isHighRes: false,
      ),
      PlantImage(
        url: rootImages['powder']!,
        type: ImageType.powder,
        description: "${plant.name} powder",
        isHighRes: false,
      ),
    ];
  }
  
  /// Get color scheme based on plant properties
  static PlantColorScheme getPlantColorScheme(Plant plant) {
    // Determine colors based on plant characteristics
    Color primaryColor;
    Color accentColor;
    Color backgroundColor;
    
    if (plant.category.contains('Mushroom')) {
      primaryColor = const Color(0xFF8B4513); // Saddle brown
      accentColor = const Color(0xFFDEB887); // Burlywood
      backgroundColor = const Color(0xFFF5F5DC); // Beige
    } else if (plant.benefits.contains('Stress Relief')) {
      primaryColor = const Color(0xFF9370DB); // Medium purple
      accentColor = const Color(0xFFE6E6FA); // Lavender
      backgroundColor = const Color(0xFFF8F8FF); // Ghost white
    } else if (plant.benefits.contains('Energy Enhancement')) {
      primaryColor = const Color(0xFFFF6347); // Tomato
      accentColor = const Color(0xFFFFE4B5); // Moccasin
      backgroundColor = const Color(0xFFFFF8DC); // Cornsilk
    } else if (plant.benefits.contains('Immune Support')) {
      primaryColor = const Color(0xFF32CD32); // Lime green
      accentColor = const Color(0xFF98FB98); // Pale green
      backgroundColor = const Color(0xFFF0FFF0); // Honeydew
    } else if (plant.origin.toLowerCase().contains('amazon')) {
      primaryColor = const Color(0xFF228B22); // Forest green
      accentColor = const Color(0xFF90EE90); // Light green
      backgroundColor = const Color(0xFFF5FFFA); // Mint cream
    } else if (plant.origin.toLowerCase().contains('arctic')) {
      primaryColor = const Color(0xFF4682B4); // Steel blue
      accentColor = const Color(0xFFB0E0E6); // Powder blue
      backgroundColor = const Color(0xFFF0F8FF); // Alice blue
    } else {
      // Default earth tones
      primaryColor = const Color(0xFF8FBC8F); // Dark sea green
      accentColor = const Color(0xFFDDA0DD); // Plum
      backgroundColor = const Color(0xFFFAF0E6); // Linen
    }
    
    return PlantColorScheme(
      primary: primaryColor,
      accent: accentColor,
      background: backgroundColor,
      text: Colors.black87,
      textSecondary: Colors.black54,
    );
  }
  
  /// Get plant icons based on category
  static IconData getPlantIcon(Plant plant) {
    if (plant.category.contains('Mushroom')) {
      return Icons.eco; // Mushroom-like icon
    } else if (plant.category.contains('Tree')) {
      return Icons.park;
    } else if (plant.category.contains('Root')) {
      return Icons.grass;
    } else if (plant.category.contains('Flower')) {
      return Icons.local_florist;
    } else if (plant.category.contains('Herb')) {
      return Icons.spa;
    } else if (plant.category.contains('Fruit')) {
      return Icons.apple;
    } else {
      return Icons.nature;
    }
  }
  
  /// Get benefit icons
  static IconData getBenefitIcon(String benefit) {
    switch (benefit.toLowerCase()) {
      case 'stress relief':
        return Icons.self_improvement;
      case 'immune support':
        return Icons.shield;
      case 'energy enhancement':
        return Icons.bolt;
      case 'cognitive function':
        return Icons.psychology;
      case 'heart health':
        return Icons.favorite;
      case 'digestive aid':
        return Icons.restaurant;
      case 'anti-inflammatory':
        return Icons.healing;
      case 'antioxidant':
        return Icons.auto_awesome;
      case 'sleep quality':
        return Icons.bedtime;
      case 'mood support':
        return Icons.sentiment_very_satisfied;
      default:
        return Icons.health_and_safety;
    }
  }
  
  /// Get rarity badge
  static RarityBadge getRarityBadge(Plant plant) {
    if (plant.isEndangered == true) {
      return RarityBadge(
        label: "ENDANGERED",
        color: Colors.red,
        icon: Icons.warning,
        description: "This plant is threatened in the wild",
      );
    } else if (plant.conservationStatus.contains('Near Threatened')) {
      return RarityBadge(
        label: "RARE",
        color: Colors.orange,
        icon: Icons.star,
        description: "This plant is becoming rare",
      );
    } else if (plant.conservationStatus.contains('Vulnerable')) {
      return RarityBadge(
        label: "VULNERABLE",
        color: Colors.amber,
        icon: Icons.eco,
        description: "This plant needs protection",
      );
    } else {
      return RarityBadge(
        label: "COMMON",
        color: Colors.green,
        icon: Icons.check_circle,
        description: "This plant is widely available",
      );
    }
  }

  /// Get mushroom-specific image URLs
  static Map<String, String> _getMushroomImageUrls(String plantName) {
    final mushroomImageMap = {
      'Reishi': {
        'closeUp': 'https://cdn.pixabay.com/photo/2019/11/07/21/32/reishi-4611052_1280.jpg',
        'habitat': 'https://cdn.pixabay.com/photo/2019/09/17/18/48/reishi-habitat-4484363_1280.jpg',
        'preparation': 'https://cdn.pixabay.com/photo/2018/03/17/19/36/reishi-powder-3234344_1280.jpg',
      },
      'Shiitake': {
        'closeUp': 'https://cdn.pixabay.com/photo/2017/09/16/19/21/shiitake-2756645_1280.jpg',
        'habitat': 'https://cdn.pixabay.com/photo/2019/09/17/18/48/shiitake-habitat-4484363_1280.jpg',
        'preparation': 'https://cdn.pixabay.com/photo/2018/03/17/19/36/shiitake-dried-3234344_1280.jpg',
      },
      'Cordyceps': {
        'closeUp': 'https://cdn.pixabay.com/photo/2019/09/17/18/48/cordyceps-4484363_1280.jpg',
        'habitat': 'https://cdn.pixabay.com/photo/2019/11/07/21/32/cordyceps-habitat-4611052_1280.jpg',
        'preparation': 'https://cdn.pixabay.com/photo/2018/03/17/19/36/cordyceps-powder-3234344_1280.jpg',
      },
      'Chaga': {
        'closeUp': 'https://cdn.pixabay.com/photo/2019/09/17/18/48/chaga-4484363_1280.jpg',
        'habitat': 'https://cdn.pixabay.com/photo/2019/11/07/21/32/chaga-habitat-4611052_1280.jpg',
        'preparation': 'https://cdn.pixabay.com/photo/2018/03/17/19/36/chaga-tea-3234344_1280.jpg',
      },
      'Lion\'s Mane': {
        'closeUp': 'https://cdn.pixabay.com/photo/2019/09/17/18/48/lions-mane-4484363_1280.jpg',
        'habitat': 'https://cdn.pixabay.com/photo/2019/11/07/21/32/lions-mane-habitat-4611052_1280.jpg',
        'preparation': 'https://cdn.pixabay.com/photo/2018/03/17/19/36/lions-mane-extract-3234344_1280.jpg',
      },
    };

    return mushroomImageMap[plantName] ?? {
      'closeUp': 'https://cdn.pixabay.com/photo/2019/09/17/18/48/mushroom-4484363_1280.jpg',
      'habitat': 'https://cdn.pixabay.com/photo/2019/11/07/21/32/mushroom-habitat-4611052_1280.jpg',
      'preparation': 'https://cdn.pixabay.com/photo/2018/03/17/19/36/mushroom-powder-3234344_1280.jpg',
    };
  }

  /// Get tree-specific image URLs
  static Map<String, String> _getTreeImageUrls(String plantName) {
    final treeImageMap = {
      'Ginkgo': {
        'leaves': 'https://cdn.pixabay.com/photo/2017/10/10/07/48/ginkgo-2836488_1280.jpg',
        'bark': 'https://cdn.pixabay.com/photo/2018/05/15/21/24/ginkgo-bark-3404181_1280.jpg',
        'habitat': 'https://cdn.pixabay.com/photo/2019/08/21/15/34/ginkgo-forest-4421847_1280.jpg',
      },
      'Ginkgo Biloba': {
        'leaves': 'https://cdn.pixabay.com/photo/2017/10/10/07/48/ginkgo-2836488_1280.jpg',
        'bark': 'https://cdn.pixabay.com/photo/2018/05/15/21/24/ginkgo-bark-3404181_1280.jpg',
        'habitat': 'https://cdn.pixabay.com/photo/2019/08/21/15/34/ginkgo-forest-4421847_1280.jpg',
      },
      'Hawthorn': {
        'leaves': 'https://cdn.pixabay.com/photo/2019/05/20/09/42/hawthorn-4216068_1280.jpg',
        'bark': 'https://cdn.pixabay.com/photo/2018/05/15/21/24/hawthorn-bark-3404181_1280.jpg',
        'habitat': 'https://cdn.pixabay.com/photo/2019/08/21/15/34/hawthorn-forest-4421847_1280.jpg',
      },
      'Slippery Elm': {
        'leaves': 'https://cdn.pixabay.com/photo/2018/05/15/21/24/elm-3404651_1280.jpg',
        'bark': 'https://cdn.pixabay.com/photo/2018/05/15/21/24/elm-bark-3404181_1280.jpg',
        'habitat': 'https://cdn.pixabay.com/photo/2019/08/21/15/34/elm-forest-4421847_1280.jpg',
      },
      'Tea Tree': {
        'leaves': 'https://cdn.pixabay.com/photo/2017/08/07/14/02/tea-tree-2604616_1280.jpg',
        'bark': 'https://cdn.pixabay.com/photo/2018/05/15/21/24/tea-tree-bark-3404181_1280.jpg',
        'habitat': 'https://cdn.pixabay.com/photo/2019/08/21/15/34/tea-tree-forest-4421847_1280.jpg',
      },
      'Eucalyptus': {
        'leaves': 'https://cdn.pixabay.com/photo/2016/11/29/12/45/eucalyptus-1869227_1280.jpg',
        'bark': 'https://cdn.pixabay.com/photo/2018/05/15/21/24/eucalyptus-bark-3404181_1280.jpg',
        'habitat': 'https://cdn.pixabay.com/photo/2019/08/21/15/34/eucalyptus-forest-4421847_1280.jpg',
      },
    };

    return treeImageMap[plantName] ?? {
      'leaves': 'https://cdn.pixabay.com/photo/2018/05/15/21/24/tree-leaves-3404181_1280.jpg',
      'bark': 'https://cdn.pixabay.com/photo/2019/08/21/15/34/tree-bark-4421847_1280.jpg',
      'habitat': 'https://cdn.pixabay.com/photo/2017/08/07/14/02/forest-2604616_1280.jpg',
    };
  }

  /// Get herb-specific image URLs
  static Map<String, String> _getHerbImageUrls(String plantName) {
    final herbImageMap = {
      'Holy Basil': {
        'leaves': 'https://cdn.pixabay.com/photo/2018/10/15/19/52/tulsi-3749933_1280.jpg',
        'flowers': 'https://cdn.pixabay.com/photo/2018/05/15/21/24/tulsi-flowers-3404181_1280.jpg',
        'dried': 'https://cdn.pixabay.com/photo/2018/03/17/19/36/tulsi-dried-3234344_1280.jpg',
      },
      'Tulsi': {
        'leaves': 'https://cdn.pixabay.com/photo/2018/05/15/21/24/tulsi-3404181_1280.jpg',
        'flowers': 'https://cdn.pixabay.com/photo/2018/10/15/19/52/tulsi-flowers-3749933_1280.jpg',
        'dried': 'https://cdn.pixabay.com/photo/2018/03/17/19/36/tulsi-dried-3234344_1280.jpg',
      },
      'Lemon Balm': {
        'leaves': 'https://cdn.pixabay.com/photo/2018/07/01/20/01/lemon-balm-3510072_1280.jpg',
        'flowers': 'https://cdn.pixabay.com/photo/2018/05/15/21/24/lemon-balm-flowers-3404181_1280.jpg',
        'dried': 'https://cdn.pixabay.com/photo/2018/03/17/19/36/lemon-balm-dried-3234344_1280.jpg',
      },
      'Lavender': {
        'leaves': 'https://cdn.pixabay.com/photo/2015/07/02/20/57/lavender-830922_1280.jpg',
        'flowers': 'https://cdn.pixabay.com/photo/2018/05/15/21/24/lavender-flowers-3404181_1280.jpg',
        'dried': 'https://cdn.pixabay.com/photo/2018/03/17/19/36/lavender-dried-3234344_1280.jpg',
      },
      'Rosemary': {
        'leaves': 'https://cdn.pixabay.com/photo/2015/05/30/21/20/rosemary-791120_1280.jpg',
        'flowers': 'https://cdn.pixabay.com/photo/2018/05/15/21/24/rosemary-flowers-3404181_1280.jpg',
        'dried': 'https://cdn.pixabay.com/photo/2018/03/17/19/36/rosemary-dried-3234344_1280.jpg',
      },
      'Oregano': {
        'leaves': 'https://cdn.pixabay.com/photo/2016/07/26/16/16/oregano-1543495_1280.jpg',
        'flowers': 'https://cdn.pixabay.com/photo/2018/05/15/21/24/oregano-flowers-3404181_1280.jpg',
        'dried': 'https://cdn.pixabay.com/photo/2018/03/17/19/36/oregano-dried-3234344_1280.jpg',
      },
    };

    return herbImageMap[plantName] ?? {
      'leaves': 'https://cdn.pixabay.com/photo/2018/05/15/21/24/herb-leaves-3404181_1280.jpg',
      'flowers': 'https://cdn.pixabay.com/photo/2019/08/21/15/34/herb-flowers-4421847_1280.jpg',
      'dried': 'https://cdn.pixabay.com/photo/2018/03/17/19/36/dried-herbs-3234344_1280.jpg',
    };
  }

  /// Get root-specific image URLs
  static Map<String, String> _getRootImageUrls(String plantName) {
    final rootImageMap = {
      'Ginger': {
        'roots': 'https://cdn.pixabay.com/photo/2017/05/11/19/44/ginger-2305199_1280.jpg',
        'crossSection': 'https://cdn.pixabay.com/photo/2018/05/15/21/24/ginger-cross-section-3404181_1280.jpg',
        'powder': 'https://cdn.pixabay.com/photo/2018/03/17/19/36/ginger-powder-3234344_1280.jpg',
      },
      'Turmeric': {
        'roots': 'https://cdn.pixabay.com/photo/2017/05/11/19/44/turmeric-2305199_1280.jpg',
        'crossSection': 'https://cdn.pixabay.com/photo/2018/05/15/21/24/turmeric-cross-section-3404181_1280.jpg',
        'powder': 'https://cdn.pixabay.com/photo/2018/03/17/19/36/turmeric-powder-3234344_1280.jpg',
      },
      'Ashwagandha': {
        'roots': 'https://cdn.pixabay.com/photo/2019/11/07/21/32/ashwagandha-4611052_1280.jpg',
        'crossSection': 'https://cdn.pixabay.com/photo/2018/05/15/21/24/ashwagandha-cross-section-3404181_1280.jpg',
        'powder': 'https://cdn.pixabay.com/photo/2018/03/17/19/36/ashwagandha-powder-3234344_1280.jpg',
      },
      'American Ginseng': {
        'roots': 'https://cdn.pixabay.com/photo/2017/09/05/10/20/ginseng-2717004_1280.jpg',
        'crossSection': 'https://cdn.pixabay.com/photo/2018/05/15/21/24/ginseng-cross-section-3404181_1280.jpg',
        'powder': 'https://cdn.pixabay.com/photo/2018/03/17/19/36/ginseng-powder-3234344_1280.jpg',
      },
      'Goldenseal': {
        'roots': 'https://cdn.pixabay.com/photo/2019/08/21/15/34/goldenseal-4421847_1280.jpg',
        'crossSection': 'https://cdn.pixabay.com/photo/2018/05/15/21/24/goldenseal-cross-section-3404181_1280.jpg',
        'powder': 'https://cdn.pixabay.com/photo/2018/03/17/19/36/goldenseal-powder-3234344_1280.jpg',
      },
    };

    return rootImageMap[plantName] ?? {
      'roots': 'https://cdn.pixabay.com/photo/2018/05/15/21/24/plant-roots-3404181_1280.jpg',
      'crossSection': 'https://cdn.pixabay.com/photo/2019/08/21/15/34/root-cross-section-4421847_1280.jpg',
      'powder': 'https://cdn.pixabay.com/photo/2018/03/17/19/36/herbal-powder-3234344_1280.jpg',
    };
  }
}

/// Plant image gallery model
class PlantImageGallery {
  final String plantId;
  final String plantName;
  final List<PlantImage> images;
  final String primaryImage;
  
  PlantImageGallery({
    required this.plantId,
    required this.plantName,
    required this.images,
    required this.primaryImage,
  });
}

/// Plant image model
class PlantImage {
  final String url;
  final ImageType type;
  final String description;
  final bool isHighRes;
  
  PlantImage({
    required this.url,
    required this.type,
    required this.description,
    required this.isHighRes,
  });
}

/// Image type enumeration
enum ImageType {
  wholePlant,
  closeUp,
  leaves,
  flowers,
  roots,
  bark,
  habitat,
  preparation,
  dried,
  powder,
  crossSection,
}

/// Plant color scheme model
class PlantColorScheme {
  final Color primary;
  final Color accent;
  final Color background;
  final Color text;
  final Color textSecondary;
  
  PlantColorScheme({
    required this.primary,
    required this.accent,
    required this.background,
    required this.text,
    required this.textSecondary,
  });
}

/// Rarity badge model
class RarityBadge {
  final String label;
  final Color color;
  final IconData icon;
  final String description;
  
  RarityBadge({
    required this.label,
    required this.color,
    required this.icon,
    required this.description,
  });
}
