import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

/// Utility functions for performance optimization
class PerformanceUtils {
  /// Debounce function calls to prevent excessive execution
  static Timer? _debounceTimer;
  
  static void debounce(Duration delay, VoidCallback callback) {
    _debounceTimer?.cancel();
    _debounceTimer = Timer(delay, callback);
  }

  /// Throttle function calls to limit execution frequency
  static DateTime? _lastThrottleTime;
  
  static void throttle(Duration interval, VoidCallback callback) {
    final now = DateTime.now();
    if (_lastThrottleTime == null || 
        now.difference(_lastThrottleTime!) >= interval) {
      _lastThrottleTime = now;
      callback();
    }
  }

  /// Batch multiple operations for better performance
  static Future<List<T>> batchOperations<T>(
    List<Future<T> Function()> operations, {
    int batchSize = 5,
    Duration batchDelay = const Duration(milliseconds: 10),
  }) async {
    final results = <T>[];
    
    for (int i = 0; i < operations.length; i += batchSize) {
      final batch = operations.skip(i).take(batchSize);
      final batchResults = await Future.wait(
        batch.map((op) => op()),
        eagerError: false,
      );
      results.addAll(batchResults);
      
      // Small delay between batches
      if (i + batchSize < operations.length) {
        await Future.delayed(batchDelay);
      }
    }
    
    return results;
  }

  /// Optimize widget rebuilds with memoization
  static final Map<String, dynamic> _memoCache = {};
  
  static T memoize<T>(String key, T Function() computation) {
    if (_memoCache.containsKey(key)) {
      return _memoCache[key] as T;
    }
    
    final result = computation();
    _memoCache[key] = result;
    
    // Limit cache size
    if (_memoCache.length > 100) {
      final oldestKey = _memoCache.keys.first;
      _memoCache.remove(oldestKey);
    }
    
    return result;
  }

  /// Clear memoization cache
  static void clearMemoCache() {
    _memoCache.clear();
  }

  /// Optimize list operations
  static List<T> optimizedWhere<T>(
    List<T> list,
    bool Function(T) test, {
    int? limit,
  }) {
    final result = <T>[];
    int count = 0;
    
    for (final item in list) {
      if (test(item)) {
        result.add(item);
        count++;
        
        if (limit != null && count >= limit) {
          break;
        }
      }
    }
    
    return result;
  }

  /// Optimize map operations
  static List<R> optimizedMap<T, R>(
    List<T> list,
    R Function(T) transform, {
    int? limit,
  }) {
    final result = <R>[];
    int count = 0;
    
    for (final item in list) {
      result.add(transform(item));
      count++;
      
      if (limit != null && count >= limit) {
        break;
      }
    }
    
    return result;
  }

  /// Precompute expensive operations
  static final Map<String, dynamic> _precomputedCache = {};
  
  static T precompute<T>(String key, T Function() computation) {
    if (_precomputedCache.containsKey(key)) {
      return _precomputedCache[key] as T;
    }
    
    final result = computation();
    _precomputedCache[key] = result;
    return result;
  }

  /// Clear precomputed cache
  static void clearPrecomputedCache() {
    _precomputedCache.clear();
  }

  /// Optimize image loading
  static Future<void> preloadImages(List<String> imagePaths) async {
    final futures = imagePaths.map((path) async {
      try {
        if (path.startsWith('http')) {
          await precacheImage(NetworkImage(path), 
            WidgetsBinding.instance.rootElement!);
        } else {
          await precacheImage(AssetImage(path), 
            WidgetsBinding.instance.rootElement!);
        }
      } catch (e) {
        debugPrint('❌ Error preloading image $path: $e');
      }
    });
    
    await Future.wait(futures, eagerError: false);
  }

  /// Memory optimization
  static void optimizeMemory() {
    // Clear various caches
    clearMemoCache();
    clearPrecomputedCache();
    
    // Force garbage collection in debug mode
    if (kDebugMode) {
      // This is a hint to the garbage collector
      // The actual GC is controlled by the Dart VM
    }
  }

  /// Platform-specific optimizations
  static Future<void> applyPlatformOptimizations() async {
    if (Platform.isAndroid) {
      await _applyAndroidOptimizations();
    } else if (Platform.isIOS) {
      await _applyIOSOptimizations();
    }
  }

  static Future<void> _applyAndroidOptimizations() async {
    try {
      // Enable hardware acceleration
      SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);
      
      // Optimize for performance
      await SystemChrome.setPreferredOrientations([
        DeviceOrientation.portraitUp,
        DeviceOrientation.portraitDown,
      ]);
    } catch (e) {
      debugPrint('❌ Error applying Android optimizations: $e');
    }
  }

  static Future<void> _applyIOSOptimizations() async {
    try {
      // iOS-specific optimizations
      SystemChrome.setSystemUIOverlayStyle(
        const SystemUiOverlayStyle(
          statusBarBrightness: Brightness.light,
        ),
      );
    } catch (e) {
      debugPrint('❌ Error applying iOS optimizations: $e');
    }
  }

  /// Network optimization
  static bool shouldMakeNetworkRequest() {
    // Check network conditions and device state
    // Return false if device is in low power mode or poor network
    return true; // Simplified for now
  }

  /// Battery optimization
  static bool isLowPowerMode() {
    // Check if device is in low power mode
    // Reduce animations and background tasks if true
    return false; // Simplified for now
  }

  /// Performance monitoring
  static void logPerformanceMetric(String name, double value) {
    if (kDebugMode) {
      debugPrint('📊 Performance: $name = ${value.toStringAsFixed(2)}');
    }
  }

  /// Widget performance wrapper
  static Widget wrapWithPerformanceMonitoring(
    Widget child,
    String name,
  ) {
    if (kDebugMode) {
      return _PerformanceWrapper(
        name: name,
        child: child,
      );
    }
    return child;
  }
}

/// Performance wrapper widget for monitoring
class _PerformanceWrapper extends StatefulWidget {
  final String name;
  final Widget child;

  const _PerformanceWrapper({
    required this.name,
    required this.child,
  });

  @override
  State<_PerformanceWrapper> createState() => _PerformanceWrapperState();
}

class _PerformanceWrapperState extends State<_PerformanceWrapper> {
  late DateTime _startTime;

  @override
  void initState() {
    super.initState();
    _startTime = DateTime.now();
  }

  @override
  Widget build(BuildContext context) {
    final buildTime = DateTime.now().difference(_startTime).inMicroseconds / 1000.0;
    PerformanceUtils.logPerformanceMetric('${widget.name}_build_time', buildTime);
    return widget.child;
  }
}

/// Security utilities for performance optimization
class SecurityUtils {
  /// Validate input for security while maintaining performance
  static bool isValidInput(String input, {int maxLength = 1000}) {
    if (input.length > maxLength) return false;
    
    // Check for common injection patterns
    final dangerousPatterns = [
      '<script',
      'javascript:',
      'data:',
      'vbscript:',
      'onload=',
      'onerror=',
    ];
    
    final lowerInput = input.toLowerCase();
    return !dangerousPatterns.any((pattern) => lowerInput.contains(pattern));
  }

  /// Sanitize input for safe processing
  static String sanitizeInput(String input) {
    return input
        .replaceAll(RegExp(r'[<>"]'), '')
        .replaceAll(RegExp(r"'"), '')
        .replaceAll(RegExp(r'javascript:', caseSensitive: false), '')
        .replaceAll(RegExp(r'data:', caseSensitive: false), '')
        .trim();
  }

  /// Rate limiting for API calls
  static final Map<String, DateTime> _lastApiCalls = {};
  static const Duration _minApiInterval = Duration(seconds: 1);

  static bool canMakeApiCall(String endpoint) {
    final now = DateTime.now();
    final lastCall = _lastApiCalls[endpoint];
    
    if (lastCall == null || now.difference(lastCall) >= _minApiInterval) {
      _lastApiCalls[endpoint] = now;
      return true;
    }
    
    return false;
  }

  /// Secure random string generation
  static String generateSecureToken(int length) {
    const chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    final random = DateTime.now().millisecondsSinceEpoch;
    
    return List.generate(length, (index) {
      final charIndex = (random + index) % chars.length;
      return chars[charIndex];
    }).join();
  }
}

/// Animation optimization utilities
class AnimationUtils {
  /// Reduce animations on low-end devices
  static Duration getOptimizedDuration(Duration original) {
    if (PerformanceUtils.isLowPowerMode()) {
      return Duration(milliseconds: (original.inMilliseconds * 0.5).round());
    }
    return original;
  }

  /// Optimize animation curves for performance
  static Curve getOptimizedCurve(Curve original) {
    if (PerformanceUtils.isLowPowerMode()) {
      return Curves.linear; // Simpler curve for better performance
    }
    return original;
  }

  /// Create performance-optimized animations
  static AnimationController createOptimizedController({
    required Duration duration,
    required TickerProvider vsync,
  }) {
    return AnimationController(
      duration: getOptimizedDuration(duration),
      vsync: vsync,
    );
  }
}
