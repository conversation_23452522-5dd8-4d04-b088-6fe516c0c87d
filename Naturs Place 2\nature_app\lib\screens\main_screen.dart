import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import '../providers/app_state.dart';
import 'home_screen.dart';
import 'marketplace_screen.dart';
import 'encyclopedia_screen.dart';
import 'ai_assistant_screen.dart';

class MainScreen extends StatelessWidget {
  const MainScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<AppState>(
      builder: (context, appState, child) {
        return Scaffold(
          body: IndexedStack(
            index: appState.currentIndex,
            children: const [
              HomeScreen(),
              MarketplaceScreen(),
              EncyclopediaScreen(),
              AIAssistantScreen(),
            ],
          ),
          bottomNavigationBar: BottomNavigationBar(
            currentIndex: appState.currentIndex,
            onTap: appState.setCurrentIndex,
            type: BottomNavigationBarType.fixed,
            items: const [
              BottomNavigationBarItem(
                icon: Icon(Icons.home),
                label: 'Home',
              ),
              BottomNavigationBarItem(
                icon: Icon(Icons.store),
                label: 'Marketplace',
              ),
              BottomNavigationBarItem(
                icon: FaIcon(FontAwesomeIcons.leaf, size: 20),
                label: 'Encyclopedia',
              ),
              BottomNavigationBarItem(
                icon: FaIcon(FontAwesomeIcons.robot, size: 20),
                label: 'AI Assistant',
              ),
            ],
          ),
        );
      },
    );
  }
}
