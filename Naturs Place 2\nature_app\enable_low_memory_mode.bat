@echo off
echo 🧠 Enabling Low-Memory Mode for Nature's Place App 🧠
echo.

echo Backing up current gradle.properties...
if exist "android\gradle.properties" (
    copy "android\gradle.properties" "android\gradle.properties.backup"
    echo ✅ Backup created: gradle.properties.backup
) else (
    echo ⚠️ No existing gradle.properties found
)
echo.

echo Switching to low-memory configuration...
copy "android\gradle-low-memory.properties" "android\gradle.properties"
echo ✅ Low-memory mode enabled
echo.

echo Current memory settings:
type "android\gradle.properties"
echo.

echo 🎯 Low-memory mode is now active!
echo This configuration uses minimal memory settings:
echo - Maximum heap: 1GB
echo - Metaspace: 512MB
echo - Parallel builds: Disabled
echo - Gradle daemon: Disabled
echo.

echo To restore normal mode, run: restore_normal_mode.bat
pause
