import 'package:flutter/material.dart';
import '../providers/app_state.dart';
import '../screens/plant_detail_screen.dart';

class InteractivePlantLearning extends StatefulWidget {
  final Plant plant;

  const InteractivePlantLearning({
    super.key,
    required this.plant,
  });

  @override
  State<InteractivePlantLearning> createState() => _InteractivePlantLearningState();
}

class _InteractivePlantLearningState extends State<InteractivePlantLearning>
    with TickerProviderStateMixin {
  late TabController _tabController;
  int _currentQuizQuestion = 0;
  List<bool> _quizAnswers = [];
  bool _showQuizResults = false;

  final List<Map<String, dynamic>> _quizQuestions = [
    {
      'question': 'What is the primary benefit of this plant?',
      'options': ['Pain relief', 'Digestive health', 'Immune support', 'Sleep aid'],
      'correct': 1,
    },
    {
      'question': 'Which part of the plant is typically used?',
      'options': ['Roots', 'Leaves', 'Flowers', 'Bark'],
      'correct': 1,
    },
    {
      'question': 'What is the recommended preparation method?',
      'options': ['Raw consumption', 'Tea infusion', 'Topical application', 'Smoking'],
      'correct': 1,
    },
  ];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _quizAnswers = List.filled(_quizQuestions.length, false);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.8,
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      child: Column(
        children: [
          _buildHeader(),
          _buildTabBar(),
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildOverviewTab(),
                _buildBenefitsTab(),
                _buildQuizTab(),
                _buildResourcesTab(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            const Color(0xFF22c55e),
            const Color(0xFF22c55e).withValues(alpha: 0.8),
          ],
        ),
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      child: Row(
        children: [
          Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              image: DecorationImage(
                image: NetworkImage(widget.plant.imageUrl),
                fit: BoxFit.cover,
              ),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  widget.plant.name,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  widget.plant.scientificName,
                  style: const TextStyle(
                    color: Colors.white70,
                    fontSize: 14,
                    fontStyle: FontStyle.italic,
                  ),
                ),
              ],
            ),
          ),
          IconButton(
            onPressed: () => Navigator.of(context).pop(),
            icon: const Icon(Icons.close, color: Colors.white),
          ),
        ],
      ),
    );
  }

  Widget _buildTabBar() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.grey[100],
        border: Border(
          bottom: BorderSide(color: Colors.grey[300]!),
        ),
      ),
      child: TabBar(
        controller: _tabController,
        isScrollable: true,
        labelColor: const Color(0xFF22c55e),
        unselectedLabelColor: Colors.grey[600],
        indicatorColor: const Color(0xFF22c55e),
        tabs: const [
          Tab(icon: Icon(Icons.info_outline), text: 'Overview'),
          Tab(icon: Icon(Icons.healing), text: 'Benefits'),
          Tab(icon: Icon(Icons.quiz), text: 'Quiz'),
          Tab(icon: Icon(Icons.library_books), text: 'Resources'),
        ],
      ),
    );
  }

  Widget _buildOverviewTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildLearningCard(
            'Plant Description',
            Icons.description,
            widget.plant.description,
          ),
          const SizedBox(height: 16),
          _buildLearningCard(
            'Origin & Habitat',
            Icons.public,
            'Native to ${widget.plant.origin}. This plant thrives in temperate climates and has been cultivated for centuries for its medicinal properties.',
          ),
          const SizedBox(height: 16),
          _buildLearningCard(
            'Traditional Uses',
            Icons.history_edu,
            'Traditionally used by indigenous peoples for various health conditions. The plant has a rich history in folk medicine and continues to be studied for its therapeutic potential.',
          ),
          const SizedBox(height: 16),
          ElevatedButton.icon(
            onPressed: () {
              Navigator.of(context).pop();
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => PlantDetailScreen(plant: widget.plant),
                ),
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF22c55e),
              foregroundColor: Colors.white,
              minimumSize: const Size(double.infinity, 48),
            ),
            icon: const Icon(Icons.article),
            label: const Text('View Full Details'),
          ),
        ],
      ),
    );
  }

  Widget _buildBenefitsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Health Benefits',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Color(0xFF22c55e),
            ),
          ),
          const SizedBox(height: 16),
          ...widget.plant.benefits.map((benefit) {
            return _buildBenefitCard(benefit);
          }),
          const SizedBox(height: 16),
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.orange.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: Colors.orange.withValues(alpha: 0.3),
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.warning_amber,
                      color: Colors.orange[700],
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'Important Safety Note',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: Colors.orange[700],
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Text(
                  'Always consult with a healthcare professional before using any herbal remedy. Individual results may vary.',
                  style: TextStyle(
                    color: Colors.orange[700],
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuizTab() {
    if (_showQuizResults) {
      return _buildQuizResults();
    }

    return Padding(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          LinearProgressIndicator(
            value: (_currentQuizQuestion + 1) / _quizQuestions.length,
            backgroundColor: Colors.grey[300],
            valueColor: const AlwaysStoppedAnimation<Color>(Color(0xFF22c55e)),
          ),
          const SizedBox(height: 20),
          Text(
            'Question ${_currentQuizQuestion + 1} of ${_quizQuestions.length}',
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
              color: Colors.grey,
            ),
          ),
          const SizedBox(height: 16),
          Text(
            _quizQuestions[_currentQuizQuestion]['question'],
            style: const TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 24),
          Expanded(
            child: ListView.builder(
              itemCount: _quizQuestions[_currentQuizQuestion]['options'].length,
              itemBuilder: (context, index) {
                return Container(
                  margin: const EdgeInsets.only(bottom: 12),
                  child: ElevatedButton(
                    onPressed: () => _answerQuestion(index),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.white,
                      foregroundColor: Colors.black87,
                      side: BorderSide(color: Colors.grey[300]!),
                      padding: const EdgeInsets.all(16),
                      alignment: Alignment.centerLeft,
                    ),
                    child: Text(
                      _quizQuestions[_currentQuizQuestion]['options'][index],
                      style: const TextStyle(fontSize: 16),
                    ),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildResourcesTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Additional Resources',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Color(0xFF22c55e),
            ),
          ),
          const SizedBox(height: 16),
          _buildResourceCard(
            'Scientific Studies',
            'Access peer-reviewed research',
            Icons.science,
            () {
              // TODO: Open scientific studies
            },
          ),
          _buildResourceCard(
            'Growing Guide',
            'Learn how to cultivate this plant',
            Icons.eco,
            () {
              // TODO: Open growing guide
            },
          ),
          _buildResourceCard(
            'Recipe Collection',
            'Traditional and modern preparations',
            Icons.restaurant_menu,
            () {
              // TODO: Open recipes
            },
          ),
          _buildResourceCard(
            'Expert Consultation',
            'Connect with herbalists',
            Icons.person_outline,
            () {
              // TODO: Open expert consultation
            },
          ),
        ],
      ),
    );
  }

  Widget _buildLearningCard(String title, IconData icon, String content) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: const Color(0xFF22c55e).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    icon,
                    color: const Color(0xFF22c55e),
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Text(
              content,
              style: const TextStyle(fontSize: 14, height: 1.5),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBenefitCard(String benefit) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.green.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.green.withValues(alpha: 0.2),
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.green.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: const Icon(
              Icons.healing,
              color: Colors.green,
              size: 20,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              benefit,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildResourceCard(String title, String subtitle, IconData icon, VoidCallback onTap) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: Card(
        elevation: 2,
        child: ListTile(
          leading: Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: const Color(0xFF22c55e).withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              icon,
              color: const Color(0xFF22c55e),
              size: 24,
            ),
          ),
          title: Text(
            title,
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
          subtitle: Text(subtitle),
          trailing: const Icon(Icons.arrow_forward_ios, size: 16),
          onTap: onTap,
        ),
      ),
    );
  }

  Widget _buildQuizResults() {
    final correctAnswers = _quizAnswers.where((answer) => answer).length;
    final percentage = (correctAnswers / _quizQuestions.length * 100).round();

    return Padding(
      padding: const EdgeInsets.all(20),
      child: Column(
        children: [
          const SizedBox(height: 40),
          Container(
            width: 120,
            height: 120,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: percentage >= 70
                  ? Colors.green.withValues(alpha: 0.1)
                  : Colors.orange.withValues(alpha: 0.1),
            ),
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    '$percentage%',
                    style: TextStyle(
                      fontSize: 32,
                      fontWeight: FontWeight.bold,
                      color: percentage >= 70 ? Colors.green : Colors.orange,
                    ),
                  ),
                  Text(
                    'Score',
                    style: TextStyle(
                      fontSize: 14,
                      color: percentage >= 70 ? Colors.green : Colors.orange,
                    ),
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 24),
          Text(
            percentage >= 70 ? 'Great job!' : 'Keep learning!',
            style: const TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'You got $correctAnswers out of ${_quizQuestions.length} questions correct.',
            style: const TextStyle(fontSize: 16, color: Colors.grey),
          ),
          const SizedBox(height: 32),
          Row(
            children: [
              Expanded(
                child: OutlinedButton(
                  onPressed: _resetQuiz,
                  child: const Text('Try Again'),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: ElevatedButton(
                  onPressed: () {
                    Navigator.of(context).pop();
                    Navigator.of(context).push(
                      MaterialPageRoute(
                        builder: (context) => PlantDetailScreen(plant: widget.plant),
                      ),
                    );
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFF22c55e),
                    foregroundColor: Colors.white,
                  ),
                  child: const Text('Learn More'),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  void _answerQuestion(int selectedIndex) {
    final correct = _quizQuestions[_currentQuizQuestion]['correct'];
    _quizAnswers[_currentQuizQuestion] = selectedIndex == correct;

    if (_currentQuizQuestion < _quizQuestions.length - 1) {
      setState(() {
        _currentQuizQuestion++;
      });
    } else {
      setState(() {
        _showQuizResults = true;
      });
    }
  }

  void _resetQuiz() {
    setState(() {
      _currentQuizQuestion = 0;
      _quizAnswers = List.filled(_quizQuestions.length, false);
      _showQuizResults = false;
    });
  }
}