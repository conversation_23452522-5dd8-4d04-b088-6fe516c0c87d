import 'package:flutter/foundation.dart';
import 'dart:convert';
import 'dart:io';
import '../models/commission_models.dart';

/// Stripe integration service for automated partner payouts
class StripeIntegrationService {
  static final StripeIntegrationService _instance = StripeIntegrationService._internal();
  factory StripeIntegrationService() => _instance;
  StripeIntegrationService._internal();

  late String _secretKey;
  late String _publishableKey;
  late String _webhookSecret;
  bool _isInitialized = false;

  /// Initialize Stripe service with API keys
  Future<void> initialize() async {
    try {
      _secretKey = Platform.environment['STRIPE_SECRET_KEY'] ?? '';
      _publishableKey = Platform.environment['STRIPE_PUBLISHABLE_KEY'] ?? '';
      _webhookSecret = Platform.environment['STRIPE_WEBHOOK_SECRET'] ?? '';

      if (_secretKey.isEmpty || _publishableKey.isEmpty) {
        throw Exception('Stripe API keys not configured');
      }

      _isInitialized = true;
      debugPrint('✅ Stripe Integration Service initialized');
    } catch (e) {
      debugPrint('❌ Failed to initialize Stripe service: $e');
      rethrow;
    }
  }

  /// Create Stripe Connect account for partner
  Future<String> createPartnerAccount(Map<String, dynamic> partnerInfo) async {
    if (!_isInitialized) throw Exception('Stripe service not initialized');

    try {
      final accountData = {
        'type': 'express',
        'country': partnerInfo['country'] ?? 'US',
        'email': partnerInfo['email'],
        'capabilities': {
          'transfers': {'requested': true},
        },
        'business_type': 'individual',
        'individual': {
          'first_name': partnerInfo['first_name'],
          'last_name': partnerInfo['last_name'],
          'email': partnerInfo['email'],
        },
        'settings': {
          'payouts': {
            'schedule': {
              'interval': 'manual', // We control payouts
            },
          },
        },
      };

      final response = await _makeStripeRequest(
        'POST',
        'accounts',
        accountData,
      );

      final accountId = response['id'] as String;
      debugPrint('✅ Created Stripe account for partner: $accountId');
      return accountId;
    } catch (e) {
      debugPrint('❌ Failed to create partner account: $e');
      rethrow;
    }
  }

  /// Create account link for partner onboarding
  Future<String> createAccountLink(String accountId, String returnUrl, String refreshUrl) async {
    if (!_isInitialized) throw Exception('Stripe service not initialized');

    try {
      final linkData = {
        'account': accountId,
        'return_url': returnUrl,
        'refresh_url': refreshUrl,
        'type': 'account_onboarding',
      };

      final response = await _makeStripeRequest(
        'POST',
        'account_links',
        linkData,
      );

      return response['url'] as String;
    } catch (e) {
      debugPrint('❌ Failed to create account link: $e');
      rethrow;
    }
  }

  /// Process 30-day payout to partner
  Future<StripePayoutResult> processPartnerPayout(Payout payout, String stripeAccountId) async {
    if (!_isInitialized) throw Exception('Stripe service not initialized');

    try {
      // Convert to cents for Stripe
      final amountCents = (payout.netAmount * 100).round();
      
      final transferData = {
        'amount': amountCents,
        'currency': 'usd',
        'destination': stripeAccountId,
        'description': 'Nature\'s Place 30-day commission payout',
        'metadata': {
          'payout_id': payout.id,
          'partner_id': payout.partnerId,
          'commission_count': payout.commissionIds.length.toString(),
          'payout_cycle': '30_days',
        },
      };

      final response = await _makeStripeRequest(
        'POST',
        'transfers',
        transferData,
      );

      final transferId = response['id'] as String;
      debugPrint('✅ Processed Stripe payout: $transferId for \$${payout.netAmount.toStringAsFixed(2)}');

      return StripePayoutResult(
        success: true,
        transferId: transferId,
        amount: payout.netAmount,
        fees: payout.fees,
        stripeResponse: response,
      );
    } catch (e) {
      debugPrint('❌ Failed to process payout: $e');
      return StripePayoutResult(
        success: false,
        error: e.toString(),
        amount: payout.netAmount,
        fees: payout.fees,
      );
    }
  }

  /// Get partner account status
  Future<Map<String, dynamic>> getAccountStatus(String accountId) async {
    if (!_isInitialized) throw Exception('Stripe service not initialized');

    try {
      final response = await _makeStripeRequest(
        'GET',
        'accounts/$accountId',
        null,
      );

      return {
        'id': response['id'],
        'charges_enabled': response['charges_enabled'],
        'payouts_enabled': response['payouts_enabled'],
        'details_submitted': response['details_submitted'],
        'requirements': response['requirements'],
      };
    } catch (e) {
      debugPrint('❌ Failed to get account status: $e');
      rethrow;
    }
  }

  /// List recent transfers for partner
  Future<List<Map<String, dynamic>>> getPartnerTransfers(String accountId, {int limit = 10}) async {
    if (!_isInitialized) throw Exception('Stripe service not initialized');

    try {
      final response = await _makeStripeRequest(
        'GET',
        'transfers?destination=$accountId&limit=$limit',
        null,
      );

      return List<Map<String, dynamic>>.from(response['data']);
    } catch (e) {
      debugPrint('❌ Failed to get partner transfers: $e');
      return [];
    }
  }

  /// Handle Stripe webhook
  Future<void> handleWebhook(String payload, String signature) async {
    if (!_isInitialized) throw Exception('Stripe service not initialized');

    try {
      // Verify webhook signature
      if (!_verifyWebhookSignature(payload, signature)) {
        throw Exception('Invalid webhook signature');
      }

      final event = jsonDecode(payload);
      final eventType = event['type'] as String;

      switch (eventType) {
        case 'transfer.created':
          await _handleTransferCreated(event['data']['object']);
          break;
        case 'transfer.paid':
          await _handleTransferPaid(event['data']['object']);
          break;
        case 'transfer.failed':
          await _handleTransferFailed(event['data']['object']);
          break;
        case 'account.updated':
          await _handleAccountUpdated(event['data']['object']);
          break;
        default:
          debugPrint('🔔 Unhandled webhook event: $eventType');
      }
    } catch (e) {
      debugPrint('❌ Failed to handle webhook: $e');
      rethrow;
    }
  }

  /// Calculate Stripe fees for payout amount
  double calculateStripeFees(double amount) {
    // Stripe transfer fees: 0.25% + $0.25 for instant, free for standard
    final payoutMethod = Platform.environment['STRIPE_PAYOUT_METHOD'] ?? 'standard';
    
    if (payoutMethod == 'instant') {
      return (amount * 0.0025) + 0.25; // 0.25% + $0.25
    } else {
      return 0.0; // Standard transfers are free
    }
  }

  /// Get payout statistics
  Future<Map<String, dynamic>> getPayoutStatistics() async {
    try {
      // This would query Stripe for actual statistics
      return {
        'total_payouts_this_month': 1250.0,
        'total_partners_paid': 16,
        'average_payout': 78.13,
        'total_fees': 45.50,
        'success_rate': 0.98,
      };
    } catch (e) {
      debugPrint('❌ Failed to get payout statistics: $e');
      return {};
    }
  }

  // Private helper methods

  Future<Map<String, dynamic>> _makeStripeRequest(
    String method,
    String endpoint,
    Map<String, dynamic>? data,
  ) async {
    // This is a simplified implementation
    // In production, use the official Stripe SDK or HTTP client
    
    // Mock response for development
    if (endpoint == 'accounts') {
      return {'id': 'acct_${DateTime.now().millisecondsSinceEpoch}'};
    } else if (endpoint == 'account_links') {
      return {'url': 'https://connect.stripe.com/setup/e/acct_test'};
    } else if (endpoint == 'transfers') {
      return {
        'id': 'tr_${DateTime.now().millisecondsSinceEpoch}',
        'amount': data?['amount'] ?? 0,
        'currency': 'usd',
        'destination': data?['destination'] ?? '',
      };
    }
    
    return {};
  }

  bool _verifyWebhookSignature(String payload, String signature) {
    // Implement webhook signature verification
    // This is a simplified version - use proper HMAC verification in production
    return signature.isNotEmpty;
  }

  Future<void> _handleTransferCreated(Map<String, dynamic> transfer) async {
    final transferId = transfer['id'];
    final payoutId = transfer['metadata']?['payout_id'];
    
    debugPrint('🔔 Transfer created: $transferId for payout: $payoutId');
    
    // Update payout status in database
    // await _updatePayoutStatus(payoutId, 'processing', transferId);
  }

  Future<void> _handleTransferPaid(Map<String, dynamic> transfer) async {
    final transferId = transfer['id'];
    final payoutId = transfer['metadata']?['payout_id'];
    
    debugPrint('✅ Transfer paid: $transferId for payout: $payoutId');
    
    // Update payout status in database
    // await _updatePayoutStatus(payoutId, 'completed', transferId);
  }

  Future<void> _handleTransferFailed(Map<String, dynamic> transfer) async {
    final transferId = transfer['id'];
    final payoutId = transfer['metadata']?['payout_id'];
    final failureCode = transfer['failure_code'];
    
    debugPrint('❌ Transfer failed: $transferId for payout: $payoutId - $failureCode');
    
    // Update payout status and handle failure
    // await _updatePayoutStatus(payoutId, 'failed', transferId, failureCode);
  }

  Future<void> _handleAccountUpdated(Map<String, dynamic> account) async {
    final accountId = account['id'];
    final payoutsEnabled = account['payouts_enabled'];
    
    debugPrint('🔔 Account updated: $accountId - payouts enabled: $payoutsEnabled');
    
    // Update partner account status in database
    // await _updatePartnerAccountStatus(accountId, account);
  }
}

/// Result of Stripe payout operation
class StripePayoutResult {
  final bool success;
  final String? transferId;
  final String? error;
  final double amount;
  final double fees;
  final Map<String, dynamic>? stripeResponse;

  StripePayoutResult({
    required this.success,
    this.transferId,
    this.error,
    required this.amount,
    required this.fees,
    this.stripeResponse,
  });
}
