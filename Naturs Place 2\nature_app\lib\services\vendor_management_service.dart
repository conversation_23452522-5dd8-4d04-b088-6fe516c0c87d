import 'package:flutter/foundation.dart';
import '../models/vendor_models.dart';

/// Service for managing vendors and affiliate partners
class VendorManagementService {
  static final VendorManagementService _instance = VendorManagementService._internal();
  factory VendorManagementService() => _instance;
  VendorManagementService._internal();

  final List<Vendor> _vendors = [];
  final List<AffiliatePartner> _affiliates = [];

  /// Initialize with sample data
  Future<void> initialize() async {
    await _loadSampleVendors();
    await _loadSampleAffiliates();
    debugPrint('✅ Vendor Management Service initialized');
  }

  /// Get all vendors
  List<Vendor> get vendors => List.unmodifiable(_vendors);

  /// Get all affiliates
  List<AffiliatePartner> get affiliates => List.unmodifiable(_affiliates);

  /// Get vendors by type
  List<Vendor> getVendorsByType(VendorType type) {
    return _vendors.where((v) => v.type == type).toList();
  }

  /// Get verified vendors
  List<Vendor> get verifiedVendors {
    return _vendors.where((v) => v.status == VerificationStatus.verified).toList();
  }

  /// Get featured vendors
  List<Vendor> get featuredVendors {
    return _vendors.where((v) => v.isFeatured && v.status == VerificationStatus.verified).toList();
  }

  /// Get active affiliates
  List<AffiliatePartner> get activeAffiliates {
    return _affiliates.where((a) => a.isActive).toList();
  }

  /// Add new vendor
  Future<String> addVendor(Vendor vendor) async {
    _vendors.add(vendor);
    debugPrint('✅ Added vendor: ${vendor.businessName}');
    return vendor.id;
  }

  /// Add new affiliate
  Future<String> addAffiliate(AffiliatePartner affiliate) async {
    _affiliates.add(affiliate);
    debugPrint('✅ Added affiliate: ${affiliate.name}');
    return affiliate.id;
  }

  /// Update vendor
  Future<void> updateVendor(String vendorId, Vendor updatedVendor) async {
    final index = _vendors.indexWhere((v) => v.id == vendorId);
    if (index != -1) {
      _vendors[index] = updatedVendor;
      debugPrint('✅ Updated vendor: ${updatedVendor.businessName}');
    }
  }

  /// Update affiliate
  Future<void> updateAffiliate(String affiliateId, AffiliatePartner updatedAffiliate) async {
    final index = _affiliates.indexWhere((a) => a.id == affiliateId);
    if (index != -1) {
      _affiliates[index] = updatedAffiliate;
      debugPrint('✅ Updated affiliate: ${updatedAffiliate.name}');
    }
  }

  /// Verify vendor
  Future<void> verifyVendor(String vendorId) async {
    final vendor = _vendors.firstWhere((v) => v.id == vendorId);
    final updatedVendor = vendor.copyWith(
      status: VerificationStatus.verified,
      verificationDate: DateTime.now(),
    );
    await updateVendor(vendorId, updatedVendor);
  }

  /// Suspend vendor
  Future<void> suspendVendor(String vendorId, String reason) async {
    final vendor = _vendors.firstWhere((v) => v.id == vendorId);
    final updatedVendor = vendor.copyWith(
      status: VerificationStatus.suspended,
      metadata: {...vendor.metadata, 'suspension_reason': reason},
    );
    await updateVendor(vendorId, updatedVendor);
  }

  /// Upgrade vendor tier
  Future<void> upgradeVendorTier(String vendorId) async {
    final vendor = _vendors.firstWhere((v) => v.id == vendorId);
    if (vendor.qualifiesForTierUpgrade()) {
      final nextTier = vendor.getNextTier();
      if (nextTier != null) {
        final updatedVendor = vendor.copyWith(tier: nextTier);
        await updateVendor(vendorId, updatedVendor);
        debugPrint('✅ Upgraded vendor ${vendor.businessName} to ${nextTier.name}');
      }
    }
  }

  /// Upgrade affiliate tier
  Future<void> upgradeAffiliateTier(String affiliateId) async {
    final affiliate = _affiliates.firstWhere((a) => a.id == affiliateId);
    if (affiliate.qualifiesForTierUpgrade()) {
      final nextTier = _getNextAffiliateTier(affiliate.tier);
      if (nextTier != null) {
        final updatedAffiliate = affiliate.copyWith(tier: nextTier);
        await updateAffiliate(affiliateId, updatedAffiliate);
        debugPrint('✅ Upgraded affiliate ${affiliate.name} to ${nextTier.name}');
      }
    }
  }

  /// Get vendor by ID
  Vendor? getVendorById(String vendorId) {
    try {
      return _vendors.firstWhere((v) => v.id == vendorId);
    } catch (e) {
      return null;
    }
  }

  /// Get affiliate by ID
  AffiliatePartner? getAffiliateById(String affiliateId) {
    try {
      return _affiliates.firstWhere((a) => a.id == affiliateId);
    } catch (e) {
      return null;
    }
  }

  /// Get affiliate by tracking code
  AffiliatePartner? getAffiliateByTrackingCode(String trackingCode) {
    try {
      return _affiliates.firstWhere((a) => a.trackingCode == trackingCode);
    } catch (e) {
      return null;
    }
  }

  /// Calculate vendor commission
  double calculateVendorCommission(String vendorId, ProductCategory category, double saleAmount) {
    final vendor = getVendorById(vendorId);
    if (vendor == null) return 0.0;
    return vendor.calculateCommission(category, saleAmount);
  }

  /// Calculate affiliate commission
  double calculateAffiliateCommission(String affiliateId, ProductCategory category, double saleAmount) {
    final affiliate = getAffiliateById(affiliateId);
    if (affiliate == null) return 0.0;
    return affiliate.calculateCommission(category, saleAmount);
  }

  /// Get vendor analytics
  Map<String, dynamic> getVendorAnalytics(String vendorId) {
    final vendor = getVendorById(vendorId);
    if (vendor == null) return {};

    return {
      'totalSales': vendor.totalSales,
      'totalOrders': vendor.totalOrders,
      'averageOrderValue': vendor.totalOrders > 0 ? vendor.totalSales / vendor.totalOrders : 0.0,
      'totalCommissionPaid': vendor.totalCommissionPaid,
      'averageRating': vendor.averageRating,
      'tier': vendor.tier.name,
      'qualifiesForUpgrade': vendor.qualifiesForTierUpgrade(),
      'nextTier': vendor.getNextTier()?.name,
      'monthlyListingFee': vendor.monthlyListingFee,
      'isFeatured': vendor.isFeatured,
    };
  }

  /// Get affiliate analytics
  Map<String, dynamic> getAffiliateAnalytics(String affiliateId) {
    final affiliate = getAffiliateById(affiliateId);
    if (affiliate == null) return {};

    return {
      'totalEarnings': affiliate.totalEarnings,
      'totalReferrals': affiliate.totalReferrals,
      'totalConversions': affiliate.totalConversions,
      'conversionRate': affiliate.conversionRate,
      'averageCommissionPerSale': affiliate.totalConversions > 0 ? affiliate.totalEarnings / affiliate.totalConversions : 0.0,
      'tier': affiliate.tier.name,
      'qualifiesForUpgrade': affiliate.qualifiesForTierUpgrade(),
      'pendingPayout': affiliate.pendingPayout,
      'lastPayoutDate': affiliate.lastPayoutDate,
    };
  }

  /// Get top performing vendors
  List<Vendor> getTopVendors({int limit = 10}) {
    final sortedVendors = List<Vendor>.from(_vendors);
    sortedVendors.sort((a, b) => b.totalSales.compareTo(a.totalSales));
    return sortedVendors.take(limit).toList();
  }

  /// Get top performing affiliates
  List<AffiliatePartner> getTopAffiliates({int limit = 10}) {
    final sortedAffiliates = List<AffiliatePartner>.from(_affiliates);
    sortedAffiliates.sort((a, b) => b.totalEarnings.compareTo(a.totalEarnings));
    return sortedAffiliates.take(limit).toList();
  }

  /// Helper method to get next affiliate tier
  CommissionTier? _getNextAffiliateTier(CommissionTier currentTier) {
    switch (currentTier) {
      case CommissionTier.tier1:
        return CommissionTier.tier2;
      case CommissionTier.tier2:
        return CommissionTier.tier3;
      case CommissionTier.tier3:
        return CommissionTier.tier4;
      case CommissionTier.tier4:
        return null;
    }
  }

  /// Load sample vendors for demonstration
  Future<void> _loadSampleVendors() async {
    final sampleVendors = [
      Vendor(
        id: 'vendor_001',
        name: 'John Smith',
        email: '<EMAIL>',
        phone: '******-0101',
        businessName: 'Herbal Haven',
        businessAddress: '123 Wellness St, Portland, OR 97201',
        taxId: 'TAX123456789',
        type: VendorType.regular,
        status: VerificationStatus.verified,
        tier: CommissionTier.tier2,
        joinDate: DateTime.now().subtract(const Duration(days: 180)),
        verificationDate: DateTime.now().subtract(const Duration(days: 175)),
        categories: [ProductCategory.supplements, ProductCategory.herbalTeas],
        commissionRates: {
          ProductCategory.supplements: 25.0,
          ProductCategory.herbalTeas: 20.0,
        },
        monthlyListingFee: 150.0,
        isFeatured: true,
        totalSales: 25000.0,
        totalCommissionPaid: 5000.0,
        totalOrders: 150,
        averageRating: 4.8,
        certifications: ['Organic Certified', 'FDA Registered'],
        description: 'Premium organic herbs and supplements for natural wellness.',
      ),
      // Add more sample vendors...
    ];

    _vendors.addAll(sampleVendors);
  }

  /// Load sample affiliates for demonstration
  Future<void> _loadSampleAffiliates() async {
    final sampleAffiliates = [
      AffiliatePartner(
        id: 'affiliate_001',
        name: 'Sarah Johnson',
        email: '<EMAIL>',
        phone: '******-0201',
        website: 'https://wellnessblog.com',
        socialMedia: '@wellnessblog',
        tier: CommissionTier.tier2,
        joinDate: DateTime.now().subtract(const Duration(days: 120)),
        trackingCode: 'WELLNESS2024',
        commissionRates: {
          ProductCategory.supplements: 12.0,
          ProductCategory.essentialOils: 15.0,
          ProductCategory.skincare: 14.0,
        },
        totalEarnings: 3500.0,
        totalReferrals: 250,
        totalConversions: 45,
        conversionRate: 0.18,
        pendingPayout: 450.0,
      ),
      // Add more sample affiliates...
    ];

    _affiliates.addAll(sampleAffiliates);
  }
}
