@echo off
echo 🌿 Nature's Place - Android Studio Setup
echo ========================================
echo.

cd /d "D:\Healing App\Naturs Place 2\nature_app"

echo 📱 Checking Flutter installation...
flutter --version
if %errorlevel% neq 0 (
    echo ❌ Flutter not found! Please install Flutter first.
    echo 📖 Visit: https://flutter.dev/docs/get-started/install
    pause
    exit /b 1
)

echo.
echo 🔍 Running Flutter Doctor...
flutter doctor

echo.
echo 🧹 Cleaning previous builds...
flutter clean

echo.
echo 📦 Getting dependencies...
flutter pub get

echo.
echo 🔧 Checking for Android Studio compatibility...
flutter config --android-studio-dir="C:\Program Files\Android\Android Studio"

echo.
echo 📊 Analyzing code...
flutter analyze --no-fatal-infos

echo.
echo ✅ Setup Complete!
echo.
echo 🚀 Next Steps:
echo   1. Open Android Studio
echo   2. Select "Open an existing project"
echo   3. Navigate to: D:\Healing App\Naturs Place 2\nature_app
echo   4. Click "Open"
echo   5. Wait for Gradle sync to complete
echo   6. Select your target device
echo   7. Click the Run button (green play icon)
echo.
echo 💡 Troubleshooting:
echo   - If Kotlin errors occur, run: flutter clean && cd android && ./gradlew clean
echo   - For web testing: flutter run -d chrome
echo   - Check Flutter Doctor: flutter doctor
echo.
echo 🌐 Web Version:
echo   Run: flutter run -d chrome --web-port 8080
echo   URL: http://localhost:8080
echo.
echo 📱 Features Ready:
echo   ✅ Vendor/Partner/Affiliate Dashboards
echo   ✅ Crypto Payment Infrastructure
echo   ✅ Mobile-First Web Design
echo   ✅ Enhanced Security
echo   ✅ Cross-Platform Compatibility
echo.

pause
