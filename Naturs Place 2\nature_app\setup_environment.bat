@echo off
echo 🔧 Setting up PostgreSQL Environment Variables for Nature's Place
echo.

echo 📋 This script will help you set up environment variables for PostgreSQL connection.
echo.

set /p POSTGRES_HOST="Enter PostgreSQL host (default: localhost): "
if "%POSTGRES_HOST%"=="" set POSTGRES_HOST=localhost

set /p POSTGRES_PORT="Enter PostgreSQL port (default: 5432): "
if "%POSTGRES_PORT%"=="" set POSTGRES_PORT=5432

set /p POSTGRES_DB="Enter database name (default: Natures Place): "
if "%POSTGRES_DB%"=="" set "POSTGRES_DB=Natures Place"

set /p POSTGRES_USER="Enter PostgreSQL username (default: postgres): "
if "%POSTGRES_USER%"=="" set POSTGRES_USER=postgres

set /p POSTGRES_PASSWORD="Enter PostgreSQL password: "

echo.
echo 🔧 Setting environment variables...

setx POSTGRES_HOST "%POSTGRES_HOST%"
setx POSTGRES_PORT "%POSTGRES_PORT%"
setx POSTGRES_DB "%POSTGRES_DB%"
setx POSTGRES_USER "%POSTGRES_USER%"
setx POSTGRES_PASSWORD "%POSTGRES_PASSWORD%"

echo.
echo ✅ Environment variables set successfully!
echo.
echo 📋 Configuration:
echo    Host: %POSTGRES_HOST%
echo    Port: %POSTGRES_PORT%
echo    Database: %POSTGRES_DB%
echo    Username: %POSTGRES_USER%
echo    Password: [HIDDEN]
echo.
echo 🔄 Please restart your terminal/IDE for changes to take effect.
echo.
echo 📝 Next steps:
echo    1. Restart your development environment
echo    2. Ensure PostgreSQL server is running
echo    3. Create the database if it doesn't exist:
echo       CREATE DATABASE "Natures Place";
echo    4. Run: dart test_postgresql_connection.dart
echo.
pause
