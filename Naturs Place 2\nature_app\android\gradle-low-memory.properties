# Low-memory Gradle configuration for Nature's Place App
# Use this file if you're experiencing out of memory errors

# Minimal JVM settings for low-memory systems
org.gradle.jvmargs=-Xmx1G -XX:MaxMetaspaceSize=512m -XX:ReservedCodeCacheSize=128m -Dfile.encoding=UTF-8

# Android settings
android.useAndroidX=true
android.enableJetifier=true

# Disable performance features that use more memory
org.gradle.parallel=false
org.gradle.caching=false
org.gradle.configureondemand=false

# Daemon settings
org.gradle.daemon=false

# Additional memory optimization
org.gradle.workers.max=1
