# 💰 Nature's Place Commission Model Guide

## 🎯 **Correct Commission Structure**

Your commission system has been updated to reflect the correct business model:

### **1. Vendors (Product Suppliers) Pay YOU Commission**

**How it works:**
- Vendors list their products on your app
- When customers buy vendor products, **vendors owe you commission**
- You collect commission from vendors, not pay them

**Example:**
```
Customer buys $100 organic shampoo from Vendor A
→ Vendor A owes you $10 commission (10%)
→ Vendor A receives $90 payout
→ You earn $10 revenue
```

**Database Records:**
```sql
-- Commission record shows vendor owes us money
INSERT INTO commissions (
  partner_type = 'vendor',
  commission_amount = 10.00,    -- Vendor owes us $10
  app_revenue = 10.00,          -- We earn $10
  notes = 'Vendor owes us $10 commission on $100 sale'
);
```

### **2. Partners (Affiliates/Influencers) Earn FROM YOU**

**How it works:**
- Partners share referral links to your app
- When their referrals make purchases, **you pay partners commission**
- Partners earn money from you for bringing customers

**Example:**
```
Partner shares link → Customer buys $100 vitamins
→ You pay partner $5 commission (5%)
→ Partner earns $5
→ You pay out $5 (expense for you)
```

**Database Records:**
```sql
-- Commission record shows we owe partner money
INSERT INTO commissions (
  partner_type = 'affiliate',
  commission_amount = 5.00,     -- We pay partner $5
  app_revenue = -5.00,          -- Negative (expense for us)
  notes = 'We pay partner $5 for $100 referral sale'
);
```

## 📊 **Updated Commission Calculations**

### **Vendor Commission Logic:**
```dart
// Vendor owes us commission
final commissionRate = 10.0; // 10%
final saleAmount = 100.0;
final commissionOwedToUs = saleAmount * (commissionRate / 100); // $10
final vendorReceives = saleAmount - commissionOwedToUs; // $90

// We earn money from vendor
appRevenue = +commissionOwedToUs; // +$10 (positive revenue)
```

### **Partner Commission Logic:**
```dart
// We pay partner commission
final commissionRate = 5.0; // 5%
final saleAmount = 100.0;
final commissionWePayPartner = saleAmount * (commissionRate / 100); // $5

// We pay money to partner
appRevenue = -commissionWePayPartner; // -$5 (negative, expense)
```

## 🔄 **Payment Flow Implementation**

### **1. Vendor Payments (They Pay You)**
```dart
// Track what vendors owe you
final vendorCommissions = commissions.where((c) => 
  c.partnerType == 'vendor' && 
  c.status == CommissionStatus.pending
);

// Collect from vendors (they pay you)
for (final commission in vendorCommissions) {
  // Deduct commission from vendor payout
  final vendorPayout = commission.saleAmount - commission.commissionAmount;
  // Process payment to vendor (reduced amount)
  // Mark commission as collected
}
```

### **2. Partner Payments (You Pay Them)**
```dart
// Track what you owe partners
final partnerCommissions = commissions.where((c) => 
  c.partnerType == 'affiliate' && 
  c.status == CommissionStatus.pending
);

// Pay partners bi-weekly via Stripe
for (final commission in partnerCommissions) {
  // You pay partner the commission amount
  await stripeService.payoutToPartner(
    partnerId: commission.partnerId,
    amount: commission.commissionAmount,
  );
  // Mark commission as paid
}
```

## 📈 **Analytics & Reporting**

### **Revenue Calculation:**
```dart
// Total app revenue
final totalRevenue = commissions.fold(0.0, (sum, c) => sum + c.appRevenue);

// Vendor commissions (positive revenue)
final vendorRevenue = commissions
  .where((c) => c.partnerType == 'vendor')
  .fold(0.0, (sum, c) => sum + c.appRevenue);

// Partner payouts (negative revenue/expenses)
final partnerExpenses = commissions
  .where((c) => c.partnerType == 'affiliate')
  .fold(0.0, (sum, c) => sum + c.appRevenue);

// Net profit
final netProfit = vendorRevenue + partnerExpenses; // partnerExpenses is negative
```

### **Outstanding Balances:**
```dart
// Money vendors owe you
final vendorsOweUs = commissions
  .where((c) => c.partnerType == 'vendor' && c.status == CommissionStatus.pending)
  .fold(0.0, (sum, c) => sum + c.commissionAmount);

// Money you owe partners
final weOwePartners = commissions
  .where((c) => c.partnerType == 'affiliate' && c.status == CommissionStatus.pending)
  .fold(0.0, (sum, c) => sum + c.commissionAmount);
```

## 🗄️ **Database Schema Updates**

### **Commission Table Structure:**
```sql
CREATE TABLE commissions (
  id VARCHAR(255) PRIMARY KEY,
  transaction_id VARCHAR(255) NOT NULL,
  partner_id VARCHAR(255) NOT NULL,
  partner_type VARCHAR(50) NOT NULL, -- 'vendor' or 'affiliate'
  commission_amount DECIMAL(10,2) NOT NULL, -- Amount involved
  app_revenue DECIMAL(10,2) NOT NULL, -- +positive for vendor, -negative for affiliate
  status VARCHAR(50) NOT NULL, -- pending, paid, collected
  notes TEXT, -- Human readable explanation
  metadata JSONB -- Additional details
);
```

### **Example Records:**
```sql
-- Vendor owes us $10 on $100 sale
INSERT INTO commissions VALUES (
  'comm_vendor_001',
  'txn_001',
  'vendor_healing_herbs',
  'vendor',
  10.00,  -- commission_amount (vendor owes us)
  10.00,  -- app_revenue (positive, we earn)
  'pending',
  'Vendor owes us $10 commission on $100 sale'
);

-- We owe partner $5 on $100 referral
INSERT INTO commissions VALUES (
  'comm_affiliate_001',
  'txn_002',
  'partner_wellness_guru',
  'affiliate',
  5.00,   -- commission_amount (we pay partner)
  -5.00,  -- app_revenue (negative, we pay out)
  'pending',
  'We pay partner $5 for $100 referral sale'
);
```

## 🧪 **Testing the Correct Model**

Run the updated tests to verify the correct commission calculations:

```bash
# Test the corrected commission system
dart test_commission_system.dart
```

**Expected Output:**
```
✅ Vendor commission created successfully!
   Commission Rate: 10% (vendor owes us)
   Amount Vendor Owes Us: $8.46
   Our Revenue from Vendor: $8.46
   Notes: Vendor owes us $8.46 commission on $84.57 sale. Vendor receives $76.11
```

## 🚀 **Production Implementation**

### **1. Vendor Commission Collection:**
- Automatically deduct commission from vendor payouts
- Track outstanding vendor balances
- Generate vendor commission statements

### **2. Partner Commission Payouts:**
- Process bi-weekly payouts via Stripe
- Track partner earnings and payment history
- Generate partner earning statements

### **3. Financial Reporting:**
- Real-time revenue tracking
- Outstanding balances dashboard
- Profit/loss statements
- Commission analytics

Your commission system now correctly reflects your business model where vendors pay you commission and you pay partners commission! 🎉
