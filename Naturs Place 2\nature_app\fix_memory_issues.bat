@echo off
echo 🧠 Fixing Memory Issues for Nature's Place App 🧠
echo.

echo Step 1: Stopping all Gradle daemons...
cd android
call gradlew --stop
cd ..
echo ✅ Gradle daemons stopped
echo.

echo Step 2: Clearing Gradle cache...
if exist "%USERPROFILE%\.gradle\caches" (
    echo Removing Gradle cache directory...
    rmdir /s /q "%USERPROFILE%\.gradle\caches"
    echo ✅ Gradle cache cleared
) else (
    echo ℹ️ Gradle cache directory not found
)
echo.

echo Step 3: Clearing Gradle daemon logs...
if exist "%USERPROFILE%\.gradle\daemon" (
    echo Removing Gradle daemon directory...
    rmdir /s /q "%USERPROFILE%\.gradle\daemon"
    echo ✅ Gradle daemon logs cleared
) else (
    echo ℹ️ Gradle daemon directory not found
)
echo.

echo Step 4: Clearing Flutter build cache...
flutter clean
if exist "build" rmdir /s /q "build"
if exist "android\app\build" rmdir /s /q "android\app\build"
if exist "android\.gradle" rmdir /s /q "android\.gradle"
echo ✅ Flutter build cache cleared
echo.

echo Step 5: Setting memory-optimized environment variables...
set GRADLE_OPTS=-Xmx1G -XX:MaxMetaspaceSize=512m
set JAVA_OPTS=-Xmx1G
echo ✅ Memory limits set
echo.

echo Step 6: Getting dependencies with memory optimization...
flutter pub get
echo ✅ Dependencies updated
echo.

echo Step 7: Building with memory-safe settings...
echo Building APK with reduced memory usage...
cd android
call gradlew assembleDebug --no-daemon --max-workers=1
cd ..

if %errorlevel% equ 0 (
    echo ✅ Build successful with memory optimization!
) else (
    echo ❌ Build failed. Trying alternative approach...
    echo.
    echo Trying with even lower memory settings...
    set GRADLE_OPTS=-Xmx512m -XX:MaxMetaspaceSize=256m
    cd android
    call gradlew clean
    call gradlew assembleDebug --no-daemon --max-workers=1 --no-parallel
    cd ..
)
echo.

echo 🎉 Memory optimization completed!
echo.
echo If you still experience issues:
echo 1. Restart your computer to free up memory
echo 2. Close other applications before building
echo 3. Try building with Android Studio instead
echo 4. Consider upgrading your system RAM
echo.
pause
