import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:provider/provider.dart';
import '../providers/app_state.dart';

class PlantDetailScreen extends StatefulWidget {
  final Plant plant;

  const PlantDetailScreen({
    super.key,
    required this.plant,
  });

  @override
  State<PlantDetailScreen> createState() => _PlantDetailScreenState();
}

class _PlantDetailScreenState extends State<PlantDetailScreen>
    with SingleTickerProviderStateMixin {
  TabController? _tabController;
  bool _isFavorite = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _isFavorite = Provider.of<AppState>(context, listen: false)
        .isFavorite(widget.plant.id);
  }

  @override
  void dispose() {
    _tabController?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: CustomScrollView(
        slivers: [
          _buildSliverAppBar(),
          SliverToBoxAdapter(
            child: Column(
              children: [
                _buildPlantHeader(),
                _buildTabSection(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSliverAppBar() {
    return SliverAppBar(
      expandedHeight: 300,
      pinned: true,
      flexibleSpace: FlexibleSpaceBar(
        background: Directionality(
          textDirection: TextDirection.ltr,
          child: Stack(
            fit: StackFit.expand,
            children: [
            CachedNetworkImage(
              imageUrl: widget.plant.imageUrl,
              fit: BoxFit.cover,
              placeholder: (context, url) => Container(
                color: Colors.grey[200],
                child: const Center(child: CircularProgressIndicator()),
              ),
              errorWidget: (context, url, error) => Container(
                color: Colors.grey[200],
                child: const Center(child: Icon(Icons.error)),
              ),
            ),
            Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Colors.transparent,
                    Colors.black.withValues(alpha: 0.7),
                  ],
                ),
              ),
            ),
          ],
          ),
        ),
      ),
      actions: [
        Consumer<AppState>(
          builder: (context, appState, child) {
            return IconButton(
              icon: Icon(
                _isFavorite ? Icons.favorite : Icons.favorite_border,
                color: _isFavorite ? Colors.red : Colors.white,
              ),
              onPressed: () {
                setState(() {
                  _isFavorite = !_isFavorite;
                });
                if (_isFavorite) {
                  appState.addFavorite(widget.plant.id);
                } else {
                  appState.removeFavorite(widget.plant.id);
                }
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(
                      _isFavorite
                          ? 'Added to favorites'
                          : 'Removed from favorites',
                    ),
                    duration: const Duration(seconds: 2),
                  ),
                );
              },
            );
          },
        ),
        IconButton(
          icon: const Icon(Icons.share, color: Colors.white),
          onPressed: () {
            // TODO: Implement share functionality
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Share functionality coming soon!'),
                duration: Duration(seconds: 2),
              ),
            );
          },
        ),
      ],
    );
  }

  Widget _buildPlantHeader() {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      widget.plant.name,
                      style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: const Color(0xFF22c55e),
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      widget.plant.scientificName,
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontStyle: FontStyle.italic,
                        color: Colors.grey[600],
                      ),
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 12,
                            vertical: 6,
                          ),
                          decoration: BoxDecoration(
                            color: const Color(0xFF22c55e).withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(20),
                          ),
                          child: Text(
                            widget.plant.category,
                            style: const TextStyle(
                              color: Color(0xFF22c55e),
                              fontWeight: FontWeight.w500,
                              fontSize: 12,
                            ),
                          ),
                        ),
                        const SizedBox(width: 8),
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 12,
                            vertical: 6,
                          ),
                          decoration: BoxDecoration(
                            color: Colors.blue.withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(20),
                          ),
                          child: Text(
                            widget.plant.origin,
                            style: const TextStyle(
                              color: Colors.blue,
                              fontWeight: FontWeight.w500,
                              fontSize: 12,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              Column(
                children: [
                  Row(
                    children: List.generate(5, (index) {
                      final rating = widget.plant.rating;
                      if (index < rating.floor()) {
                        return const Icon(
                          Icons.star,
                          color: Colors.amber,
                          size: 20,
                        );
                      } else if (index < rating) {
                        return const Icon(
                          Icons.star_half,
                          color: Colors.amber,
                          size: 20,
                        );
                      } else {
                        return const Icon(
                          Icons.star_border,
                          color: Colors.amber,
                          size: 20,
                        );
                      }
                    }),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    '${widget.plant.rating.toStringAsFixed(1)}/5',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ],
          ),
          const SizedBox(height: 16),
          Text(
            widget.plant.description,
            style: Theme.of(context).textTheme.bodyLarge,
          ),
          const SizedBox(height: 16),
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: widget.plant.benefits.map((benefit) {
              return Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 6,
                ),
                decoration: BoxDecoration(
                  color: Colors.orange.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(
                    color: Colors.orange.withValues(alpha: 0.3),
                  ),
                ),
                child: Text(
                  benefit,
                  style: const TextStyle(
                    color: Colors.orange,
                    fontWeight: FontWeight.w500,
                    fontSize: 12,
                  ),
                ),
              );
            }).toList(),
          ),
        ],
      ),
    );
  }

  Widget _buildTabSection() {
    return Column(
      children: [
        Container(
          decoration: BoxDecoration(
            color: Colors.grey[100],
            border: Border(
              bottom: BorderSide(color: Colors.grey[300]!),
            ),
          ),
          child: TabBar(
            controller: _tabController!,
            isScrollable: true,
            labelColor: const Color(0xFF22c55e),
            unselectedLabelColor: Colors.grey[600],
            indicatorColor: const Color(0xFF22c55e),
            tabs: const [
              Tab(icon: Icon(Icons.info_outline), text: 'Overview'),
              Tab(icon: Icon(Icons.healing), text: 'Uses'),
              Tab(icon: Icon(Icons.eco), text: 'Growing'),
              Tab(icon: Icon(Icons.warning_amber), text: 'Safety'),
            ],
          ),
        ),
        SizedBox(
          height: 400,
          child: TabBarView(
            controller: _tabController!,
            children: [
              _buildOverviewTab(),
              _buildUsesTab(),
              _buildGrowingTab(),
              _buildSafetyTab(),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildOverviewTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildInfoCard(
            'Plant Information',
            Icons.info,
            [
              _buildInfoRow('Scientific Name', widget.plant.scientificName),
              _buildInfoRow('Common Names', widget.plant.name),
              _buildInfoRow('Family', widget.plant.category),
              _buildInfoRow('Origin', widget.plant.origin),
              _buildInfoRow('Habitat', 'Various temperate regions'),
            ],
          ),
          const SizedBox(height: 16),
          _buildInfoCard(
            'Physical Characteristics',
            Icons.visibility,
            [
              _buildInfoRow('Type', 'Perennial herb'),
              _buildInfoRow('Height', '30-60 cm'),
              _buildInfoRow('Leaves', 'Compound, serrated edges'),
              _buildInfoRow('Flowers', 'Small, white clusters'),
              _buildInfoRow('Harvest Time', 'Late summer to early fall'),
            ],
          ),
          const SizedBox(height: 16),
          _buildInfoCard(
            'Active Compounds',
            Icons.science,
            [
              _buildInfoRow('Primary', 'Flavonoids, tannins'),
              _buildInfoRow('Secondary', 'Essential oils, saponins'),
              _buildInfoRow('Concentration', 'Highest in leaves and flowers'),
              _buildInfoRow('Bioavailability', 'Enhanced when dried'),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildUsesTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildInfoCard(
            'Traditional Uses',
            Icons.history_edu,
            [
              _buildInfoRow('Ancient Medicine', 'Used for over 2000 years'),
              _buildInfoRow('Folk Remedies', 'Digestive and respiratory support'),
              _buildInfoRow('Cultural Use', 'Ceremonial and medicinal'),
              _buildInfoRow('Regional Variations', 'Different preparations worldwide'),
            ],
          ),
          const SizedBox(height: 16),
          _buildInfoCard(
            'Modern Applications',
            Icons.medical_services,
            [
              _buildInfoRow('Research Status', 'Clinically studied'),
              _buildInfoRow('Primary Benefits', widget.plant.benefits.join(', ')),
              _buildInfoRow('Dosage Forms', 'Tea, extract, vitamins & minerals'),
              _buildInfoRow('Effectiveness', 'Moderate to high'),
            ],
          ),
          const SizedBox(height: 16),
          _buildInfoCard(
            'Preparation Methods',
            Icons.local_cafe,
            [
              _buildInfoRow('Tea', '1-2 tsp dried herb per cup'),
              _buildInfoRow('Tincture', '1-3 ml, 3 times daily'),
              _buildInfoRow('Vitamins & Minerals', 'Follow manufacturer instructions'),
              _buildInfoRow('Topical', 'Infused oils or creams'),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildGrowingTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildInfoCard(
            'Growing Conditions',
            Icons.wb_sunny,
            [
              _buildInfoRow('Sunlight', 'Full sun to partial shade'),
              _buildInfoRow('Soil Type', 'Well-draining, fertile'),
              _buildInfoRow('pH Level', '6.0 - 7.5 (slightly acidic to neutral)'),
              _buildInfoRow('Water Needs', 'Moderate, avoid waterlogging'),
              _buildInfoRow('Temperature', '15-25°C (59-77°F)'),
            ],
          ),
          const SizedBox(height: 16),
          _buildInfoCard(
            'Planting & Care',
            Icons.eco,
            [
              _buildInfoRow('Planting Time', 'Spring after last frost'),
              _buildInfoRow('Spacing', '30-45 cm apart'),
              _buildInfoRow('Fertilizer', 'Organic compost, low nitrogen'),
              _buildInfoRow('Pruning', 'Regular deadheading'),
              _buildInfoRow('Companion Plants', 'Lavender, rosemary, thyme'),
            ],
          ),
          const SizedBox(height: 16),
          _buildInfoCard(
            'Harvesting & Storage',
            Icons.agriculture,
            [
              _buildInfoRow('Best Time', 'Morning after dew dries'),
              _buildInfoRow('Method', 'Cut stems 1/3 from top'),
              _buildInfoRow('Drying', 'Hang in dark, ventilated area'),
              _buildInfoRow('Storage', 'Airtight containers, cool place'),
              _buildInfoRow('Shelf Life', '1-2 years when properly stored'),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSafetyTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildInfoCard(
            'Safety Information',
            Icons.warning_amber,
            [
              _buildInfoRow('General Safety', 'Generally safe for most adults'),
              _buildInfoRow('Pregnancy', 'Consult healthcare provider'),
              _buildInfoRow('Breastfeeding', 'Limited data available'),
              _buildInfoRow('Children', 'Not recommended under 12'),
              _buildInfoRow('Allergies', 'May cause reactions in sensitive individuals'),
            ],
          ),
          const SizedBox(height: 16),
          _buildInfoCard(
            'Contraindications',
            Icons.block,
            [
              _buildInfoRow('Blood Thinners', 'May interact with anticoagulants'),
              _buildInfoRow('Surgery', 'Discontinue 2 weeks before surgery'),
              _buildInfoRow('Autoimmune', 'Avoid with autoimmune conditions'),
              _buildInfoRow('Liver Disease', 'Use with caution'),
            ],
          ),
          const SizedBox(height: 16),
          _buildInfoCard(
            'Side Effects',
            Icons.health_and_safety,
            [
              _buildInfoRow('Common', 'Mild stomach upset, drowsiness'),
              _buildInfoRow('Rare', 'Allergic reactions, skin rash'),
              _buildInfoRow('Overdose', 'Nausea, vomiting, diarrhea'),
              _buildInfoRow('Duration', 'Usually resolve within 24 hours'),
            ],
          ),
          const SizedBox(height: 16),
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.orange.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: Colors.orange.withValues(alpha: 0.3),
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.info,
                      color: Colors.orange[700],
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'Important Notice',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: Colors.orange[700],
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Text(
                  'This information is for educational purposes only and should not replace professional medical advice. Always consult with a healthcare provider before using any herbal remedy, especially if you have existing health conditions or are taking medications.',
                  style: TextStyle(
                    color: Colors.orange[700],
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoCard(String title, IconData icon, List<Widget> children) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: const Color(0xFF22c55e).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    icon,
                    color: const Color(0xFF22c55e),
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                Text(
                  title,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ...children,
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w500,
                color: Colors.grey[700],
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: Theme.of(context).textTheme.bodyMedium,
            ),
          ),
        ],
      ),
    );
  }
}
