@echo off
echo 🌐 Starting Nature's Place Web App...
echo.

echo 📱 Checking Flutter installation...
flutter --version
if %errorlevel% neq 0 (
    echo ❌ Flutter not found! Please install Flutter first.
    pause
    exit /b 1
)

echo.
echo 🧹 Cleaning previous builds...
flutter clean

echo.
echo 📦 Getting dependencies...
flutter pub get

echo.
echo 🌐 Building and running web app...
echo.
echo 🚀 Your Nature's Place app will open in your default browser!
echo 📍 URL: http://localhost:8080
echo.
echo ⏹️  Press Ctrl+C to stop the server
echo.

flutter run -d web-server --web-port 8080 --web-hostname localhost

pause
