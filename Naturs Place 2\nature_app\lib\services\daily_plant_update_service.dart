import 'dart:async';
import 'dart:math';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'plant_data_service.dart';
import 'research_backed_plant_service.dart';

/// Daily Plant Update Service for automated daily plant additions
class DailyPlantUpdateService {
  static final DailyPlantUpdateService _instance = DailyPlantUpdateService._internal();
  factory DailyPlantUpdateService() => _instance;
  DailyPlantUpdateService._internal();

  Timer? _dailyTimer;
  bool _isInitialized = false;
  final PlantDataService _plantDataService = PlantDataService();
  final ResearchBackedPlantService _researchBackedPlantService = ResearchBackedPlantService();

  /// Initialize the daily update service
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // Initialize plant data service
      await _plantDataService.initialize();

      // Initialize research-backed plant service
      await _researchBackedPlantService.initialize();

      // Start daily timer
      _startDailyTimer();

      // Check for updates immediately
      await _checkForDailyUpdate();

      _isInitialized = true;
      debugPrint('DailyPlantUpdateService initialized successfully');

    } catch (e) {
      debugPrint('Error initializing DailyPlantUpdateService: $e');
    }
  }

  /// Start the daily timer for automatic updates
  void _startDailyTimer() {
    // Cancel existing timer if any
    _dailyTimer?.cancel();
    
    // Calculate time until next midnight
    final now = DateTime.now();
    final tomorrow = DateTime(now.year, now.month, now.day + 1);
    final timeUntilMidnight = tomorrow.difference(now);
    
    debugPrint('Next plant update scheduled in: ${timeUntilMidnight.inHours}h ${timeUntilMidnight.inMinutes % 60}m');
    
    // Set timer for midnight, then repeat every 24 hours
    _dailyTimer = Timer(timeUntilMidnight, () {
      _performDailyUpdate();
      
      // Set up recurring daily timer
      _dailyTimer = Timer.periodic(const Duration(days: 1), (timer) {
        _performDailyUpdate();
      });
    });
  }

  /// Check for daily update (called on app start)
  Future<void> _checkForDailyUpdate() async {
    try {
      await _plantDataService.checkAndAddDailyPlants();
      await _researchBackedPlantService.addDailyResearchBackedPlants();
    } catch (e) {
      debugPrint('Error checking for daily update: $e');
    }
  }

  /// Perform the daily update
  Future<void> _performDailyUpdate() async {
    try {
      debugPrint('Performing daily plant update...');

      await _plantDataService.checkAndAddDailyPlants();
      await _researchBackedPlantService.addDailyResearchBackedPlants();

      // Show notification about new plants
      await _showNewPlantsNotification();

      debugPrint('Daily plant update completed successfully');

    } catch (e) {
      debugPrint('Error performing daily update: $e');
    }
  }

  /// Show notification about new plants added
  Future<void> _showNewPlantsNotification() async {
    try {
      final todaysPlants = _plantDataService.getTodaysPlants();
      if (todaysPlants.isNotEmpty) {
        // Save notification data for the app to show
        final prefs = await SharedPreferences.getInstance();
        await prefs.setString('new_plants_notification', 
          '${todaysPlants.length} new healing plants added today!');
        await prefs.setInt('new_plants_count', todaysPlants.length);
        await prefs.setString('new_plants_date', DateTime.now().toIso8601String());
        
        debugPrint('Notification saved: ${todaysPlants.length} new plants added');
      }
    } catch (e) {
      debugPrint('Error showing new plants notification: $e');
    }
  }

  /// Get notification about new plants
  Future<Map<String, dynamic>?> getNewPlantsNotification() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final notification = prefs.getString('new_plants_notification');
      final count = prefs.getInt('new_plants_count');
      final dateString = prefs.getString('new_plants_date');
      
      if (notification != null && count != null && dateString != null) {
        final date = DateTime.parse(dateString);
        final today = DateTime.now();
        
        // Only show notification for today
        if (date.day == today.day && date.month == today.month && date.year == today.year) {
          return {
            'message': notification,
            'count': count,
            'date': date,
            'plants': _plantDataService.getTodaysPlants(),
          };
        }
      }
      
      return null;
    } catch (e) {
      debugPrint('Error getting new plants notification: $e');
      return null;
    }
  }

  /// Clear the new plants notification
  Future<void> clearNewPlantsNotification() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('new_plants_notification');
      await prefs.remove('new_plants_count');
      await prefs.remove('new_plants_date');
    } catch (e) {
      debugPrint('Error clearing new plants notification: $e');
    }
  }

  /// Force update plants (for testing)
  Future<void> forceUpdate() async {
    try {
      debugPrint('Forcing plant update...');
      await _plantDataService.forceRefreshDailyPlants();
      await _researchBackedPlantService.addDailyResearchBackedPlants();
      await _showNewPlantsNotification();
      debugPrint('Force update completed');
    } catch (e) {
      debugPrint('Error forcing update: $e');
    }
  }

  /// Get statistics about plant additions
  Map<String, dynamic> getStatistics() {
    final researchStats = _researchBackedPlantService.getStatistics();
    return {
      'totalPlantsAdded': _plantDataService.totalPlantsAdded,
      'lastUpdateDate': _plantDataService.lastUpdateDate,
      'todaysPlants': _plantDataService.getTodaysPlants().length,
      'totalPlants': _plantDataService.getAllPlants().length,
      'researchBackedPlants': researchStats['totalPlants'],
      'highEvidencePlants': researchStats['highEvidencePlants'],
      'fruitsCount': researchStats['fruitsCount'],
      'herbsCount': researchStats['herbsCount'],
      'averageEfficacyScore': researchStats['averageEfficacyScore'],
      'isServiceRunning': _dailyTimer?.isActive ?? false,
    };
  }

  /// Get today's featured healing plants
  List<Map<String, dynamic>> getTodaysFeaturedPlants() {
    final todaysPlants = _plantDataService.getTodaysPlants();
    return todaysPlants.map((plant) => {
      'id': plant.id,
      'name': plant.name,
      'scientificName': plant.scientificName,
      'category': plant.category,
      'description': plant.description,
      'imageUrl': plant.imageUrl,
      'benefits': plant.benefits,
      'rating': plant.rating,
      'isNew': true,
    }).toList();
  }

  /// Get healing plant of the day
  Map<String, dynamic>? getPlantOfTheDay() {
    final todaysPlants = _plantDataService.getTodaysPlants();
    if (todaysPlants.isEmpty) return null;
    
    // Select the first plant as plant of the day
    final plant = todaysPlants.first;
    return {
      'id': plant.id,
      'name': plant.name,
      'scientificName': plant.scientificName,
      'category': plant.category,
      'description': plant.description,
      'imageUrl': plant.imageUrl,
      'benefits': plant.benefits,
      'rating': plant.rating,
      'keyBenefit': plant.benefits.isNotEmpty ? plant.benefits.first : 'General Wellness',
      'funFact': _generateFunFact(plant),
      'dailyTip': _generateDailyTip(plant),
    };
  }

  /// Generate a fun fact about the plant
  String _generateFunFact(plant) {
    final facts = [
      'This plant has been used for over ${Random().nextInt(3000) + 500} years in traditional medicine.',
      'Ancient healers called this the "${plant.commonNames.isNotEmpty ? plant.commonNames.first : plant.name}" for its remarkable properties.',
      'This healing plant contains over ${Random().nextInt(50) + 10} active compounds.',
      'Traditional practitioners believed this plant could enhance both physical and spiritual well-being.',
      'This plant was so valued that it was once traded as currency in some cultures.',
    ];
    
    return facts[Random().nextInt(facts.length)];
  }

  /// Generate a daily tip for using the plant
  String _generateDailyTip(plant) {
    final tips = [
      'Try incorporating this plant into your morning routine for sustained energy throughout the day.',
      'This plant works best when taken consistently over time - patience is key with herbal medicine.',
      'Consider combining this with meditation or mindfulness practices for enhanced benefits.',
      'Start with small amounts and gradually increase as your body adapts to this powerful plant.',
      'This plant is most effective when sourced from reputable, organic suppliers.',
    ];
    
    return tips[Random().nextInt(tips.length)];
  }

  /// Dispose of the service
  void dispose() {
    _dailyTimer?.cancel();
    _dailyTimer = null;
    _isInitialized = false;
    debugPrint('DailyPlantUpdateService disposed');
  }

  /// Check if service is running
  bool get isRunning => _dailyTimer?.isActive ?? false;

  /// Get next update time
  DateTime? get nextUpdateTime {
    if (!isRunning) return null;
    
    final now = DateTime.now();
    final tomorrow = DateTime(now.year, now.month, now.day + 1);
    return tomorrow;
  }
}
