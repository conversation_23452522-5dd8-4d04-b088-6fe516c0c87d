@echo off
echo 🌿 Nature's Place - Enhanced Web Version
echo ========================================
echo.

echo 🔒 Security-Enhanced Web Build
echo 📱 PWA-Ready with Offline Support
echo ⚡ Performance Optimized
echo 🎨 Responsive Design
echo.

cd /d "D:\Healing App\Naturs Place 2\nature_app"

echo 📱 Checking Flutter installation...
flutter --version
if %errorlevel% neq 0 (
    echo ❌ Flutter not found! Please install Flutter first.
    pause
    exit /b 1
)

echo.
echo 🧹 Cleaning previous builds...
flutter clean

echo.
echo 📦 Getting dependencies (including security packages)...
flutter pub get

echo.
echo 🔧 Analyzing code for security issues...
flutter analyze

echo.
echo 🌐 Building enhanced web version...
echo.
echo ✨ Features included:
echo   🔒 Enhanced security headers
echo   🛡️  CSRF protection
echo   ⚡ Performance optimizations
echo   📱 PWA capabilities
echo   🎨 Responsive design
echo   🔄 Offline support
echo   📊 Analytics ready
echo.

echo 🚀 Starting enhanced web server...
echo 📍 URL: http://localhost:8080
echo.
echo 💡 Tips:
echo   - Install as PWA for best experience
echo   - All vendor/partner dashboards are fully functional
echo   - Security monitoring is active
echo   - Performance optimizations enabled
echo.
echo ⏹️  Press Ctrl+C to stop the server
echo.

flutter run -d web-server --web-port 8080 --web-hostname localhost --dart-define=FLUTTER_WEB_USE_SKIA=false

pause
