import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import '../providers/app_state.dart';
import '../screens/plant_detail_screen.dart';
import 'interactive_plant_learning.dart';

class EnhancedLearnMoreButton extends StatelessWidget {
  final Plant plant;
  final bool showQuickInfo;
  final VoidCallback? onTap;

  const EnhancedLearnMoreButton({
    super.key,
    required this.plant,
    this.showQuickInfo = false,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    if (showQuickInfo) {
      return _buildQuickInfoButton(context);
    } else {
      return _buildSimpleButton(context);
    }
  }

  Widget _buildSimpleButton(BuildContext context) {
    return TextButton.icon(
      onPressed: onTap ?? () => _navigateToDetail(context),
      style: TextButton.styleFrom(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        minimumSize: Size.zero,
        tapTargetSize: MaterialTapTargetSize.shrinkWrap,
        foregroundColor: const Color(0xFF22c55e),
      ),
      icon: const Icon(Icons.info_outline, size: 16),
      label: const Text(
        'Learn more',
        style: TextStyle(fontSize: 12, fontWeight: FontWeight.w500),
      ),
    );
  }

  Widget _buildQuickInfoButton(BuildContext context) {
    return PopupMenuButton<String>(
      onSelected: (value) {
        switch (value) {
          case 'detail':
            _navigateToDetail(context);
            break;
          case 'quick_info':
            _showQuickInfo(context);
            break;
          case 'benefits':
            _showBenefits(context);
            break;
          case 'safety':
            _showSafety(context);
            break;
          case 'interactive':
            _showInteractiveLearning(context);
            break;
        }
      },
      itemBuilder: (context) => [
        const PopupMenuItem(
          value: 'detail',
          child: Row(
            children: [
              Icon(Icons.article_outlined, size: 18),
              SizedBox(width: 8),
              Text('Full Details'),
            ],
          ),
        ),
        const PopupMenuItem(
          value: 'quick_info',
          child: Row(
            children: [
              Icon(Icons.info_outline, size: 18),
              SizedBox(width: 8),
              Text('Quick Info'),
            ],
          ),
        ),
        const PopupMenuItem(
          value: 'benefits',
          child: Row(
            children: [
              Icon(Icons.healing, size: 18),
              SizedBox(width: 8),
              Text('Benefits'),
            ],
          ),
        ),
        const PopupMenuItem(
          value: 'safety',
          child: Row(
            children: [
              Icon(Icons.warning_amber_outlined, size: 18),
              SizedBox(width: 8),
              Text('Safety Info'),
            ],
          ),
        ),
        const PopupMenuItem(
          value: 'interactive',
          child: Row(
            children: [
              Icon(Icons.school_outlined, size: 18),
              SizedBox(width: 8),
              Text('Interactive Learning'),
            ],
          ),
        ),
      ],
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        decoration: BoxDecoration(
          color: const Color(0xFF22c55e).withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: const Color(0xFF22c55e).withValues(alpha: 0.3),
          ),
        ),
        child: const Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.info_outline,
              size: 16,
              color: Color(0xFF22c55e),
            ),
            SizedBox(width: 4),
            Text(
              'Learn more',
              style: TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w500,
                color: Color(0xFF22c55e),
              ),
            ),
            SizedBox(width: 4),
            Icon(
              Icons.arrow_drop_down,
              size: 16,
              color: Color(0xFF22c55e),
            ),
          ],
        ),
      ),
    );
  }

  void _navigateToDetail(BuildContext context) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => PlantDetailScreen(plant: plant),
      ),
    );
  }

  void _showQuickInfo(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: const Color(0xFF22c55e).withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: const FaIcon(
                FontAwesomeIcons.leaf,
                color: Color(0xFF22c55e),
                size: 20,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    plant.name,
                    style: const TextStyle(fontSize: 18),
                  ),
                  Text(
                    plant.scientificName,
                    style: TextStyle(
                      fontSize: 14,
                      fontStyle: FontStyle.italic,
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'Origin: ${plant.origin}',
                style: const TextStyle(fontWeight: FontWeight.w500),
              ),
              const SizedBox(height: 8),
              Text(
                'Category: ${plant.category}',
                style: const TextStyle(fontWeight: FontWeight.w500),
              ),
              const SizedBox(height: 12),
              Text(
                plant.description,
                style: const TextStyle(fontSize: 14),
              ),
              const SizedBox(height: 12),
              const Text(
                'Primary Benefits:',
                style: TextStyle(fontWeight: FontWeight.w500),
              ),
              const SizedBox(height: 4),
              Wrap(
                spacing: 6,
                runSpacing: 6,
                children: plant.benefits.take(3).map((benefit) {
                  return Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.orange.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      benefit,
                      style: const TextStyle(
                        fontSize: 12,
                        color: Colors.orange,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  );
                }).toList(),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _navigateToDetail(context);
            },
            child: const Text('Full Details'),
          ),
        ],
      ),
    );
  }

  void _showBenefits(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            const Icon(Icons.healing, color: Color(0xFF22c55e)),
            const SizedBox(width: 8),
            Text('${plant.name} Benefits'),
          ],
        ),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              const Text(
                'Traditional and modern uses:',
                style: TextStyle(fontWeight: FontWeight.w500),
              ),
              const SizedBox(height: 12),
              ...plant.benefits.map((benefit) {
                return Padding(
                  padding: const EdgeInsets.only(bottom: 8),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Container(
                        margin: const EdgeInsets.only(top: 6),
                        width: 6,
                        height: 6,
                        decoration: const BoxDecoration(
                          color: Color(0xFF22c55e),
                          shape: BoxShape.circle,
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Text(
                          benefit,
                          style: const TextStyle(fontSize: 14),
                        ),
                      ),
                    ],
                  ),
                );
              }),
              const SizedBox(height: 12),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.blue.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Text(
                  'Note: These statements have not been evaluated by the FDA. This plant is not intended to diagnose, treat, cure, or prevent any disease.',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.blue,
                    fontStyle: FontStyle.italic,
                  ),
                ),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _navigateToDetail(context);
            },
            child: const Text('Learn More'),
          ),
        ],
      ),
    );
  }

  void _showSafety(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            const Icon(Icons.warning_amber, color: Colors.orange),
            const SizedBox(width: 8),
            Text('${plant.name} Safety'),
          ],
        ),
        content: const SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'General Safety Guidelines:',
                style: TextStyle(fontWeight: FontWeight.w500),
              ),
              SizedBox(height: 12),
              Text('• Consult healthcare provider before use'),
              SizedBox(height: 6),
              Text('• Start with small amounts to test tolerance'),
              SizedBox(height: 6),
              Text('• Avoid during pregnancy unless approved by doctor'),
              SizedBox(height: 6),
              Text('• May interact with certain medications'),
              SizedBox(height: 6),
              Text('• Discontinue if adverse reactions occur'),
              SizedBox(height: 12),
              Text(
                'Important:',
                style: TextStyle(fontWeight: FontWeight.w500, color: Colors.red),
              ),
              SizedBox(height: 6),
              Text(
                'This information is for educational purposes only. Always consult with a qualified healthcare professional before using any herbal remedy.',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.red,
                  fontStyle: FontStyle.italic,
                ),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _navigateToDetail(context);
            },
            child: const Text('Full Safety Info'),
          ),
        ],
      ),
    );
  }

  void _showInteractiveLearning(BuildContext context) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => InteractivePlantLearning(plant: plant),
    );
  }
}
