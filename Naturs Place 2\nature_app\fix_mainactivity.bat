@echo off
echo 🔧 MainActivity Error Fix Script 🔧
echo.

echo This script will fix the MainActivity not found error
echo.

echo Step 1: Cleaning all build artifacts...
flutter clean
if exist "build" rmdir /s /q "build"
if exist "android\app\build" rmdir /s /q "android\app\build"
if exist "android\.gradle" rmdir /s /q "android\.gradle"
echo ✅ Build artifacts cleaned
echo.

echo Step 2: Cleaning Gradle cache...
cd android
call gradlew clean
cd ..
echo ✅ Gradle cache cleaned
echo.

echo Step 3: Refreshing dependencies...
flutter pub get
echo ✅ Dependencies refreshed
echo.

echo Step 4: Verifying MainActivity.kt exists...
if exist "android\app\src\main\kotlin\com\naturesplace\MainActivity.kt" (
    echo ✅ MainActivity.kt found
) else (
    echo ❌ MainActivity.kt not found! Creating it...
    mkdir "android\app\src\main\kotlin\com\naturesplace" 2>nul
    echo package com.naturesplace > "android\app\src\main\kotlin\com\naturesplace\MainActivity.kt"
    echo. >> "android\app\src\main\kotlin\com\naturesplace\MainActivity.kt"
    echo import io.flutter.embedding.android.FlutterActivity >> "android\app\src\main\kotlin\com\naturesplace\MainActivity.kt"
    echo import io.flutter.embedding.engine.FlutterEngine >> "android\app\src\main\kotlin\com\naturesplace\MainActivity.kt"
    echo import io.flutter.plugins.GeneratedPluginRegistrant >> "android\app\src\main\kotlin\com\naturesplace\MainActivity.kt"
    echo. >> "android\app\src\main\kotlin\com\naturesplace\MainActivity.kt"
    echo class MainActivity : FlutterActivity^(^) { >> "android\app\src\main\kotlin\com\naturesplace\MainActivity.kt"
    echo     override fun configureFlutterEngine^(flutterEngine: FlutterEngine^) { >> "android\app\src\main\kotlin\com\naturesplace\MainActivity.kt"
    echo         super.configureFlutterEngine^(flutterEngine^) >> "android\app\src\main\kotlin\com\naturesplace\MainActivity.kt"
    echo         GeneratedPluginRegistrant.registerWith^(flutterEngine^) >> "android\app\src\main\kotlin\com\naturesplace\MainActivity.kt"
    echo     } >> "android\app\src\main\kotlin\com\naturesplace\MainActivity.kt"
    echo } >> "android\app\src\main\kotlin\com\naturesplace\MainActivity.kt"
    echo ✅ MainActivity.kt created
)
echo.

echo Step 5: Building APK to test...
flutter build apk --debug
if %errorlevel% equ 0 (
    echo ✅ Build successful! MainActivity error fixed.
) else (
    echo ❌ Build still failing. Please check the error messages above.
    echo.
    echo Additional troubleshooting:
    echo 1. Check that Android SDK is properly installed
    echo 2. Verify ANDROID_HOME environment variable is set
    echo 3. Make sure you have the correct Android SDK version
    echo 4. Try running 'flutter doctor' to check for issues
)
echo.

echo 🎉 MainActivity fix script completed!
pause
