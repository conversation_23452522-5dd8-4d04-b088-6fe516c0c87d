import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
// Conditional import for web-only functionality
import 'web_stub.dart' if (dart.library.html) 'dart:html' as html;

/// Enhanced web-specific service for security and performance
class WebEnhancementService {
  static final WebEnhancementService _instance = WebEnhancementService._internal();
  factory WebEnhancementService() => _instance;
  WebEnhancementService._internal();

  static bool _isInitialized = false;
  static Timer? _securityCheckTimer;
  static Timer? _performanceMonitorTimer;

  /// Initialize web enhancements (mobile-first approach)
  static Future<void> initialize() async {
    if (!kIsWeb || _isInitialized) return;

    try {
      // Minimal security enhancements (preserve mobile behavior)
      await _initializeSecurity();

      // Minimal performance optimizations (mobile-first)
      await _initializePerformance();

      // PWA features (mobile-like experience)
      await _initializePWA();

      // Basic monitoring (non-intrusive)
      _initializeMonitoring();

      _isInitialized = true;
      debugPrint('✅ Web enhancement service initialized (mobile-first)');
    } catch (e) {
      debugPrint('❌ Error initializing web enhancements: $e');
    }
  }

  /// Initialize security features
  static Future<void> _initializeSecurity() async {
    if (!kIsWeb) return;

    try {
      // Keep mobile-like behavior - only prevent right-click in production
      if (kReleaseMode) {
        try {
          html.document.onContextMenu.listen((event) => event.preventDefault());
        } catch (e) {
          debugPrint('Context menu prevention not available');
        }
      }

      // Preserve mobile text selection behavior (allow selection for better UX)

      // Monitor for suspicious activity
      _startSecurityMonitoring();

      // Set up session management
      await _initializeSessionSecurity();

      debugPrint('🔒 Web security features initialized');
    } catch (e) {
      debugPrint('❌ Error initializing security: $e');
    }
  }

  /// Initialize performance optimizations
  static Future<void> _initializePerformance() async {
    if (!kIsWeb) return;

    try {
      // Optimize viewport
      _optimizeViewport();

      // Preload critical resources
      await _preloadCriticalResources();

      // Set up lazy loading
      _setupLazyLoading();

      // Monitor performance
      _startPerformanceMonitoring();

      debugPrint('⚡ Web performance optimizations initialized');
    } catch (e) {
      debugPrint('❌ Error initializing performance: $e');
    }
  }

  /// Initialize PWA features
  static Future<void> _initializePWA() async {
    if (!kIsWeb) return;

    try {
      // Check if app is installed
      _checkInstallationStatus();

      // Handle app install prompt
      _setupInstallPrompt();

      // Set up offline capabilities
      await _setupOfflineSupport();

      debugPrint('📱 PWA features initialized');
    } catch (e) {
      debugPrint('❌ Error initializing PWA: $e');
    }
  }

  /// Start security monitoring
  static void _startSecurityMonitoring() {
    if (!kIsWeb) return;

    _securityCheckTimer = Timer.periodic(const Duration(minutes: 5), (timer) {
      try {
        // Check for suspicious activity
        _checkForSuspiciousActivity();

        // Validate session integrity
        _validateSession();

        // Monitor for XSS attempts
        _monitorForXSS();

      } catch (e) {
        debugPrint('❌ Security monitoring error: $e');
      }
    });
  }

  /// Initialize session security
  static Future<void> _initializeSessionSecurity() async {
    if (!kIsWeb) return;

    try {
      final prefs = await SharedPreferences.getInstance();
      
      // Generate session token
      final sessionToken = DateTime.now().millisecondsSinceEpoch.toString();
      await prefs.setString('web_session_token', sessionToken);
      
      // Set session timeout
      final sessionTimeout = DateTime.now().add(const Duration(hours: 8));
      await prefs.setString('web_session_timeout', sessionTimeout.toIso8601String());

      debugPrint('🔐 Session security initialized');
    } catch (e) {
      debugPrint('❌ Error initializing session security: $e');
    }
  }

  /// Optimize viewport for web (mobile-first approach)
  static void _optimizeViewport() {
    if (!kIsWeb) return;

    try {
      // Keep mobile-first viewport settings (same as mobile app)
      try {
        final viewport = html.document.querySelector('meta[name="viewport"]');
        if (viewport != null) {
          viewport.setAttribute('content',
            'width=device-width, initial-scale=1.0, user-scalable=yes');
        }
      } catch (e) {
        debugPrint('Viewport optimization not available');
      }

      // Minimal resize handling to preserve mobile layout
      try {
        html.window.onResize.listen((event) {
          // Keep mobile-like behavior - no special desktop adaptations
        });
      } catch (e) {
        debugPrint('Resize handling not available');
      }

    } catch (e) {
      debugPrint('❌ Error optimizing viewport: $e');
    }
  }

  /// Preload critical resources
  static Future<void> _preloadCriticalResources() async {
    if (!kIsWeb) return;

    try {
      // Preload fonts
      final fontLink = html.LinkElement()
        ..rel = 'preload'
        ..href = 'https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap'
        ..as = 'style';
      html.document.head?.append(fontLink);

      // Preload critical images
      final criticalImages = [
        'icons/Icon-192.png',
        'icons/Icon-512.png',
        'favicon.png'
      ];

      for (final imagePath in criticalImages) {
        final imageLink = html.LinkElement()
          ..rel = 'preload'
          ..href = imagePath
          ..as = 'image';
        html.document.head?.append(imageLink);
      }

      debugPrint('📦 Critical resources preloaded');
    } catch (e) {
      debugPrint('❌ Error preloading resources: $e');
    }
  }

  /// Setup lazy loading for images
  static void _setupLazyLoading() {
    if (!kIsWeb) return;

    try {
      // Use Intersection Observer for lazy loading
      if (html.window.navigator.userAgent.contains('IntersectionObserver')) {
        debugPrint('🔄 Lazy loading enabled');
      }
    } catch (e) {
      debugPrint('❌ Error setting up lazy loading: $e');
    }
  }

  /// Start performance monitoring
  static void _startPerformanceMonitoring() {
    if (!kIsWeb) return;

    _performanceMonitorTimer = Timer.periodic(const Duration(minutes: 2), (timer) {
      try {
        _monitorPerformanceMetrics();
      } catch (e) {
        debugPrint('❌ Performance monitoring error: $e');
      }
    });
  }

  /// Check installation status
  static void _checkInstallationStatus() {
    if (!kIsWeb) return;

    try {
      // Check if running as PWA
      final isStandalone = html.window.matchMedia('(display-mode: standalone)').matches;
      if (isStandalone) {
        debugPrint('📱 Running as installed PWA');
      } else {
        debugPrint('🌐 Running in browser');
      }
    } catch (e) {
      debugPrint('❌ Error checking installation status: $e');
    }
  }

  /// Setup install prompt
  static void _setupInstallPrompt() {
    if (!kIsWeb) return;

    try {
      html.window.addEventListener('beforeinstallprompt', (event) {
        event.preventDefault();
        debugPrint('📱 Install prompt available');
        // Store the event for later use
      });

      html.window.addEventListener('appinstalled', (event) {
        debugPrint('✅ App installed successfully');
      });
    } catch (e) {
      debugPrint('❌ Error setting up install prompt: $e');
    }
  }

  /// Setup offline support
  static Future<void> _setupOfflineSupport() async {
    if (!kIsWeb) return;

    try {
      // Register service worker for offline capabilities
      if (html.window.navigator.serviceWorker != null) {
        debugPrint('🔄 Service worker support available');
      }

      // Cache critical data for offline use
      await _cacheOfflineData();

    } catch (e) {
      debugPrint('❌ Error setting up offline support: $e');
    }
  }

  /// Initialize monitoring
  static void _initializeMonitoring() {
    if (!kIsWeb) return;

    try {
      // Monitor page visibility
      html.document.onVisibilityChange.listen((event) {
        if (html.document.hidden == true) {
          debugPrint('📱 App hidden - pausing non-critical operations');
        } else {
          debugPrint('📱 App visible - resuming operations');
        }
      });

      // Monitor network status
      html.window.onOnline.listen((event) {
        debugPrint('🌐 Network online');
      });

      html.window.onOffline.listen((event) {
        debugPrint('📴 Network offline');
      });

    } catch (e) {
      debugPrint('❌ Error initializing monitoring: $e');
    }
  }

  /// Check for suspicious activity
  static void _checkForSuspiciousActivity() {
    // Implementation for detecting suspicious patterns
    // This is a placeholder for actual security monitoring
  }

  /// Validate session
  static void _validateSession() {
    // Implementation for session validation
    // This is a placeholder for actual session checks
  }

  /// Monitor for XSS attempts
  static void _monitorForXSS() {
    // Implementation for XSS monitoring
    // This is a placeholder for actual XSS detection
  }



  /// Monitor performance metrics
  static void _monitorPerformanceMetrics() {
    // Implementation for performance monitoring
    // This is a placeholder for actual performance tracking
  }

  /// Cache offline data
  static Future<void> _cacheOfflineData() async {
    // Implementation for offline data caching
    // This is a placeholder for actual offline support
  }

  /// Cleanup resources
  static void dispose() {
    _securityCheckTimer?.cancel();
    _performanceMonitorTimer?.cancel();
    _isInitialized = false;
  }
}
