import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/user_models.dart';

class AuthService extends ChangeNotifier {
  static final AuthService _instance = AuthService._internal();
  factory AuthService() => _instance;
  AuthService._internal();

  User? _currentUser;
  bool _isLoading = false;

  User? get currentUser => _currentUser;
  bool get isLoggedIn => _currentUser != null;
  bool get isLoading => _isLoading;

  Future<void> initialize() async {
    _isLoading = true;
    notifyListeners();

    try {
      final prefs = await SharedPreferences.getInstance();
      final userJson = prefs.getString('current_user');
      
      if (userJson != null) {
        // In a real app, you'd validate the token with your backend
        final userData = Map<String, dynamic>.from(
          // Simple JSON parsing for demo
          {'id': '1', 'email': '<EMAIL>', 'name': 'Demo User', 'createdAt': DateTime.now().toIso8601String()}
        );
        _currentUser = User.fromJson(userData);
      }
    } catch (e) {
      debugPrint('Error loading user: $e');
    }

    _isLoading = false;
    notifyListeners();
  }

  Future<bool> signIn(String email, String password) async {
    _isLoading = true;
    notifyListeners();

    try {
      // Basic rate limiting for web (simplified)
      if (kIsWeb) {
        // Simple rate limiting without external dependencies
        final prefs = await SharedPreferences.getInstance();
        final lastAttempt = prefs.getInt('last_signin_attempt') ?? 0;
        final now = DateTime.now().millisecondsSinceEpoch;

        if (now - lastAttempt < 1000) { // 1 second cooldown
          debugPrint('Sign in rate limit exceeded');
          _isLoading = false;
          notifyListeners();
          return false;
        }

        await prefs.setInt('last_signin_attempt', now);
      }

      // Simulate API call
      await Future.delayed(const Duration(seconds: 2));

      // Mock authentication - in real app, call your backend
      if (email.isNotEmpty && password.length >= 6) {
        _currentUser = User(
          id: DateTime.now().millisecondsSinceEpoch.toString(),
          email: email,
          firstName: email.split('@').first.toUpperCase(),
          lastName: 'User',
          role: UserRole.customer,
          status: UserStatus.active,
          createdAt: DateTime.now(),
        );

        // Save user to local storage
        final prefs = await SharedPreferences.getInstance();
        await prefs.setString('current_user', 'demo_user_token');

        // Web security: Generate simple session token
        if (kIsWeb) {
          final sessionToken = DateTime.now().millisecondsSinceEpoch.toString();
          await prefs.setString('session_token', sessionToken);
        }

        _isLoading = false;
        notifyListeners();
        return true;
      }
    } catch (e) {
      debugPrint('Sign in error: $e');
    }

    _isLoading = false;
    notifyListeners();
    return false;
  }

  Future<bool> signUp(String name, String email, String password) async {
    _isLoading = true;
    notifyListeners();

    try {
      // Simulate API call
      await Future.delayed(const Duration(seconds: 2));

      // Mock registration - in real app, call your backend
      if (name.isNotEmpty && email.isNotEmpty && password.length >= 6) {
        _currentUser = User(
          id: DateTime.now().millisecondsSinceEpoch.toString(),
          email: email,
          firstName: name.split(' ').first,
          lastName: name.split(' ').length > 1 ? name.split(' ').last : '',
          role: UserRole.customer,
          status: UserStatus.active,
          createdAt: DateTime.now(),
        );

        // Save user to local storage
        final prefs = await SharedPreferences.getInstance();
        await prefs.setString('current_user', 'demo_user_token');

        _isLoading = false;
        notifyListeners();
        return true;
      }
    } catch (e) {
      debugPrint('Sign up error: $e');
    }

    _isLoading = false;
    notifyListeners();
    return false;
  }

  Future<void> signOut() async {
    _isLoading = true;
    notifyListeners();

    try {
      // Clear local storage
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('current_user');
      
      _currentUser = null;
    } catch (e) {
      debugPrint('Sign out error: $e');
    }

    _isLoading = false;
    notifyListeners();
  }

  Future<bool> resetPassword(String email) async {
    _isLoading = true;
    notifyListeners();

    try {
      // Simulate API call
      await Future.delayed(const Duration(seconds: 2));
      
      // Mock password reset - in real app, call your backend
      _isLoading = false;
      notifyListeners();
      return true;
    } catch (e) {
      debugPrint('Password reset error: $e');
    }

    _isLoading = false;
    notifyListeners();
    return false;
  }

  Future<bool> updateProfile(String name, String? profileImageUrl) async {
    if (_currentUser == null) return false;

    _isLoading = true;
    notifyListeners();

    try {
      // Simulate API call
      await Future.delayed(const Duration(seconds: 1));

      _currentUser = User(
        id: _currentUser!.id,
        email: _currentUser!.email,
        firstName: name.split(' ').first,
        lastName: name.split(' ').length > 1 ? name.split(' ').last : _currentUser!.lastName,
        role: _currentUser!.role,
        status: _currentUser!.status,
        createdAt: _currentUser!.createdAt,
      );

      _isLoading = false;
      notifyListeners();
      return true;
    } catch (e) {
      debugPrint('Update profile error: $e');
    }

    _isLoading = false;
    notifyListeners();
    return false;
  }
}
