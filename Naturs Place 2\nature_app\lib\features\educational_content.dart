import '../data/world_plant_encyclopedia.dart';

/// Educational content and learning features for the plant encyclopedia
class EducationalContentService {
  
  /// Get daily plant facts
  static List<PlantFact> getDailyFacts() {
    return [
      PlantFact(
        title: "Ancient Wisdom",
        fact: "Turmeric has been used in traditional medicine for over 4,000 years and contains curcumin, one of nature's most powerful anti-inflammatory compounds.",
        plantId: "ayur_002",
        category: "Historical",
      ),
      PlantFact(
        title: "Nutritional Powerhouse",
        fact: "Moringa leaves contain more vitamin C than oranges, more calcium than milk, and more iron than spinach.",
        plantId: "car_002",
        category: "Nutrition",
      ),
      PlantFact(
        title: "Conservation Alert",
        fact: "American Ginseng is critically endangered due to overharvesting. It takes 5-10 years for the plant to mature.",
        plantId: "nam_001",
        category: "Conservation",
      ),
      PlantFact(
        title: "Sacred Medicine",
        fact: "Frankincense was once more valuable than gold and has been used in spiritual practices for over 5,000 years.",
        plantId: "me_002",
        category: "Cultural",
      ),
      PlantFact(
        title: "Arctic Survival",
        fact: "Chaga mushroom can survive temperatures as low as -40°F and has been used by Siberian peoples for centuries.",
        plantId: "scan_001",
        category: "Survival",
      ),
    ];
  }
  
  /// Get plant preparation guides
  static List<PreparationGuide> getPreparationGuides() {
    return [
      PreparationGuide(
        title: "Making Golden Milk with Turmeric",
        plantId: "ayur_002",
        difficulty: "Easy",
        time: "10 minutes",
        ingredients: [
          "1 tsp turmeric powder",
          "1 cup warm milk (dairy or plant-based)",
          "1/4 tsp black pepper",
          "1 tsp honey",
          "1/2 tsp ginger powder",
        ],
        steps: [
          "Warm the milk in a saucepan over medium heat",
          "Add turmeric, black pepper, and ginger",
          "Whisk well to combine and avoid lumps",
          "Simmer for 3-5 minutes",
          "Remove from heat and add honey",
          "Strain if desired and serve warm",
        ],
        tips: [
          "Black pepper increases curcumin absorption by 2000%",
          "Add a pinch of cinnamon for extra flavor",
          "Best consumed in the evening for relaxation",
        ],
      ),
      PreparationGuide(
        title: "Traditional Ashwagandha Tea",
        plantId: "ayur_001",
        difficulty: "Easy",
        time: "15 minutes",
        ingredients: [
          "1 tsp ashwagandha root powder",
          "1 cup water",
          "1 tsp honey",
          "1/4 tsp ginger powder",
        ],
        steps: [
          "Boil water in a small pot",
          "Add ashwagandha powder and ginger",
          "Simmer for 10 minutes",
          "Strain the mixture",
          "Add honey to taste",
          "Drink warm, preferably before bedtime",
        ],
        tips: [
          "Start with smaller doses to assess tolerance",
          "Best taken consistently for 2-3 months",
          "Avoid during pregnancy",
        ],
      ),
      PreparationGuide(
        title: "Chaga Mushroom Decoction",
        plantId: "scan_001",
        difficulty: "Medium",
        time: "2-4 hours",
        ingredients: [
          "1 oz dried chaga chunks",
          "4 cups water",
        ],
        steps: [
          "Break chaga into small pieces",
          "Add to pot with cold water",
          "Bring to a boil, then reduce heat",
          "Simmer for 2-4 hours",
          "Strain and store in refrigerator",
          "Reheat portions as needed",
        ],
        tips: [
          "Chaga can be re-used 2-3 times",
          "Add honey or lemon for taste",
          "Store decoction for up to 1 week",
        ],
      ),
    ];
  }
  
  /// Get plant cultivation guides
  static List<CultivationGuide> getCultivationGuides() {
    return [
      CultivationGuide(
        plantName: "Holy Basil (Tulsi)",
        plantId: "ind_002",
        difficulty: "Easy",
        climate: "Warm, tropical to subtropical",
        soilType: "Well-draining, fertile soil",
        sunlight: "Full sun to partial shade",
        waterNeeds: "Moderate, avoid overwatering",
        growthTime: "60-90 days from seed",
        harvestTime: "Continuous harvest of leaves",
        steps: [
          "Start seeds indoors 6-8 weeks before last frost",
          "Transplant seedlings after soil warms",
          "Space plants 12-18 inches apart",
          "Pinch flowers to encourage leaf growth",
          "Harvest leaves regularly for best flavor",
          "Protect from frost in winter",
        ],
        tips: [
          "Grows well in containers",
          "Attracts beneficial insects",
          "Can be grown year-round indoors",
        ],
      ),
      CultivationGuide(
        plantName: "Turmeric",
        plantId: "ayur_002",
        difficulty: "Medium",
        climate: "Warm, humid tropical climate",
        soilType: "Rich, well-draining loamy soil",
        sunlight: "Partial shade to filtered sunlight",
        waterNeeds: "High humidity, regular watering",
        growthTime: "7-10 months",
        harvestTime: "When leaves turn yellow and dry",
        steps: [
          "Plant rhizomes in spring after last frost",
          "Bury rhizomes 2 inches deep",
          "Maintain consistent moisture",
          "Fertilize monthly during growing season",
          "Harvest when foliage dies back",
          "Cure rhizomes in sun for several days",
        ],
        tips: [
          "Can be grown in large containers",
          "Requires warm temperatures (68-86°F)",
          "Protect from strong winds",
        ],
      ),
    ];
  }
  
  /// Get plant conservation information
  static List<ConservationInfo> getConservationInfo() {
    return [
      ConservationInfo(
        plantName: "American Ginseng",
        plantId: "nam_001",
        status: "Critically Endangered",
        threats: [
          "Overharvesting for commercial trade",
          "Habitat destruction",
          "Climate change",
          "Illegal poaching",
        ],
        conservationEfforts: [
          "CITES protection (international trade regulation)",
          "Sustainable cultivation programs",
          "Habitat restoration projects",
          "Education and awareness campaigns",
        ],
        howToHelp: [
          "Buy only from certified sustainable sources",
          "Support conservation organizations",
          "Choose cultivated over wild-harvested",
          "Report illegal harvesting",
        ],
      ),
      ConservationInfo(
        plantName: "Frankincense",
        plantId: "me_002",
        status: "Near Threatened",
        threats: [
          "Overharvesting of resin",
          "Habitat degradation",
          "Climate change and drought",
          "Overgrazing by livestock",
        ],
        conservationEfforts: [
          "Sustainable harvesting practices",
          "Community-based conservation",
          "Research on cultivation methods",
          "International trade monitoring",
        ],
        howToHelp: [
          "Purchase from ethical suppliers",
          "Support fair trade initiatives",
          "Use frankincense mindfully",
          "Donate to conservation projects",
        ],
      ),
    ];
  }
  
  /// Get plant interaction checker
  static PlantInteractionResult checkInteractions(List<String> plantIds, List<String> medications) {
    List<String> warnings = [];
    List<String> recommendations = [];
    
    for (String plantId in plantIds) {
      final plant = WorldPlantEncyclopedia.getAllPlants()
          .firstWhere((p) => p.id == plantId);
      
      // Check for medication interactions
      for (String medication in medications) {
        for (String interaction in plant.interactions) {
          if (interaction.toLowerCase().contains(medication.toLowerCase())) {
            warnings.add("${plant.name} may interact with $medication");
          }
        }
      }
      
      // Check for plant-plant interactions
      if (plantIds.length > 1) {
        // Add logic for known plant combinations
        if (plantIds.contains("ayur_001") && plantIds.contains("tcm_001")) {
          recommendations.add("Ashwagandha and Ginseng work well together for energy and stress");
        }
      }
    }
    
    return PlantInteractionResult(
      warnings: warnings,
      recommendations: recommendations,
      safetyLevel: warnings.isEmpty ? SafetyLevel.high : SafetyLevel.medium,
    );
  }
}

/// Plant fact model
class PlantFact {
  final String title;
  final String fact;
  final String plantId;
  final String category;
  
  PlantFact({
    required this.title,
    required this.fact,
    required this.plantId,
    required this.category,
  });
}

/// Preparation guide model
class PreparationGuide {
  final String title;
  final String plantId;
  final String difficulty;
  final String time;
  final List<String> ingredients;
  final List<String> steps;
  final List<String> tips;
  
  PreparationGuide({
    required this.title,
    required this.plantId,
    required this.difficulty,
    required this.time,
    required this.ingredients,
    required this.steps,
    required this.tips,
  });
}

/// Cultivation guide model
class CultivationGuide {
  final String plantName;
  final String plantId;
  final String difficulty;
  final String climate;
  final String soilType;
  final String sunlight;
  final String waterNeeds;
  final String growthTime;
  final String harvestTime;
  final List<String> steps;
  final List<String> tips;
  
  CultivationGuide({
    required this.plantName,
    required this.plantId,
    required this.difficulty,
    required this.climate,
    required this.soilType,
    required this.sunlight,
    required this.waterNeeds,
    required this.growthTime,
    required this.harvestTime,
    required this.steps,
    required this.tips,
  });
}

/// Conservation information model
class ConservationInfo {
  final String plantName;
  final String plantId;
  final String status;
  final List<String> threats;
  final List<String> conservationEfforts;
  final List<String> howToHelp;
  
  ConservationInfo({
    required this.plantName,
    required this.plantId,
    required this.status,
    required this.threats,
    required this.conservationEfforts,
    required this.howToHelp,
  });
}

/// Plant interaction result model
class PlantInteractionResult {
  final List<String> warnings;
  final List<String> recommendations;
  final SafetyLevel safetyLevel;
  
  PlantInteractionResult({
    required this.warnings,
    required this.recommendations,
    required this.safetyLevel,
  });
}

/// Safety level enumeration
enum SafetyLevel {
  high,
  medium,
  low,
}
